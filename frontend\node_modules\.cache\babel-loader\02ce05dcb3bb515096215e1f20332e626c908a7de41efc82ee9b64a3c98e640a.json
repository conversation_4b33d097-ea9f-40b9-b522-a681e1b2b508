{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.areViewsEqual = exports.applyDefaultViewProps = void 0;\nconst areViewsEqual = (views, expectedViews) => {\n  if (views.length !== expectedViews.length) {\n    return false;\n  }\n  return expectedViews.every(expectedView => views.includes(expectedView));\n};\nexports.areViewsEqual = areViewsEqual;\nconst applyDefaultViewProps = ({\n  openTo,\n  defaultOpenTo,\n  views,\n  defaultViews\n}) => {\n  const viewsWithDefault = views ?? defaultViews;\n  let openToWithDefault;\n  if (openTo != null) {\n    openToWithDefault = openTo;\n  } else if (viewsWithDefault.includes(defaultOpenTo)) {\n    openToWithDefault = defaultOpenTo;\n  } else if (viewsWithDefault.length > 0) {\n    openToWithDefault = viewsWithDefault[0];\n  } else {\n    throw new Error('MUI X: The `views` prop must contain at least one view.');\n  }\n  return {\n    views: viewsWithDefault,\n    openTo: openToWithDefault\n  };\n};\nexports.applyDefaultViewProps = applyDefaultViewProps;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "areViewsEqual", "applyDefaultViewProps", "views", "expectedViews", "length", "every", "expected<PERSON>iew", "includes", "openTo", "defaultOpenTo", "defaultViews", "viewsWithDefault", "openToWithDefault", "Error"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/utils/views.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.areViewsEqual = exports.applyDefaultViewProps = void 0;\nconst areViewsEqual = (views, expectedViews) => {\n  if (views.length !== expectedViews.length) {\n    return false;\n  }\n  return expectedViews.every(expectedView => views.includes(expectedView));\n};\nexports.areViewsEqual = areViewsEqual;\nconst applyDefaultViewProps = ({\n  openTo,\n  defaultOpenTo,\n  views,\n  defaultViews\n}) => {\n  const viewsWithDefault = views ?? defaultViews;\n  let openToWithDefault;\n  if (openTo != null) {\n    openToWithDefault = openTo;\n  } else if (viewsWithDefault.includes(defaultOpenTo)) {\n    openToWithDefault = defaultOpenTo;\n  } else if (viewsWithDefault.length > 0) {\n    openToWithDefault = viewsWithDefault[0];\n  } else {\n    throw new Error('MUI X: The `views` prop must contain at least one view.');\n  }\n  return {\n    views: viewsWithDefault,\n    openTo: openToWithDefault\n  };\n};\nexports.applyDefaultViewProps = applyDefaultViewProps;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,aAAa,GAAGF,OAAO,CAACG,qBAAqB,GAAG,KAAK,CAAC;AAC9D,MAAMD,aAAa,GAAGA,CAACE,KAAK,EAAEC,aAAa,KAAK;EAC9C,IAAID,KAAK,CAACE,MAAM,KAAKD,aAAa,CAACC,MAAM,EAAE;IACzC,OAAO,KAAK;EACd;EACA,OAAOD,aAAa,CAACE,KAAK,CAACC,YAAY,IAAIJ,KAAK,CAACK,QAAQ,CAACD,YAAY,CAAC,CAAC;AAC1E,CAAC;AACDR,OAAO,CAACE,aAAa,GAAGA,aAAa;AACrC,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BO,MAAM;EACNC,aAAa;EACbP,KAAK;EACLQ;AACF,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAGT,KAAK,IAAIQ,YAAY;EAC9C,IAAIE,iBAAiB;EACrB,IAAIJ,MAAM,IAAI,IAAI,EAAE;IAClBI,iBAAiB,GAAGJ,MAAM;EAC5B,CAAC,MAAM,IAAIG,gBAAgB,CAACJ,QAAQ,CAACE,aAAa,CAAC,EAAE;IACnDG,iBAAiB,GAAGH,aAAa;EACnC,CAAC,MAAM,IAAIE,gBAAgB,CAACP,MAAM,GAAG,CAAC,EAAE;IACtCQ,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACzC,CAAC,MAAM;IACL,MAAM,IAAIE,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EACA,OAAO;IACLX,KAAK,EAAES,gBAAgB;IACvBH,MAAM,EAAEI;EACV,CAAC;AACH,CAAC;AACDd,OAAO,CAACG,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}