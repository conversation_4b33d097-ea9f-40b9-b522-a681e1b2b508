{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useNullablePickerContext = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _usePickerContext = require(\"../../hooks/usePickerContext\");\n/**\n * Returns the context passed by the Picker wrapping the current component.\n * If the context is not found, returns `null`.\n */\nconst useNullablePickerContext = () => React.useContext(_usePickerContext.PickerContext);\nexports.useNullablePickerContext = useNullablePickerContext;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "useNullablePickerContext", "React", "_usePickerContext", "useContext", "<PERSON>er<PERSON>ontext"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useNullablePickerContext.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useNullablePickerContext = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _usePickerContext = require(\"../../hooks/usePickerContext\");\n/**\n * Returns the context passed by the Picker wrapping the current component.\n * If the context is not found, returns `null`.\n */\nconst useNullablePickerContext = () => React.useContext(_usePickerContext.PickerContext);\nexports.useNullablePickerContext = useNullablePickerContext;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,wBAAwB,GAAG,KAAK,CAAC;AACzC,IAAIC,KAAK,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,iBAAiB,GAAGR,OAAO,CAAC,8BAA8B,CAAC;AAC/D;AACA;AACA;AACA;AACA,MAAMM,wBAAwB,GAAGA,CAAA,KAAMC,KAAK,CAACE,UAAU,CAACD,iBAAiB,CAACE,aAAa,CAAC;AACxFN,OAAO,CAACE,wBAAwB,GAAGA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}