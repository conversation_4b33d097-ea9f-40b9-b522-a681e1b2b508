import * as React from 'react';
import { MakeRequired, SlotComponentPropsFromProps } from '@mui/x-internals/types';
import { BasePickerProps } from "../../models/props/basePickerProps.js";
import { PickersModalDialogSlots, PickersModalDialogSlotProps } from "../../components/PickersModalDialog.js";
import { UsePickerParameters, UsePickerNonStaticProps, UsePickerProps } from "../usePicker/index.js";
import { PickerFieldSlotProps, PickerOwnerState } from "../../../models/index.js";
import { ExportedPickersLayoutSlots, ExportedPickersLayoutSlotProps, PickersLayoutSlotProps } from "../../../PickersLayout/PickersLayout.types.js";
import { DateOrTimeViewWithMeridiem, PickerValue } from "../../models/index.js";
import { PickerFieldUISlotsFromContext, PickerFieldUISlotPropsFromContext } from "../../components/PickerFieldUI.js";
import { PickerStep } from "../../utils/createNonRangePickerStepNavigation.js";
export interface UseMobilePickerSlots extends PickersModalDialogSlots, ExportedPickersLayoutSlots<PickerValue>, PickerFieldUISlotsFromContext {
  /**
   * Component used to enter the date with the keyboard.
   */
  field: React.ElementType;
}
export interface ExportedUseMobilePickerSlotProps<TEnableAccessibleFieldDOMStructure extends boolean> extends PickersModalDialogSlotProps, ExportedPickersLayoutSlotProps<PickerValue>, PickerFieldUISlotPropsFromContext {
  field?: SlotComponentPropsFromProps<PickerFieldSlotProps<PickerValue, TEnableAccessibleFieldDOMStructure>, {}, PickerOwnerState>;
}
export interface UseMobilePickerSlotProps<TEnableAccessibleFieldDOMStructure extends boolean> extends ExportedUseMobilePickerSlotProps<TEnableAccessibleFieldDOMStructure>, Pick<PickersLayoutSlotProps<PickerValue>, 'toolbar'> {}
export interface MobileOnlyPickerProps extends UsePickerNonStaticProps {}
export interface UseMobilePickerProps<TView extends DateOrTimeViewWithMeridiem, TEnableAccessibleFieldDOMStructure extends boolean, TError, TExternalProps extends UsePickerProps<PickerValue, TView, TError, any>> extends BasePickerProps<PickerValue, TView, TError, TExternalProps>, MakeRequired<MobileOnlyPickerProps, 'format'> {
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: UseMobilePickerSlots;
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: UseMobilePickerSlotProps<TEnableAccessibleFieldDOMStructure>;
}
export interface UseMobilePickerParams<TView extends DateOrTimeViewWithMeridiem, TEnableAccessibleFieldDOMStructure extends boolean, TExternalProps extends UseMobilePickerProps<TView, TEnableAccessibleFieldDOMStructure, any, TExternalProps>> extends Pick<UsePickerParameters<PickerValue, TView, TExternalProps>, 'valueManager' | 'valueType' | 'validator' | 'ref'> {
  props: TExternalProps;
  /**
   * Steps available for the picker.
   * This will be used to define the behavior of navigation actions.
   * If null, the picker will not have any step navigation.
   */
  steps: PickerStep[] | null;
}