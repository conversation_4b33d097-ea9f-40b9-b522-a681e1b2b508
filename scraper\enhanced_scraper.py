"""
Enhanced NSE Insider Trading Data Scraper
Features:
- Robust error handling and retry mechanisms
- Database integration with deduplication
- Incremental updates
- Comprehensive logging and monitoring
- Scheduled execution
"""

import pandas as pd
import requests
import json
import time
import random
import logging
import numpy as np
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, List, Tuple
from urllib.parse import urlencode
import asyncio
from dataclasses import dataclass
import traceback
from pathlib import Path

# Import our database components
import sys
sys.path.append(str(Path(__file__).parent.parent))
from database.connection import db_manager, data_access

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def safe_json_serializer(obj):
    """Custom JSON serializer that handles NaN, infinity, and pandas types"""
    if pd.isna(obj):
        return None
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        if np.isnan(obj) or np.isinf(obj):
            return None
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, pd.Timestamp):
        return obj.isoformat()
    elif hasattr(obj, '__class__') and 'NAType' in str(obj.__class__):
        return None
    else:
        return str(obj)

def sanitize_dict_for_json(data_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Sanitize dictionary values to be JSON-serializable, handling NaN values"""
    sanitized = {}
    nan_fields_found = []

    for key, value in data_dict.items():
        if value is None:
            sanitized[key] = None
        elif pd.isna(value):
            sanitized[key] = None
            nan_fields_found.append(key)
        elif isinstance(value, (np.integer, np.int64, np.int32)):
            sanitized[key] = int(value)
        elif isinstance(value, (np.floating, np.float64, np.float32)):
            if np.isnan(value) or np.isinf(value):
                sanitized[key] = None
                nan_fields_found.append(key)
            else:
                sanitized[key] = float(value)
        elif isinstance(value, float):
            if np.isnan(value) or np.isinf(value):
                sanitized[key] = None
                nan_fields_found.append(key)
            else:
                sanitized[key] = value
        elif isinstance(value, pd.Timestamp):
            sanitized[key] = value.isoformat() if not pd.isna(value) else None
        elif hasattr(value, '__class__') and 'NAType' in str(value.__class__):
            sanitized[key] = None
            nan_fields_found.append(key)
        elif str(value).strip() in ['nan', 'NaN', 'None', '<NA>', '']:
            sanitized[key] = None
            nan_fields_found.append(key)
        else:
            sanitized[key] = value

    if nan_fields_found:
        logger.debug(f"Sanitized NaN values in fields: {nan_fields_found}")

    return sanitized

@dataclass
class ScrapingResult:
    """Result of a scraping operation"""
    success: bool
    records_fetched: int = 0
    records_inserted: int = 0
    records_updated: int = 0
    records_skipped: int = 0
    error_message: Optional[str] = None
    execution_time: float = 0.0
    details: Dict[str, Any] = None

class EnhancedNSEScraper:
    """Enhanced NSE Insider Trading Data Scraper"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.nseindia.com"
        self.api_url = f"{self.base_url}/api/corporates-pit"
        self.max_retries = 3
        self.retry_delay = 5  # seconds
        self.request_timeout = 30
        self.setup_session()
        
        # Ensure logs directory exists
        Path("logs").mkdir(exist_ok=True)
    
    def setup_session(self):
        """Setup session with realistic headers and user agent rotation"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://www.nseindia.com/companies-listing/corporate-filings-insider-trading'
        }
        
        self.session.headers.update(headers)
        logger.info("Session configured with headers and user agent rotation")
    
    def get_cookies_with_retry(self) -> bool:
        """Get necessary cookies with retry mechanism"""
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Getting session cookies (attempt {attempt + 1}/{self.max_retries})")
                
                # Visit homepage first
                response = self.session.get(self.base_url, timeout=self.request_timeout)
                if response.status_code != 200:
                    raise requests.RequestException(f"Homepage returned status {response.status_code}")
                
                # Random delay
                time.sleep(random.uniform(1, 3))
                
                # Visit insider trading page
                insider_url = f"{self.base_url}/companies-listing/corporate-filings-insider-trading"
                response = self.session.get(insider_url, timeout=self.request_timeout)
                if response.status_code != 200:
                    raise requests.RequestException(f"Insider trading page returned status {response.status_code}")
                
                time.sleep(random.uniform(1, 2))
                logger.info("Successfully obtained session cookies")
                return True
                
            except Exception as e:
                logger.warning(f"Cookie attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    delay = self.retry_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                else:
                    logger.error("Failed to get cookies after all retries")
                    return False
        
        return False
    
    def fetch_data_with_retry(self, from_date: str, to_date: str) -> Optional[pd.DataFrame]:
        """Fetch data with retry mechanism and error handling"""
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Fetching data from {from_date} to {to_date} (attempt {attempt + 1}/{self.max_retries})")
                
                params = {
                    'index': 'equities',
                    'from_date': from_date,
                    'to_date': to_date
                }
                
                response = self.session.get(
                    self.api_url, 
                    params=params, 
                    timeout=self.request_timeout
                )
                
                if response.status_code != 200:
                    raise requests.RequestException(f"API returned status {response.status_code}")
                
                # Handle response content
                content_encoding = response.headers.get('Content-Encoding', '').lower()
                
                if 'br' in content_encoding:
                    try:
                        import brotli
                        response_content = brotli.decompress(response.content).decode('utf-8')
                    except ImportError:
                        raise Exception("Brotli library not installed. Install with: pip install brotli")
                else:
                    response_content = response.text
                
                # Parse JSON
                try:
                    data = json.loads(response_content)
                except json.JSONDecodeError as e:
                    raise Exception(f"Failed to parse JSON response: {e}")
                
                # Validate data structure
                if not isinstance(data, dict) or 'data' not in data:
                    raise Exception("Invalid response structure")
                
                if not data['data']:
                    logger.info("No data found for the specified date range")
                    return pd.DataFrame()
                
                # Convert to DataFrame
                df = pd.DataFrame(data['data'])
                logger.info(f"Successfully fetched {len(df)} records")
                return df
                
            except Exception as e:
                logger.warning(f"Data fetch attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    delay = self.retry_delay * (2 ** attempt)
                    logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                else:
                    logger.error(f"Failed to fetch data after all retries: {e}")
                    return None
        
        return None
    
    def clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate the scraped data"""
        if df is None or df.empty:
            return df
        
        logger.info("Cleaning and validating data...")
        df_clean = df.copy()
        
        # Convert date columns
        date_columns = ['date', 'acqfromDt', 'acqtoDt', 'intimDt']
        for col in date_columns:
            if col in df_clean.columns:
                df_clean[col] = pd.to_datetime(
                    df_clean[col], 
                    format='%d-%b-%Y %H:%M', 
                    errors='coerce'
                )
        
        # Convert numeric columns with enhanced NaN tracking
        numeric_columns = [
            'buyValue', 'sellValue', 'buyQuantity', 'sellquantity',
            'secAcq', 'befAcqSharesNo', 'befAcqSharesPer', 'secVal',
            'afterAcqSharesNo', 'afterAcqSharesPer'
        ]

        nan_counts = {}
        for col in numeric_columns:
            if col in df_clean.columns:
                # Track original NaN count
                original_nan_count = df_clean[col].isna().sum()

                # Convert to numeric
                df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')

                # Track new NaN count after conversion
                new_nan_count = df_clean[col].isna().sum()
                conversion_nans = new_nan_count - original_nan_count

                if conversion_nans > 0:
                    nan_counts[col] = conversion_nans
                    logger.info(f"Column '{col}': {conversion_nans} values converted to NaN during numeric conversion")

        if nan_counts:
            logger.info(f"Total NaN values created during numeric conversion: {nan_counts}")

        # Replace None, '-', and empty strings with NaN
        df_clean = df_clean.replace([None, '-', '', 'nan', 'NaN', 'null', 'NULL'], pd.NA)

        # Additional NaN handling for problematic fields
        problematic_fields = ['befAcqSharesNo', 'afterAcqSharesNo', 'befAcqSharesPer', 'afterAcqSharesPer']
        for field in problematic_fields:
            if field in df_clean.columns:
                # Count NaN values in problematic fields
                nan_count = df_clean[field].isna().sum()
                if nan_count > 0:
                    logger.info(f"Field '{field}' contains {nan_count} NaN values that will be converted to NULL in database")
        
        # Data validation
        initial_count = len(df_clean)
        
        # Remove records without essential data
        df_clean = df_clean.dropna(subset=['symbol', 'company', 'acqName'])
        
        validation_dropped = initial_count - len(df_clean)
        if validation_dropped > 0:
            logger.warning(f"Dropped {validation_dropped} records due to missing essential data")
        
        logger.info(f"Data cleaning completed. {len(df_clean)} records ready for processing")
        return df_clean
    
    def safe_convert_value(self, value, target_type=None, field_name=None):
        """Safely convert pandas values to database-compatible types with enhanced NaN handling"""
        # Handle pandas NA types and None values
        if value is None:
            return None

        # Check for various NaN representations
        if pd.isna(value):
            if field_name:
                logger.debug(f"Converting NaN to None for field '{field_name}'")
            return None

        # Handle numpy NaN and infinity
        if isinstance(value, (np.floating, np.float64, np.float32)):
            if np.isnan(value) or np.isinf(value):
                if field_name:
                    logger.debug(f"Converting numpy NaN/Inf to None for field '{field_name}': {value}")
                return None

        # Handle pandas NA types
        if hasattr(value, '__class__') and 'NAType' in str(value.__class__):
            if field_name:
                logger.debug(f"Converting pandas NAType to None for field '{field_name}'")
            return None

        # Handle string representations of NaN
        str_value = str(value).strip()
        if str_value.lower() in ['', 'nan', 'none', '<na>', 'null', 'n/a']:
            if field_name:
                logger.debug(f"Converting string NaN representation to None for field '{field_name}': '{str_value}'")
            return None

        # Type-specific conversions
        if target_type == 'str':
            return str_value if str_value else None
        elif target_type == 'int':
            try:
                # Handle numpy integers
                if isinstance(value, (np.integer, np.int64, np.int32)):
                    return int(value)
                return int(float(value))
            except (ValueError, TypeError, OverflowError):
                if field_name:
                    logger.debug(f"Failed to convert to int for field '{field_name}': {value}")
                return None
        elif target_type == 'float':
            try:
                # Handle numpy floats
                if isinstance(value, (np.floating, np.float64, np.float32)):
                    if np.isnan(value) or np.isinf(value):
                        return None
                    return float(value)
                result = float(value)
                if np.isnan(result) or np.isinf(result):
                    if field_name:
                        logger.debug(f"Float conversion resulted in NaN/Inf for field '{field_name}': {value}")
                    return None
                return result
            except (ValueError, TypeError, OverflowError):
                if field_name:
                    logger.debug(f"Failed to convert to float for field '{field_name}': {value}")
                return None
        elif target_type == 'datetime':
            if pd.isna(value):
                return None
            return value if isinstance(value, datetime) else None
        else:
            return value if not pd.isna(value) else None

    def create_safe_raw_data(self, row: pd.Series) -> str:
        """Create JSON-safe raw data from pandas Series, handling NaN values"""
        try:
            # Convert pandas Series to dictionary
            raw_dict = row.to_dict()

            # Sanitize the dictionary to handle NaN values
            sanitized_dict = sanitize_dict_for_json(raw_dict)

            # Convert to JSON with custom serializer as fallback
            return json.dumps(sanitized_dict, default=safe_json_serializer, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Failed to create safe raw data: {e}")
            # Fallback: create a minimal safe JSON
            return json.dumps({
                'error': 'Failed to serialize raw data',
                'symbol': self.safe_convert_value(row.get('symbol'), 'str'),
                'company': self.safe_convert_value(row.get('company'), 'str'),
                'person': self.safe_convert_value(row.get('acqName'), 'str')
            })

    def map_to_database_format(self, row: pd.Series) -> Dict[str, Any]:
        """Map scraped data row to database format with enhanced NaN handling"""
        try:
            # Get or create company
            symbol = self.safe_convert_value(row.get('symbol', ''), 'str', 'symbol')
            company_name = self.safe_convert_value(row.get('company', ''), 'str', 'company')

            if not symbol or not company_name:
                raise ValueError("Missing required symbol or company name")

            company_id = data_access.get_or_create_company(symbol, company_name)

            # Get lookup IDs with safe conversion
            person_category = self.safe_convert_value(row.get('personCategory', ''), 'str', 'personCategory')
            person_category_id = data_access.get_lookup_id(
                'person_categories', 'category_name', person_category
            ) if person_category else None

            transaction_type = self.safe_convert_value(row.get('tdpTransactionType', ''), 'str', 'tdpTransactionType')
            transaction_type_id = data_access.get_lookup_id(
                'transaction_types', 'type_name', transaction_type
            ) if transaction_type else None

            transaction_mode = self.safe_convert_value(row.get('acqMode', ''), 'str', 'acqMode')
            transaction_mode_id = data_access.get_lookup_id(
                'transaction_modes', 'mode_name', transaction_mode
            ) if transaction_mode else None

            security_type = self.safe_convert_value(row.get('secType', ''), 'str', 'secType')
            security_type_id = data_access.get_lookup_id(
                'security_types', 'type_name', security_type
            ) if security_type else None

            exchange = self.safe_convert_value(row.get('exchange', 'NA'), 'str', 'exchange')
            exchange_id = data_access.get_lookup_id(
                'exchanges', 'exchange_name', exchange or 'NA'
            )

            # Map the data with safe conversion and field name tracking
            transaction_data = {
                'company_id': company_id,
                'person_name': self.safe_convert_value(row.get('acqName', ''), 'str', 'acqName'),
                'person_category_id': person_category_id,
                'transaction_date': self.safe_convert_value(row.get('date'), 'datetime', 'date'),
                'intimation_date': self.safe_convert_value(row.get('intimDt'), 'datetime', 'intimDt'),
                'acquisition_from_date': self.safe_convert_value(row.get('acqfromDt'), 'datetime', 'acqfromDt'),
                'acquisition_to_date': self.safe_convert_value(row.get('acqtoDt'), 'datetime', 'acqtoDt'),
                'transaction_type_id': transaction_type_id,
                'transaction_mode_id': transaction_mode_id,
                'security_type_id': security_type_id,
                'exchange_id': exchange_id,
                'buy_value': self.safe_convert_value(row.get('buyValue'), 'float', 'buyValue'),
                'sell_value': self.safe_convert_value(row.get('sellValue'), 'float', 'sellValue'),
                'buy_quantity': self.safe_convert_value(row.get('buyQuantity'), 'int', 'buyQuantity'),
                'sell_quantity': self.safe_convert_value(row.get('sellquantity'), 'int', 'sellquantity'),
                'security_value': self.safe_convert_value(row.get('secVal'), 'float', 'secVal'),
                'securities_acquired': self.safe_convert_value(row.get('secAcq'), 'int', 'secAcq'),
                'shares_before_transaction': self.safe_convert_value(row.get('befAcqSharesNo'), 'int', 'befAcqSharesNo'),
                'percentage_before_transaction': self.safe_convert_value(row.get('befAcqSharesPer'), 'float', 'befAcqSharesPer'),
                'shares_after_transaction': self.safe_convert_value(row.get('afterAcqSharesNo'), 'int', 'afterAcqSharesNo'),
                'percentage_after_transaction': self.safe_convert_value(row.get('afterAcqSharesPer'), 'float', 'afterAcqSharesPer'),
                'derivative_contract_type': self.safe_convert_value(row.get('tdpDerivativeContractType'), 'str', 'tdpDerivativeContractType'),
                'remarks': self.safe_convert_value(row.get('remarks'), 'str', 'remarks'),
                'xbrl_link': self.safe_convert_value(row.get('xbrl'), 'str', 'xbrl'),
                'nse_pid': self.safe_convert_value(row.get('pid', ''), 'str', 'pid'),
                'nse_did': self.safe_convert_value(row.get('did', ''), 'str', 'did'),
                'nse_anex': self.safe_convert_value(row.get('anex', ''), 'str', 'anex'),
                'raw_data': self.create_safe_raw_data(row),
                'data_source': 'NSE_API'
            }

            # Validate required fields
            if not transaction_data['person_name']:
                raise ValueError("Missing required person name")
            
            return transaction_data
            
        except Exception as e:
            logger.error(f"Failed to map row to database format: {e}")
            logger.debug(f"Row data: {row.to_dict()}")
            raise
    
    def save_to_database(self, df: pd.DataFrame) -> Tuple[int, int, int]:
        """Save data to database with deduplication and enhanced error handling"""
        if df is None or df.empty:
            return 0, 0, 0

        inserted_count = 0
        updated_count = 0
        skipped_count = 0
        json_errors = 0

        logger.info(f"Saving {len(df)} records to database...")

        for index, row in df.iterrows():
            try:
                transaction_data = self.map_to_database_format(row)
                result = data_access.insert_transaction(transaction_data)

                if result:
                    inserted_count += 1
                    logger.debug(f"Successfully inserted record {index}")
                else:
                    skipped_count += 1  # Duplicate
                    logger.debug(f"Skipped duplicate record {index}")

            except json.JSONDecodeError as e:
                logger.error(f"JSON serialization error for record {index}: {e}")
                logger.debug(f"Problematic row data: {row.to_dict()}")
                json_errors += 1
                skipped_count += 1
            except Exception as e:
                error_msg = str(e)
                if 'json' in error_msg.lower() or 'nan' in error_msg.lower():
                    logger.error(f"JSON/NaN related error for record {index}: {e}")
                    logger.debug(f"Row data types: {row.dtypes}")
                    logger.debug(f"Row values: {row.to_dict()}")
                    json_errors += 1
                else:
                    logger.error(f"Failed to save record {index}: {e}")
                skipped_count += 1

        if json_errors > 0:
            logger.warning(f"Encountered {json_errors} JSON/NaN serialization errors during database save")

        logger.info(f"Database save completed: {inserted_count} inserted, {updated_count} updated, {skipped_count} skipped")
        return inserted_count, updated_count, skipped_count

    def scrape_data(self, days_back: int = 7) -> ScrapingResult:
        """Main scraping method with comprehensive error handling"""
        start_time = time.time()
        execution_start = datetime.now(timezone.utc)

        try:
            # Calculate date range
            to_date = datetime.today()
            from_date = to_date - timedelta(days=days_back)

            to_date_str = to_date.strftime('%d-%m-%Y')
            from_date_str = from_date.strftime('%d-%m-%Y')

            logger.info(f"Starting scrape for date range: {from_date_str} to {to_date_str}")

            # Get cookies
            if not self.get_cookies_with_retry():
                return ScrapingResult(
                    success=False,
                    error_message="Failed to obtain session cookies",
                    execution_time=time.time() - start_time
                )

            # Add delay before API call
            time.sleep(random.uniform(2, 4))

            # Fetch data
            df = self.fetch_data_with_retry(from_date_str, to_date_str)
            if df is None:
                return ScrapingResult(
                    success=False,
                    error_message="Failed to fetch data from NSE API",
                    execution_time=time.time() - start_time
                )

            records_fetched = len(df)

            # Clean and validate data
            df_clean = self.clean_and_validate_data(df)

            # Save to database
            inserted, updated, skipped = self.save_to_database(df_clean)

            execution_time = time.time() - start_time

            # Log execution details
            execution_data = {
                'execution_start': execution_start,
                'execution_end': datetime.now(timezone.utc),
                'status': 'SUCCESS',
                'records_fetched': records_fetched,
                'records_inserted': inserted,
                'records_updated': updated,
                'records_skipped': skipped,
                'error_message': None,
                'execution_details': json.dumps({
                    'date_range': f"{from_date_str} to {to_date_str}",
                    'execution_time_seconds': execution_time,
                    'days_back': days_back
                })
            }

            data_access.log_scraper_execution(execution_data)

            result = ScrapingResult(
                success=True,
                records_fetched=records_fetched,
                records_inserted=inserted,
                records_updated=updated,
                records_skipped=skipped,
                execution_time=execution_time,
                details={
                    'date_range': f"{from_date_str} to {to_date_str}",
                    'days_back': days_back
                }
            )

            logger.info(f"Scraping completed successfully in {execution_time:.2f} seconds")
            return result

        except Exception as e:
            execution_time = time.time() - start_time
            error_message = f"Scraping failed: {str(e)}"
            logger.error(error_message)
            logger.debug(traceback.format_exc())

            # Log failed execution
            execution_data = {
                'execution_start': execution_start,
                'execution_end': datetime.now(timezone.utc),
                'status': 'FAILED',
                'records_fetched': 0,
                'records_inserted': 0,
                'records_updated': 0,
                'records_skipped': 0,
                'error_message': error_message,
                'execution_details': json.dumps({
                    'days_back': days_back,
                    'execution_time_seconds': execution_time,
                    'traceback': traceback.format_exc()
                })
            }

            try:
                data_access.log_scraper_execution(execution_data)
            except Exception as log_error:
                logger.error(f"Failed to log execution: {log_error}")

            return ScrapingResult(
                success=False,
                error_message=error_message,
                execution_time=execution_time
            )

    def incremental_scrape(self) -> ScrapingResult:
        """Perform incremental scrape based on last successful execution"""
        try:
            # Get last successful execution time
            conn = db_manager.get_sync_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT execution_start
                        FROM scraper_logs
                        WHERE status = 'SUCCESS'
                        ORDER BY execution_start DESC
                        LIMIT 1
                    """)
                    result = cursor.fetchone()

                    if result:
                        last_execution = result[0]
                        days_back = max(1, (datetime.now(timezone.utc) - last_execution).days + 1)
                        logger.info(f"Performing incremental scrape for last {days_back} days")
                    else:
                        days_back = 30  # Default for first run
                        logger.info("No previous successful execution found, scraping last 30 days")

            finally:
                db_manager.return_sync_connection(conn)

            return self.scrape_data(days_back=days_back)

        except Exception as e:
            logger.error(f"Incremental scrape failed: {e}")
            # Fallback to default scrape
            return self.scrape_data(days_back=7)

def main():
    """Main execution function for testing"""
    scraper = EnhancedNSEScraper()

    # Test scrape
    result = scraper.scrape_data(days_back=7)

    if result.success:
        print(f"✅ Scraping successful!")
        print(f"   Records fetched: {result.records_fetched}")
        print(f"   Records inserted: {result.records_inserted}")
        print(f"   Records skipped: {result.records_skipped}")
        print(f"   Execution time: {result.execution_time:.2f} seconds")
    else:
        print(f"❌ Scraping failed: {result.error_message}")

if __name__ == "__main__":
    main()
