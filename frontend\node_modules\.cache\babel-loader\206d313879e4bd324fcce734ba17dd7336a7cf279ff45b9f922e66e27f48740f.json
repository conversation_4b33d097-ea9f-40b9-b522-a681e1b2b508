{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  tabScrollButtonClasses: true\n};\nObject.defineProperty(exports, \"default\", {\n  enumerable: true,\n  get: function () {\n    return _TabScrollButton.default;\n  }\n});\nObject.defineProperty(exports, \"tabScrollButtonClasses\", {\n  enumerable: true,\n  get: function () {\n    return _tabScrollButtonClasses.default;\n  }\n});\nvar _TabScrollButton = _interopRequireDefault(require(\"./TabScrollButton\"));\nvar _tabScrollButtonClasses = _interopRequireWildcard(require(\"./tabScrollButtonClasses\"));\nObject.keys(_tabScrollButtonClasses).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _tabScrollButtonClasses[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _tabScrollButtonClasses[key];\n    }\n  });\n});", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_exportNames", "tabScrollButtonClasses", "enumerable", "get", "_TabScrollButton", "_tabScrollButtonClasses", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/TabScrollButton/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  tabScrollButtonClasses: true\n};\nObject.defineProperty(exports, \"default\", {\n  enumerable: true,\n  get: function () {\n    return _TabScrollButton.default;\n  }\n});\nObject.defineProperty(exports, \"tabScrollButtonClasses\", {\n  enumerable: true,\n  get: function () {\n    return _tabScrollButtonClasses.default;\n  }\n});\nvar _TabScrollButton = _interopRequireDefault(require(\"./TabScrollButton\"));\nvar _tabScrollButtonClasses = _interopRequireWildcard(require(\"./tabScrollButtonClasses\"));\nObject.keys(_tabScrollButtonClasses).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _tabScrollButtonClasses[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _tabScrollButtonClasses[key];\n    }\n  });\n});"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,IAAIC,YAAY,GAAG;EACjBC,sBAAsB,EAAE;AAC1B,CAAC;AACDL,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,SAAS,EAAE;EACxCI,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOC,gBAAgB,CAACV,OAAO;EACjC;AACF,CAAC,CAAC;AACFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,wBAAwB,EAAE;EACvDI,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOE,uBAAuB,CAACX,OAAO;EACxC;AACF,CAAC,CAAC;AACF,IAAIU,gBAAgB,GAAGT,sBAAsB,CAACF,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC3E,IAAIY,uBAAuB,GAAGb,uBAAuB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAC1FG,MAAM,CAACU,IAAI,CAACD,uBAAuB,CAAC,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;EAC1D,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,YAAY,EAAE;EAC/C,IAAIZ,MAAM,CAACa,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,YAAY,EAAEQ,GAAG,CAAC,EAAE;EAC7D,IAAIA,GAAG,IAAIV,OAAO,IAAIA,OAAO,CAACU,GAAG,CAAC,KAAKH,uBAAuB,CAACG,GAAG,CAAC,EAAE;EACrEZ,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEU,GAAG,EAAE;IAClCN,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACf,OAAOE,uBAAuB,CAACG,GAAG,CAAC;IACrC;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}