{"ast": null, "code": "\"use strict\";\n'use client';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.usePickerTranslations = void 0;\nvar _useUtils = require(\"../internals/hooks/useUtils\");\nconst usePickerTranslations = () => (0, _useUtils.useLocalizationContext)().localeText;\nexports.usePickerTranslations = usePickerTranslations;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "usePickerTranslations", "_useUtils", "require", "useLocalizationContext", "localeText"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/hooks/usePickerTranslations.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.usePickerTranslations = void 0;\nvar _useUtils = require(\"../internals/hooks/useUtils\");\nconst usePickerTranslations = () => (0, _useUtils.useLocalizationContext)().localeText;\nexports.usePickerTranslations = usePickerTranslations;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,qBAAqB,GAAG,KAAK,CAAC;AACtC,IAAIC,SAAS,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AACtD,MAAMF,qBAAqB,GAAGA,CAAA,KAAM,CAAC,CAAC,EAAEC,SAAS,CAACE,sBAAsB,EAAE,CAAC,CAACC,UAAU;AACtFN,OAAO,CAACE,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}