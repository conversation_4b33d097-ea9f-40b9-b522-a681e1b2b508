{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createNonRangePickerStepNavigation = createNonRangePickerStepNavigation;\nvar _createStepNavigation = require(\"./createStepNavigation\");\nfunction createNonRangePickerStepNavigation(parameters) {\n  const {\n    steps\n  } = parameters;\n  return (0, _createStepNavigation.createStepNavigation)({\n    steps,\n    isViewMatchingStep: (view, step) => {\n      return step.views == null || step.views.includes(view);\n    },\n    onStepChange: ({\n      step,\n      defaultView,\n      setView,\n      view,\n      views\n    }) => {\n      const targetView = step.views == null ? defaultView : step.views.find(viewBis => views.includes(viewBis));\n      if (targetView !== view) {\n        setView(targetView);\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createNonRangePickerStepNavigation", "_createStepNavigation", "require", "parameters", "steps", "createStepNavigation", "isViewMatchingStep", "view", "step", "views", "includes", "onStepChange", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "targetView", "find", "viewBis"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/utils/createNonRangePickerStepNavigation.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createNonRangePickerStepNavigation = createNonRangePickerStepNavigation;\nvar _createStepNavigation = require(\"./createStepNavigation\");\nfunction createNonRangePickerStepNavigation(parameters) {\n  const {\n    steps\n  } = parameters;\n  return (0, _createStepNavigation.createStepNavigation)({\n    steps,\n    isViewMatchingStep: (view, step) => {\n      return step.views == null || step.views.includes(view);\n    },\n    onStepChange: ({\n      step,\n      defaultView,\n      setView,\n      view,\n      views\n    }) => {\n      const targetView = step.views == null ? defaultView : step.views.find(viewBis => views.includes(viewBis));\n      if (targetView !== view) {\n        setView(targetView);\n      }\n    }\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,kCAAkC,GAAGA,kCAAkC;AAC/E,IAAIC,qBAAqB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC7D,SAASF,kCAAkCA,CAACG,UAAU,EAAE;EACtD,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,OAAO,CAAC,CAAC,EAAEF,qBAAqB,CAACI,oBAAoB,EAAE;IACrDD,KAAK;IACLE,kBAAkB,EAAEA,CAACC,IAAI,EAAEC,IAAI,KAAK;MAClC,OAAOA,IAAI,CAACC,KAAK,IAAI,IAAI,IAAID,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACH,IAAI,CAAC;IACxD,CAAC;IACDI,YAAY,EAAEA,CAAC;MACbH,IAAI;MACJI,WAAW;MACXC,OAAO;MACPN,IAAI;MACJE;IACF,CAAC,KAAK;MACJ,MAAMK,UAAU,GAAGN,IAAI,CAACC,KAAK,IAAI,IAAI,GAAGG,WAAW,GAAGJ,IAAI,CAACC,KAAK,CAACM,IAAI,CAACC,OAAO,IAAIP,KAAK,CAACC,QAAQ,CAACM,OAAO,CAAC,CAAC;MACzG,IAAIF,UAAU,KAAKP,IAAI,EAAE;QACvBM,OAAO,CAACC,UAAU,CAAC;MACrB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}