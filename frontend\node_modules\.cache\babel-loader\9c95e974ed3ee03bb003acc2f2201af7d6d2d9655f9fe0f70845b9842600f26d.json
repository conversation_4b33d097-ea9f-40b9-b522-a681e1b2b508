{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _useMediaQuery = require(\"@mui/system/useMediaQuery\");\nvar _identifier = _interopRequireDefault(require(\"../styles/identifier\"));\nconst useMediaQuery = (0, _useMediaQuery.unstable_createUseMediaQuery)({\n  themeId: _identifier.default\n});\nvar _default = exports.default = useMediaQuery;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "_useMediaQuery", "_identifier", "useMediaQuery", "unstable_createUseMediaQuery", "themeId", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/useMediaQuery/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _useMediaQuery = require(\"@mui/system/useMediaQuery\");\nvar _identifier = _interopRequireDefault(require(\"../styles/identifier\"));\nconst useMediaQuery = (0, _useMediaQuery.unstable_createUseMediaQuery)({\n  themeId: _identifier.default\n});\nvar _default = exports.default = useMediaQuery;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIK,cAAc,GAAGN,OAAO,CAAC,2BAA2B,CAAC;AACzD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACzE,MAAMQ,aAAa,GAAG,CAAC,CAAC,EAAEF,cAAc,CAACG,4BAA4B,EAAE;EACrEC,OAAO,EAAEH,WAAW,CAACN;AACvB,CAAC,CAAC;AACF,IAAIU,QAAQ,GAAGP,OAAO,CAACH,OAAO,GAAGO,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}