import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';
import { Box, Typography, useTheme, CircularProgress } from '@mui/material';
import { format, subDays, startOfDay } from 'date-fns';
import { apiService } from '../services/apiService';

interface TrendData {
  date: string;
  buy_value: number;
  sell_value: number;
  transaction_count: number;
  net_value: number;
}

const TransactionTrendsChart: React.FC = () => {
  const theme = useTheme();
  const [data, setData] = useState<TrendData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTrendData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get data for last 30 days
        const endDate = new Date();
        const startDate = subDays(endDate, 30);

        // For now, we'll simulate trend data since we don't have a specific trends endpoint
        // In a real implementation, you'd call a dedicated trends API
        const response = await apiService.getTransactions({
          from_date: startDate.toISOString().split('T')[0],
          to_date: endDate.toISOString().split('T')[0],
          limit: 1000, // Get more data for aggregation
        });

        // Aggregate data by date
        const aggregatedData: { [key: string]: TrendData } = {};
        
        response.data.transactions.forEach((transaction) => {
          const date = format(new Date(transaction.transaction_date), 'yyyy-MM-dd');
          
          if (!aggregatedData[date]) {
            aggregatedData[date] = {
              date,
              buy_value: 0,
              sell_value: 0,
              transaction_count: 0,
              net_value: 0,
            };
          }

          aggregatedData[date].transaction_count += 1;
          
          if (transaction.buy_value) {
            aggregatedData[date].buy_value += transaction.buy_value;
          }
          
          if (transaction.sell_value) {
            aggregatedData[date].sell_value += transaction.sell_value;
          }
        });

        // Calculate net values and convert to array
        const trendData = Object.values(aggregatedData)
          .map((item) => ({
            ...item,
            net_value: item.buy_value - item.sell_value,
            displayDate: format(new Date(item.date), 'MMM dd'),
          }))
          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
          .slice(-30); // Last 30 days

        setData(trendData);
      } catch (err) {
        console.error('Error fetching trend data:', err);
        setError('Failed to load trend data');
      } finally {
        setLoading(false);
      }
    };

    fetchTrendData();
  }, []);

  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(value)) {
      return '₹0';
    }

    const absValue = Math.abs(value);
    if (absValue >= 10000000) {
      return `₹${(value / 10000000).toFixed(1)}Cr`;
    } else if (absValue >= 100000) {
      return `₹${(value / 100000).toFixed(1)}L`;
    } else {
      return `₹${(value / 1000).toFixed(1)}K`;
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          sx={{
            backgroundColor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            p: 2,
            boxShadow: theme.shadows[4],
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {format(new Date(data.date), 'MMM dd, yyyy')}
          </Typography>
          <Typography variant="body2" color="success.main">
            Buy Value: {formatCurrency(data.buy_value)}
          </Typography>
          <Typography variant="body2" color="error.main">
            Sell Value: {formatCurrency(data.sell_value)}
          </Typography>
          <Typography variant="body2" color="primary.main" fontWeight={600}>
            Net Value: {formatCurrency(data.net_value)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Transactions: {data.transaction_count}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <Typography variant="body1" color="error">
          {error}
        </Typography>
      </Box>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <Typography variant="body1" color="text.secondary">
          No trend data available
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: 400 }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey="displayDate"
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
            interval="preserveStartEnd"
          />
          <YAxis
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
            tickFormatter={(value) => {
              if (Math.abs(value) >= 10000000) {
                return `₹${(value / 10000000).toFixed(0)}Cr`;
              } else if (Math.abs(value) >= 100000) {
                return `₹${(value / 100000).toFixed(0)}L`;
              } else {
                return `₹${(value / 1000).toFixed(0)}K`;
              }
            }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Line
            type="monotone"
            dataKey="buy_value"
            stroke={theme.palette.success.main}
            strokeWidth={2}
            dot={{ fill: theme.palette.success.main, strokeWidth: 2, r: 4 }}
            name="Buy Value"
          />
          <Line
            type="monotone"
            dataKey="sell_value"
            stroke={theme.palette.error.main}
            strokeWidth={2}
            dot={{ fill: theme.palette.error.main, strokeWidth: 2, r: 4 }}
            name="Sell Value"
          />
          <Line
            type="monotone"
            dataKey="net_value"
            stroke={theme.palette.primary.main}
            strokeWidth={3}
            dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 5 }}
            name="Net Value"
          />
        </LineChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default TransactionTrendsChart;
