{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getTimeSectionOptions = exports.getHourSectionOptions = void 0;\nconst getHourSectionOptions = ({\n  now,\n  value,\n  utils,\n  ampm,\n  isDisabled,\n  resolveAriaLabel,\n  timeStep,\n  valueOrReferenceDate\n}) => {\n  const currentHours = value ? utils.getHours(value) : null;\n  const result = [];\n  const isSelected = (hour, overriddenCurrentHours) => {\n    const resolvedCurrentHours = overriddenCurrentHours ?? currentHours;\n    if (resolvedCurrentHours === null) {\n      return false;\n    }\n    if (ampm) {\n      if (hour === 12) {\n        return resolvedCurrentHours === 12 || resolvedCurrentHours === 0;\n      }\n      return resolvedCurrentHours === hour || resolvedCurrentHours - 12 === hour;\n    }\n    return resolvedCurrentHours === hour;\n  };\n  const isFocused = hour => {\n    return isSelected(hour, utils.getHours(valueOrReferenceDate));\n  };\n  const endHour = ampm ? 11 : 23;\n  for (let hour = 0; hour <= endHour; hour += timeStep) {\n    let label = utils.format(utils.setHours(now, hour), ampm ? 'hours12h' : 'hours24h');\n    const ariaLabel = resolveAriaLabel(parseInt(label, 10).toString());\n    label = utils.formatNumber(label);\n    result.push({\n      value: hour,\n      label,\n      isSelected,\n      isDisabled,\n      isFocused,\n      ariaLabel\n    });\n  }\n  return result;\n};\nexports.getHourSectionOptions = getHourSectionOptions;\nconst getTimeSectionOptions = ({\n  value,\n  utils,\n  isDisabled,\n  timeStep,\n  resolveLabel,\n  resolveAriaLabel,\n  hasValue = true\n}) => {\n  const isSelected = timeValue => {\n    if (value === null) {\n      return false;\n    }\n    return hasValue && value === timeValue;\n  };\n  const isFocused = timeValue => {\n    return value === timeValue;\n  };\n  return [...Array.from({\n    length: Math.ceil(60 / timeStep)\n  }, (_, index) => {\n    const timeValue = timeStep * index;\n    return {\n      value: timeValue,\n      label: utils.formatNumber(resolveLabel(timeValue)),\n      isDisabled,\n      isSelected,\n      isFocused,\n      ariaLabel: resolveAriaLabel(timeValue.toString())\n    };\n  })];\n};\nexports.getTimeSectionOptions = getTimeSectionOptions;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getTimeSectionOptions", "getHourSectionOptions", "now", "utils", "ampm", "isDisabled", "resolveAriaLabel", "timeStep", "valueOrReferenceDate", "currentHours", "getHours", "result", "isSelected", "hour", "overriddenCurrentHours", "resolvedCurrentHours", "isFocused", "endHour", "label", "format", "setHours", "aria<PERSON><PERSON><PERSON>", "parseInt", "toString", "formatNumber", "push", "resolve<PERSON>abel", "hasValue", "timeValue", "Array", "from", "length", "Math", "ceil", "_", "index"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/MultiSectionDigitalClock/MultiSectionDigitalClock.utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getTimeSectionOptions = exports.getHourSectionOptions = void 0;\nconst getHourSectionOptions = ({\n  now,\n  value,\n  utils,\n  ampm,\n  isDisabled,\n  resolveAriaLabel,\n  timeStep,\n  valueOrReferenceDate\n}) => {\n  const currentHours = value ? utils.getHours(value) : null;\n  const result = [];\n  const isSelected = (hour, overriddenCurrentHours) => {\n    const resolvedCurrentHours = overriddenCurrentHours ?? currentHours;\n    if (resolvedCurrentHours === null) {\n      return false;\n    }\n    if (ampm) {\n      if (hour === 12) {\n        return resolvedCurrentHours === 12 || resolvedCurrentHours === 0;\n      }\n      return resolvedCurrentHours === hour || resolvedCurrentHours - 12 === hour;\n    }\n    return resolvedCurrentHours === hour;\n  };\n  const isFocused = hour => {\n    return isSelected(hour, utils.getHours(valueOrReferenceDate));\n  };\n  const endHour = ampm ? 11 : 23;\n  for (let hour = 0; hour <= endHour; hour += timeStep) {\n    let label = utils.format(utils.setHours(now, hour), ampm ? 'hours12h' : 'hours24h');\n    const ariaLabel = resolveAriaLabel(parseInt(label, 10).toString());\n    label = utils.formatNumber(label);\n    result.push({\n      value: hour,\n      label,\n      isSelected,\n      isDisabled,\n      isFocused,\n      ariaLabel\n    });\n  }\n  return result;\n};\nexports.getHourSectionOptions = getHourSectionOptions;\nconst getTimeSectionOptions = ({\n  value,\n  utils,\n  isDisabled,\n  timeStep,\n  resolveLabel,\n  resolveAriaLabel,\n  hasValue = true\n}) => {\n  const isSelected = timeValue => {\n    if (value === null) {\n      return false;\n    }\n    return hasValue && value === timeValue;\n  };\n  const isFocused = timeValue => {\n    return value === timeValue;\n  };\n  return [...Array.from({\n    length: Math.ceil(60 / timeStep)\n  }, (_, index) => {\n    const timeValue = timeStep * index;\n    return {\n      value: timeValue,\n      label: utils.formatNumber(resolveLabel(timeValue)),\n      isDisabled,\n      isSelected,\n      isFocused,\n      ariaLabel: resolveAriaLabel(timeValue.toString())\n    };\n  })];\n};\nexports.getTimeSectionOptions = getTimeSectionOptions;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,qBAAqB,GAAGF,OAAO,CAACG,qBAAqB,GAAG,KAAK,CAAC;AACtE,MAAMA,qBAAqB,GAAGA,CAAC;EAC7BC,GAAG;EACHH,KAAK;EACLI,KAAK;EACLC,IAAI;EACJC,UAAU;EACVC,gBAAgB;EAChBC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGV,KAAK,GAAGI,KAAK,CAACO,QAAQ,CAACX,KAAK,CAAC,GAAG,IAAI;EACzD,MAAMY,MAAM,GAAG,EAAE;EACjB,MAAMC,UAAU,GAAGA,CAACC,IAAI,EAAEC,sBAAsB,KAAK;IACnD,MAAMC,oBAAoB,GAAGD,sBAAsB,IAAIL,YAAY;IACnE,IAAIM,oBAAoB,KAAK,IAAI,EAAE;MACjC,OAAO,KAAK;IACd;IACA,IAAIX,IAAI,EAAE;MACR,IAAIS,IAAI,KAAK,EAAE,EAAE;QACf,OAAOE,oBAAoB,KAAK,EAAE,IAAIA,oBAAoB,KAAK,CAAC;MAClE;MACA,OAAOA,oBAAoB,KAAKF,IAAI,IAAIE,oBAAoB,GAAG,EAAE,KAAKF,IAAI;IAC5E;IACA,OAAOE,oBAAoB,KAAKF,IAAI;EACtC,CAAC;EACD,MAAMG,SAAS,GAAGH,IAAI,IAAI;IACxB,OAAOD,UAAU,CAACC,IAAI,EAAEV,KAAK,CAACO,QAAQ,CAACF,oBAAoB,CAAC,CAAC;EAC/D,CAAC;EACD,MAAMS,OAAO,GAAGb,IAAI,GAAG,EAAE,GAAG,EAAE;EAC9B,KAAK,IAAIS,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAII,OAAO,EAAEJ,IAAI,IAAIN,QAAQ,EAAE;IACpD,IAAIW,KAAK,GAAGf,KAAK,CAACgB,MAAM,CAAChB,KAAK,CAACiB,QAAQ,CAAClB,GAAG,EAAEW,IAAI,CAAC,EAAET,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC;IACnF,MAAMiB,SAAS,GAAGf,gBAAgB,CAACgB,QAAQ,CAACJ,KAAK,EAAE,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;IAClEL,KAAK,GAAGf,KAAK,CAACqB,YAAY,CAACN,KAAK,CAAC;IACjCP,MAAM,CAACc,IAAI,CAAC;MACV1B,KAAK,EAAEc,IAAI;MACXK,KAAK;MACLN,UAAU;MACVP,UAAU;MACVW,SAAS;MACTK;IACF,CAAC,CAAC;EACJ;EACA,OAAOV,MAAM;AACf,CAAC;AACDb,OAAO,CAACG,qBAAqB,GAAGA,qBAAqB;AACrD,MAAMD,qBAAqB,GAAGA,CAAC;EAC7BD,KAAK;EACLI,KAAK;EACLE,UAAU;EACVE,QAAQ;EACRmB,YAAY;EACZpB,gBAAgB;EAChBqB,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMf,UAAU,GAAGgB,SAAS,IAAI;IAC9B,IAAI7B,KAAK,KAAK,IAAI,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAO4B,QAAQ,IAAI5B,KAAK,KAAK6B,SAAS;EACxC,CAAC;EACD,MAAMZ,SAAS,GAAGY,SAAS,IAAI;IAC7B,OAAO7B,KAAK,KAAK6B,SAAS;EAC5B,CAAC;EACD,OAAO,CAAC,GAAGC,KAAK,CAACC,IAAI,CAAC;IACpBC,MAAM,EAAEC,IAAI,CAACC,IAAI,CAAC,EAAE,GAAG1B,QAAQ;EACjC,CAAC,EAAE,CAAC2B,CAAC,EAAEC,KAAK,KAAK;IACf,MAAMP,SAAS,GAAGrB,QAAQ,GAAG4B,KAAK;IAClC,OAAO;MACLpC,KAAK,EAAE6B,SAAS;MAChBV,KAAK,EAAEf,KAAK,CAACqB,YAAY,CAACE,YAAY,CAACE,SAAS,CAAC,CAAC;MAClDvB,UAAU;MACVO,UAAU;MACVI,SAAS;MACTK,SAAS,EAAEf,gBAAgB,CAACsB,SAAS,CAACL,QAAQ,CAAC,CAAC;IAClD,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC;AACDzB,OAAO,CAACE,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}