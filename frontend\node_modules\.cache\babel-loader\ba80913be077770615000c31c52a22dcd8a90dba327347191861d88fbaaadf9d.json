{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getTabsUtilityClass = getTabsUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getTabsUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiTabs', slot);\n}\nconst tabsClasses = (0, _generateUtilityClasses.default)('MuiTabs', ['root', 'vertical', 'list', 'flexContainer', 'flexContainerVertical', 'centered', 'scroller', 'fixed', 'scrollableX', 'scrollableY', 'hideScrollbar', 'scrollButtons', 'scrollButtonsHideMobile', 'indicator']);\nvar _default = exports.default = tabsClasses;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "getTabsUtilityClass", "_generateUtilityClasses", "_generateUtilityClass", "slot", "tabsClasses", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/Tabs/tabsClasses.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getTabsUtilityClass = getTabsUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getTabsUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiTabs', slot);\n}\nconst tabsClasses = (0, _generateUtilityClasses.default)('MuiTabs', ['root', 'vertical', 'list', 'flexContainer', 'flexContainerVertical', 'centered', 'scroller', 'fixed', 'scrollableX', 'scrollableY', 'hideScrollbar', 'scrollButtons', 'scrollButtonsHideMobile', 'indicator']);\nvar _default = exports.default = tabsClasses;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxBG,OAAO,CAACE,mBAAmB,GAAGA,mBAAmB;AACjD,IAAIC,uBAAuB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAClG,IAAIQ,qBAAqB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC9F,SAASM,mBAAmBA,CAACG,IAAI,EAAE;EACjC,OAAO,CAAC,CAAC,EAAED,qBAAqB,CAACP,OAAO,EAAE,SAAS,EAAEQ,IAAI,CAAC;AAC5D;AACA,MAAMC,WAAW,GAAG,CAAC,CAAC,EAAEH,uBAAuB,CAACN,OAAO,EAAE,SAAS,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,uBAAuB,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,yBAAyB,EAAE,WAAW,CAAC,CAAC;AACpR,IAAIU,QAAQ,GAAGP,OAAO,CAACH,OAAO,GAAGS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}