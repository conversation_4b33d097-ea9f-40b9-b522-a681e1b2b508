{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.PickersShortcuts = PickersShortcuts;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styles = require(\"@mui/material/styles\");\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _List = _interopRequireDefault(require(\"@mui/material/List\"));\nvar _ListItem = _interopRequireDefault(require(\"@mui/material/ListItem\"));\nvar _Chip = _interopRequireDefault(require(\"@mui/material/Chip\"));\nvar _dimensions = require(\"../internals/constants/dimensions\");\nvar _hooks = require(\"../hooks\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"items\", \"changeImportance\"],\n  _excluded2 = [\"getValue\"];\nconst PickersShortcutsRoot = (0, _styles.styled)(_List.default, {\n  name: 'MuiPickersLayout',\n  slot: 'Shortcuts'\n})({});\n\n/**\n * Demos:\n *\n * - [Shortcuts](https://mui.com/x/react-date-pickers/shortcuts/)\n *\n * API:\n *\n * - [PickersShortcuts API](https://mui.com/x/api/date-pickers/pickers-shortcuts/)\n */\nfunction PickersShortcuts(props) {\n  const {\n      items,\n      changeImportance = 'accept'\n    } = props,\n    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  const {\n    setValue\n  } = (0, _hooks.usePickerActionsContext)();\n  const isValidValue = (0, _hooks.useIsValidValue)();\n  if (items == null || items.length === 0) {\n    return null;\n  }\n  const resolvedItems = items.map(_ref => {\n    let {\n        getValue\n      } = _ref,\n      item = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded2);\n    const newValue = getValue({\n      isValid: isValidValue\n    });\n    return (0, _extends2.default)({}, item, {\n      label: item.label,\n      onClick: () => {\n        setValue(newValue, {\n          changeImportance,\n          shortcut: item\n        });\n      },\n      disabled: !isValidValue(newValue)\n    });\n  });\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(PickersShortcutsRoot, (0, _extends2.default)({\n    dense: true,\n    sx: [{\n      maxHeight: _dimensions.VIEW_HEIGHT,\n      maxWidth: 200,\n      overflow: 'auto'\n    }, ...(Array.isArray(other.sx) ? other.sx : [other.sx])]\n  }, other, {\n    children: resolvedItems.map(item => {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ListItem.default, {\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Chip.default, (0, _extends2.default)({}, item))\n      }, item.id ?? item.label);\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PickersShortcuts.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Importance of the change when picking a shortcut:\n   * - \"accept\": fires `onChange`, fires `onAccept` and closes the Picker.\n   * - \"set\": fires `onChange` but do not fire `onAccept` and does not close the Picker.\n   * @default \"accept\"\n   */\n  changeImportance: _propTypes.default.oneOf(['accept', 'set']),\n  className: _propTypes.default.string,\n  component: _propTypes.default.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: _propTypes.default.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: _propTypes.default.bool,\n  /**\n   * Ordered array of shortcuts to display.\n   * If empty, does not display the shortcuts.\n   * @default []\n   */\n  items: _propTypes.default.arrayOf(_propTypes.default.shape({\n    getValue: _propTypes.default.func.isRequired,\n    id: _propTypes.default.string,\n    label: _propTypes.default.string.isRequired\n  })),\n  style: _propTypes.default.object,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: _propTypes.default.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object])\n} : void 0;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "PickersShortcuts", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_styles", "_propTypes", "_List", "_ListItem", "_Chip", "_dimensions", "_hooks", "_jsxRuntime", "_excluded", "_excluded2", "PickersShortcutsRoot", "styled", "name", "slot", "props", "items", "changeImportance", "other", "setValue", "usePickerActionsContext", "isValidValue", "useIsValidValue", "length", "resolvedItems", "map", "_ref", "getValue", "item", "newValue", "<PERSON><PERSON><PERSON><PERSON>", "label", "onClick", "shortcut", "disabled", "jsx", "dense", "sx", "maxHeight", "VIEW_HEIGHT", "max<PERSON><PERSON><PERSON>", "overflow", "Array", "isArray", "children", "id", "process", "env", "NODE_ENV", "propTypes", "oneOf", "className", "string", "component", "elementType", "bool", "disablePadding", "arrayOf", "shape", "func", "isRequired", "style", "object", "subheader", "node", "oneOfType"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/PickersShortcuts/PickersShortcuts.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.PickersShortcuts = PickersShortcuts;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styles = require(\"@mui/material/styles\");\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _List = _interopRequireDefault(require(\"@mui/material/List\"));\nvar _ListItem = _interopRequireDefault(require(\"@mui/material/ListItem\"));\nvar _Chip = _interopRequireDefault(require(\"@mui/material/Chip\"));\nvar _dimensions = require(\"../internals/constants/dimensions\");\nvar _hooks = require(\"../hooks\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"items\", \"changeImportance\"],\n  _excluded2 = [\"getValue\"];\nconst PickersShortcutsRoot = (0, _styles.styled)(_List.default, {\n  name: 'MuiPickersLayout',\n  slot: 'Shortcuts'\n})({});\n\n/**\n * Demos:\n *\n * - [Shortcuts](https://mui.com/x/react-date-pickers/shortcuts/)\n *\n * API:\n *\n * - [PickersShortcuts API](https://mui.com/x/api/date-pickers/pickers-shortcuts/)\n */\nfunction PickersShortcuts(props) {\n  const {\n      items,\n      changeImportance = 'accept'\n    } = props,\n    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  const {\n    setValue\n  } = (0, _hooks.usePickerActionsContext)();\n  const isValidValue = (0, _hooks.useIsValidValue)();\n  if (items == null || items.length === 0) {\n    return null;\n  }\n  const resolvedItems = items.map(_ref => {\n    let {\n        getValue\n      } = _ref,\n      item = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded2);\n    const newValue = getValue({\n      isValid: isValidValue\n    });\n    return (0, _extends2.default)({}, item, {\n      label: item.label,\n      onClick: () => {\n        setValue(newValue, {\n          changeImportance,\n          shortcut: item\n        });\n      },\n      disabled: !isValidValue(newValue)\n    });\n  });\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(PickersShortcutsRoot, (0, _extends2.default)({\n    dense: true,\n    sx: [{\n      maxHeight: _dimensions.VIEW_HEIGHT,\n      maxWidth: 200,\n      overflow: 'auto'\n    }, ...(Array.isArray(other.sx) ? other.sx : [other.sx])]\n  }, other, {\n    children: resolvedItems.map(item => {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ListItem.default, {\n        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_Chip.default, (0, _extends2.default)({}, item))\n      }, item.id ?? item.label);\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PickersShortcuts.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Importance of the change when picking a shortcut:\n   * - \"accept\": fires `onChange`, fires `onAccept` and closes the Picker.\n   * - \"set\": fires `onChange` but do not fire `onAccept` and does not close the Picker.\n   * @default \"accept\"\n   */\n  changeImportance: _propTypes.default.oneOf(['accept', 'set']),\n  className: _propTypes.default.string,\n  component: _propTypes.default.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: _propTypes.default.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: _propTypes.default.bool,\n  /**\n   * Ordered array of shortcuts to display.\n   * If empty, does not display the shortcuts.\n   * @default []\n   */\n  items: _propTypes.default.arrayOf(_propTypes.default.shape({\n    getValue: _propTypes.default.func.isRequired,\n    id: _propTypes.default.string,\n    label: _propTypes.default.string.isRequired\n  })),\n  style: _propTypes.default.object,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: _propTypes.default.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object])\n} : void 0;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3C,IAAIC,SAAS,GAAGT,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIS,8BAA8B,GAAGV,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIU,KAAK,GAAGR,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIW,OAAO,GAAGX,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIY,UAAU,GAAGb,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIa,KAAK,GAAGd,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACjE,IAAIc,SAAS,GAAGf,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AACzE,IAAIe,KAAK,GAAGhB,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACjE,IAAIgB,WAAW,GAAGhB,OAAO,CAAC,mCAAmC,CAAC;AAC9D,IAAIiB,MAAM,GAAGjB,OAAO,CAAC,UAAU,CAAC;AAChC,IAAIkB,WAAW,GAAGlB,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAMmB,SAAS,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC;EAC7CC,UAAU,GAAG,CAAC,UAAU,CAAC;AAC3B,MAAMC,oBAAoB,GAAG,CAAC,CAAC,EAAEV,OAAO,CAACW,MAAM,EAAET,KAAK,CAACZ,OAAO,EAAE;EAC9DsB,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,gBAAgBA,CAACkB,KAAK,EAAE;EAC/B,MAAM;MACFC,KAAK;MACLC,gBAAgB,GAAG;IACrB,CAAC,GAAGF,KAAK;IACTG,KAAK,GAAG,CAAC,CAAC,EAAEnB,8BAA8B,CAACR,OAAO,EAAEwB,KAAK,EAAEN,SAAS,CAAC;EACvE,MAAM;IACJU;EACF,CAAC,GAAG,CAAC,CAAC,EAAEZ,MAAM,CAACa,uBAAuB,EAAE,CAAC;EACzC,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAEd,MAAM,CAACe,eAAe,EAAE,CAAC;EAClD,IAAIN,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;IACvC,OAAO,IAAI;EACb;EACA,MAAMC,aAAa,GAAGR,KAAK,CAACS,GAAG,CAACC,IAAI,IAAI;IACtC,IAAI;QACAC;MACF,CAAC,GAAGD,IAAI;MACRE,IAAI,GAAG,CAAC,CAAC,EAAE7B,8BAA8B,CAACR,OAAO,EAAEmC,IAAI,EAAEhB,UAAU,CAAC;IACtE,MAAMmB,QAAQ,GAAGF,QAAQ,CAAC;MACxBG,OAAO,EAAET;IACX,CAAC,CAAC;IACF,OAAO,CAAC,CAAC,EAAEvB,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEqC,IAAI,EAAE;MACtCG,KAAK,EAAEH,IAAI,CAACG,KAAK;MACjBC,OAAO,EAAEA,CAAA,KAAM;QACbb,QAAQ,CAACU,QAAQ,EAAE;UACjBZ,gBAAgB;UAChBgB,QAAQ,EAAEL;QACZ,CAAC,CAAC;MACJ,CAAC;MACDM,QAAQ,EAAE,CAACb,YAAY,CAACQ,QAAQ;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAa,CAAC,CAAC,EAAErB,WAAW,CAAC2B,GAAG,EAAExB,oBAAoB,EAAE,CAAC,CAAC,EAAEb,SAAS,CAACP,OAAO,EAAE;IACpF6C,KAAK,EAAE,IAAI;IACXC,EAAE,EAAE,CAAC;MACHC,SAAS,EAAEhC,WAAW,CAACiC,WAAW;MAClCC,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE;IACZ,CAAC,EAAE,IAAIC,KAAK,CAACC,OAAO,CAACzB,KAAK,CAACmB,EAAE,CAAC,GAAGnB,KAAK,CAACmB,EAAE,GAAG,CAACnB,KAAK,CAACmB,EAAE,CAAC,CAAC;EACzD,CAAC,EAAEnB,KAAK,EAAE;IACR0B,QAAQ,EAAEpB,aAAa,CAACC,GAAG,CAACG,IAAI,IAAI;MAClC,OAAO,aAAa,CAAC,CAAC,EAAEpB,WAAW,CAAC2B,GAAG,EAAE/B,SAAS,CAACb,OAAO,EAAE;QAC1DqD,QAAQ,EAAE,aAAa,CAAC,CAAC,EAAEpC,WAAW,CAAC2B,GAAG,EAAE9B,KAAK,CAACd,OAAO,EAAE,CAAC,CAAC,EAAEO,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEqC,IAAI,CAAC;MAC7F,CAAC,EAAEA,IAAI,CAACiB,EAAE,IAAIjB,IAAI,CAACG,KAAK,CAAC;IAC3B,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACAe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnD,gBAAgB,CAACoD,SAAS,GAAG;EACnE;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACEhC,gBAAgB,EAAEf,UAAU,CAACX,OAAO,CAAC2D,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;EAC7DC,SAAS,EAAEjD,UAAU,CAACX,OAAO,CAAC6D,MAAM;EACpCC,SAAS,EAAEnD,UAAU,CAACX,OAAO,CAAC+D,WAAW;EACzC;AACF;AACA;AACA;AACA;AACA;EACElB,KAAK,EAAElC,UAAU,CAACX,OAAO,CAACgE,IAAI;EAC9B;AACF;AACA;AACA;EACEC,cAAc,EAAEtD,UAAU,CAACX,OAAO,CAACgE,IAAI;EACvC;AACF;AACA;AACA;AACA;EACEvC,KAAK,EAAEd,UAAU,CAACX,OAAO,CAACkE,OAAO,CAACvD,UAAU,CAACX,OAAO,CAACmE,KAAK,CAAC;IACzD/B,QAAQ,EAAEzB,UAAU,CAACX,OAAO,CAACoE,IAAI,CAACC,UAAU;IAC5Cf,EAAE,EAAE3C,UAAU,CAACX,OAAO,CAAC6D,MAAM;IAC7BrB,KAAK,EAAE7B,UAAU,CAACX,OAAO,CAAC6D,MAAM,CAACQ;EACnC,CAAC,CAAC,CAAC;EACHC,KAAK,EAAE3D,UAAU,CAACX,OAAO,CAACuE,MAAM;EAChC;AACF;AACA;EACEC,SAAS,EAAE7D,UAAU,CAACX,OAAO,CAACyE,IAAI;EAClC;AACF;AACA;EACE3B,EAAE,EAAEnC,UAAU,CAACX,OAAO,CAAC0E,SAAS,CAAC,CAAC/D,UAAU,CAACX,OAAO,CAACkE,OAAO,CAACvD,UAAU,CAACX,OAAO,CAAC0E,SAAS,CAAC,CAAC/D,UAAU,CAACX,OAAO,CAACoE,IAAI,EAAEzD,UAAU,CAACX,OAAO,CAACuE,MAAM,EAAE5D,UAAU,CAACX,OAAO,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAErD,UAAU,CAACX,OAAO,CAACoE,IAAI,EAAEzD,UAAU,CAACX,OAAO,CAACuE,MAAM,CAAC;AAChO,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}