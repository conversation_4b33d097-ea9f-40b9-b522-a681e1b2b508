{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getDialogTitleUtilityClass = getDialogTitleUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getDialogTitleUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiDialogTitle', slot);\n}\nconst dialogTitleClasses = (0, _generateUtilityClasses.default)('MuiDialogTitle', ['root']);\nvar _default = exports.default = dialogTitleClasses;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "getDialogTitleUtilityClass", "_generateUtilityClasses", "_generateUtilityClass", "slot", "dialogTitleClasses", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/DialogTitle/dialogTitleClasses.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getDialogTitleUtilityClass = getDialogTitleUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getDialogTitleUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiDialogTitle', slot);\n}\nconst dialogTitleClasses = (0, _generateUtilityClasses.default)('MuiDialogTitle', ['root']);\nvar _default = exports.default = dialogTitleClasses;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxBG,OAAO,CAACE,0BAA0B,GAAGA,0BAA0B;AAC/D,IAAIC,uBAAuB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAClG,IAAIQ,qBAAqB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC9F,SAASM,0BAA0BA,CAACG,IAAI,EAAE;EACxC,OAAO,CAAC,CAAC,EAAED,qBAAqB,CAACP,OAAO,EAAE,gBAAgB,EAAEQ,IAAI,CAAC;AACnE;AACA,MAAMC,kBAAkB,GAAG,CAAC,CAAC,EAAEH,uBAAuB,CAACN,OAAO,EAAE,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC;AAC3F,IAAIU,QAAQ,GAAGP,OAAO,CAACH,OAAO,GAAGS,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}