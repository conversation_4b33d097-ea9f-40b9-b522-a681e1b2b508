{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useViews = useViews;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _createStepNavigation = require(\"../utils/createStepNavigation\");\nlet warnedOnceNotValidView = false;\nfunction useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange,\n  getStepNavigation\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI X: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI X: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = (0, _useControlled.default)({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = (0, _useControlled.default)({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  const stepNavigation = getStepNavigation ? getStepNavigation({\n    setView,\n    view,\n    defaultView: defaultView.current,\n    views\n  }) : _createStepNavigation.DEFAULT_STEP_NAVIGATION;\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = views[viewIndex - 1] ?? null;\n  const nextView = views[viewIndex + 1] ?? null;\n  const handleFocusedViewChange = (0, _useEventCallback.default)((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange?.(viewToFocus, hasFocus);\n  });\n  const handleChangeView = (0, _useEventCallback.default)(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = (0, _useEventCallback.default)(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = (0, _useEventCallback.default)((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but when it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n\n    // The selected view can be different from the active view,\n    // This can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    let currentView = null;\n    if (selectedView != null && selectedView !== view) {\n      currentView = selectedView;\n    } else if (isSelectionFinishedOnCurrentView) {\n      currentView = view;\n    }\n    if (currentView == null) {\n      return;\n    }\n    const viewToNavigateTo = views[views.indexOf(currentView) + 1];\n    if (viewToNavigateTo == null || !stepNavigation.areViewsInSameStep(currentView, viewToNavigateTo)) {\n      return;\n    }\n    handleChangeView(viewToNavigateTo);\n  });\n  return (0, _extends2.default)({}, stepNavigation, {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up-to-date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  });\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "useViews", "_extends2", "React", "_useEventCallback", "_useControlled", "_createStepNavigation", "warnedOnceNotValidView", "onChange", "onViewChange", "openTo", "view", "inView", "views", "autoFocus", "focused<PERSON>iew", "inFocusedView", "onFocusedViewChange", "getStepNavigation", "process", "env", "NODE_ENV", "includes", "console", "warn", "join", "previousOpenTo", "useRef", "previousViews", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "name", "state", "controlled", "current", "defaultFocusedView", "setFocusedView", "stepNavigation", "DEFAULT_STEP_NAVIGATION", "useEffect", "some", "previousView", "viewIndex", "indexOf", "next<PERSON>iew", "handleFocusedViewChange", "viewToFocus", "hasFocus", "prevFocusedView", "handleChangeView", "newView", "goToNextView", "setValueAndGoToNextView", "currentViewSelectionState", "<PERSON><PERSON><PERSON><PERSON>", "isSelectionFinishedOnCurrentView", "hasMoreViews", "length", "Boolean", "globalSelectionState", "current<PERSON>iew", "viewToNavigateTo", "areViewsInSameStep"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useViews.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useViews = useViews;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _createStepNavigation = require(\"../utils/createStepNavigation\");\nlet warnedOnceNotValidView = false;\nfunction useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange,\n  getStepNavigation\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI X: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI X: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = (0, _useControlled.default)({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = (0, _useControlled.default)({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  const stepNavigation = getStepNavigation ? getStepNavigation({\n    setView,\n    view,\n    defaultView: defaultView.current,\n    views\n  }) : _createStepNavigation.DEFAULT_STEP_NAVIGATION;\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = views[viewIndex - 1] ?? null;\n  const nextView = views[viewIndex + 1] ?? null;\n  const handleFocusedViewChange = (0, _useEventCallback.default)((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange?.(viewToFocus, hasFocus);\n  });\n  const handleChangeView = (0, _useEventCallback.default)(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = (0, _useEventCallback.default)(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = (0, _useEventCallback.default)((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but when it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n\n    // The selected view can be different from the active view,\n    // This can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    let currentView = null;\n    if (selectedView != null && selectedView !== view) {\n      currentView = selectedView;\n    } else if (isSelectionFinishedOnCurrentView) {\n      currentView = view;\n    }\n    if (currentView == null) {\n      return;\n    }\n    const viewToNavigateTo = views[views.indexOf(currentView) + 1];\n    if (viewToNavigateTo == null || !stepNavigation.areViewsInSameStep(currentView, viewToNavigateTo)) {\n      return;\n    }\n    handleChangeView(viewToNavigateTo);\n  });\n  return (0, _extends2.default)({}, stepNavigation, {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up-to-date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAGA,QAAQ;AAC3B,IAAIC,SAAS,GAAGN,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIS,KAAK,GAAGV,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIU,iBAAiB,GAAGR,sBAAsB,CAACF,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIW,cAAc,GAAGT,sBAAsB,CAACF,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAIY,qBAAqB,GAAGZ,OAAO,CAAC,+BAA+B,CAAC;AACpE,IAAIa,sBAAsB,GAAG,KAAK;AAClC,SAASN,QAAQA,CAAC;EAChBO,QAAQ;EACRC,YAAY;EACZC,MAAM;EACNC,IAAI,EAAEC,MAAM;EACZC,KAAK;EACLC,SAAS;EACTC,WAAW,EAAEC,aAAa;EAC1BC,mBAAmB;EACnBC;AACF,CAAC,EAAE;EACD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACd,sBAAsB,EAAE;MAC3B,IAAIK,MAAM,IAAI,IAAI,IAAI,CAACC,KAAK,CAACS,QAAQ,CAACV,MAAM,CAAC,EAAE;QAC7CW,OAAO,CAACC,IAAI,CAAC,kBAAkBZ,MAAM,0BAA0B,EAAE,sCAAsCC,KAAK,CAACY,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACjIlB,sBAAsB,GAAG,IAAI;MAC/B;MACA,IAAIK,MAAM,IAAI,IAAI,IAAIF,MAAM,IAAI,IAAI,IAAI,CAACG,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,EAAE;QAC/Da,OAAO,CAACC,IAAI,CAAC,oBAAoBd,MAAM,0BAA0B,EAAE,sCAAsCG,KAAK,CAACY,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnIlB,sBAAsB,GAAG,IAAI;MAC/B;IACF;EACF;EACA,MAAMmB,cAAc,GAAGvB,KAAK,CAACwB,MAAM,CAACjB,MAAM,CAAC;EAC3C,MAAMkB,aAAa,GAAGzB,KAAK,CAACwB,MAAM,CAACd,KAAK,CAAC;EACzC,MAAMgB,WAAW,GAAG1B,KAAK,CAACwB,MAAM,CAACd,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5E,MAAM,CAACF,IAAI,EAAEmB,OAAO,CAAC,GAAG,CAAC,CAAC,EAAEzB,cAAc,CAACV,OAAO,EAAE;IAClDoC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAErB,MAAM;IAClBjB,OAAO,EAAEkC,WAAW,CAACK;EACvB,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGhC,KAAK,CAACwB,MAAM,CAACb,SAAS,GAAGH,IAAI,GAAG,IAAI,CAAC;EAChE,MAAM,CAACI,WAAW,EAAEqB,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE/B,cAAc,CAACV,OAAO,EAAE;IAChEoC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAEjB,aAAa;IACzBrB,OAAO,EAAEwC,kBAAkB,CAACD;EAC9B,CAAC,CAAC;EACF,MAAMG,cAAc,GAAGnB,iBAAiB,GAAGA,iBAAiB,CAAC;IAC3DY,OAAO;IACPnB,IAAI;IACJkB,WAAW,EAAEA,WAAW,CAACK,OAAO;IAChCrB;EACF,CAAC,CAAC,GAAGP,qBAAqB,CAACgC,uBAAuB;EAClDnC,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpB;IACA,IAAIb,cAAc,CAACQ,OAAO,IAAIR,cAAc,CAACQ,OAAO,KAAKxB,MAAM,IAAIkB,aAAa,CAACM,OAAO,IAAIN,aAAa,CAACM,OAAO,CAACM,IAAI,CAACC,YAAY,IAAI,CAAC5B,KAAK,CAACS,QAAQ,CAACmB,YAAY,CAAC,CAAC,EAAE;MACrKX,OAAO,CAACjB,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;MACnDe,aAAa,CAACM,OAAO,GAAGrB,KAAK;MAC7Ba,cAAc,CAACQ,OAAO,GAAGxB,MAAM;IACjC;EACF,CAAC,EAAE,CAACA,MAAM,EAAEoB,OAAO,EAAEnB,IAAI,EAAEE,KAAK,CAAC,CAAC;EAClC,MAAM6B,SAAS,GAAG7B,KAAK,CAAC8B,OAAO,CAAChC,IAAI,CAAC;EACrC,MAAM8B,YAAY,GAAG5B,KAAK,CAAC6B,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI;EACjD,MAAME,QAAQ,GAAG/B,KAAK,CAAC6B,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI;EAC7C,MAAMG,uBAAuB,GAAG,CAAC,CAAC,EAAEzC,iBAAiB,CAACT,OAAO,EAAE,CAACmD,WAAW,EAAEC,QAAQ,KAAK;IACxF,IAAIA,QAAQ,EAAE;MACZ;MACAX,cAAc,CAACU,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACAV,cAAc,CAACY,eAAe,IAAIF,WAAW,KAAKE,eAAe,GAAG,IAAI,GAAGA,eAAe,CAAC;MAC3F,CAAC;IACH;IACA/B,mBAAmB,GAAG6B,WAAW,EAAEC,QAAQ,CAAC;EAC9C,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG,CAAC,CAAC,EAAE7C,iBAAiB,CAACT,OAAO,EAAEuD,OAAO,IAAI;IACjE;IACAL,uBAAuB,CAACK,OAAO,EAAE,IAAI,CAAC;IACtC,IAAIA,OAAO,KAAKvC,IAAI,EAAE;MACpB;IACF;IACAmB,OAAO,CAACoB,OAAO,CAAC;IAChB,IAAIzC,YAAY,EAAE;MAChBA,YAAY,CAACyC,OAAO,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAE/C,iBAAiB,CAACT,OAAO,EAAE,MAAM;IACxD,IAAIiD,QAAQ,EAAE;MACZK,gBAAgB,CAACL,QAAQ,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,MAAMQ,uBAAuB,GAAG,CAAC,CAAC,EAAEhD,iBAAiB,CAACT,OAAO,EAAE,CAACK,KAAK,EAAEqD,yBAAyB,EAAEC,YAAY,KAAK;IACjH,MAAMC,gCAAgC,GAAGF,yBAAyB,KAAK,QAAQ;IAC/E,MAAMG,YAAY,GAAGF,YAAY;IACjC;IACA;IACAzC,KAAK,CAAC8B,OAAO,CAACW,YAAY,CAAC,GAAGzC,KAAK,CAAC4C,MAAM,GAAG,CAAC,GAAGC,OAAO,CAACd,QAAQ,CAAC;IAClE,MAAMe,oBAAoB,GAAGJ,gCAAgC,IAAIC,YAAY,GAAG,SAAS,GAAGH,yBAAyB;IACrH7C,QAAQ,CAACR,KAAK,EAAE2D,oBAAoB,EAAEL,YAAY,CAAC;;IAEnD;IACA;IACA,IAAIM,WAAW,GAAG,IAAI;IACtB,IAAIN,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK3C,IAAI,EAAE;MACjDiD,WAAW,GAAGN,YAAY;IAC5B,CAAC,MAAM,IAAIC,gCAAgC,EAAE;MAC3CK,WAAW,GAAGjD,IAAI;IACpB;IACA,IAAIiD,WAAW,IAAI,IAAI,EAAE;MACvB;IACF;IACA,MAAMC,gBAAgB,GAAGhD,KAAK,CAACA,KAAK,CAAC8B,OAAO,CAACiB,WAAW,CAAC,GAAG,CAAC,CAAC;IAC9D,IAAIC,gBAAgB,IAAI,IAAI,IAAI,CAACxB,cAAc,CAACyB,kBAAkB,CAACF,WAAW,EAAEC,gBAAgB,CAAC,EAAE;MACjG;IACF;IACAZ,gBAAgB,CAACY,gBAAgB,CAAC;EACpC,CAAC,CAAC;EACF,OAAO,CAAC,CAAC,EAAE3D,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAE0C,cAAc,EAAE;IAChD1B,IAAI;IACJmB,OAAO,EAAEmB,gBAAgB;IACzBlC,WAAW;IACXqB,cAAc,EAAES,uBAAuB;IACvCD,QAAQ;IACRH,YAAY;IACZ;IACAZ,WAAW,EAAEhB,KAAK,CAACS,QAAQ,CAACZ,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC;IACvDsC,YAAY;IACZC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}