{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useValidation = useValidation;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useUtils = require(\"../internals/hooks/useUtils\");\n/**\n * Utility hook to check if a given value is valid based on the provided validation props.\n * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n * @param {UseValidationOptions<TValue, TError, TValidationProps>} options The options to configure the hook.\n * @param {TValue} options.value The value to validate.\n * @param {PickersTimezone} options.timezone The timezone to use for the validation.\n * @param {Validator<TValue, TError, TValidationProps>} options.validator The validator function to use.\n * @param {TValidationProps} options.props The validation props, they differ depending on the component.\n * @param {(error: TError, value: TValue) => void} options.onError Callback fired when the error associated with the current value changes.\n */\nfunction useValidation(options) {\n  const {\n    props,\n    validator,\n    value,\n    timezone,\n    onError\n  } = options;\n  const adapter = (0, _useUtils.useLocalizationContext)();\n  const previousValidationErrorRef = React.useRef(validator.valueManager.defaultErrorState);\n  const validationError = validator({\n    adapter,\n    value,\n    timezone,\n    props\n  });\n  const hasValidationError = validator.valueManager.hasError(validationError);\n  React.useEffect(() => {\n    if (onError && !validator.valueManager.isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n    previousValidationErrorRef.current = validationError;\n  }, [validator, onError, validationError, value]);\n  const getValidationErrorForNewValue = (0, _useEventCallback.default)(newValue => {\n    return validator({\n      adapter,\n      value: newValue,\n      timezone,\n      props\n    });\n  });\n  return {\n    validationError,\n    hasValidationError,\n    getValidationErrorForNewValue\n  };\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "useValidation", "React", "_useEventCallback", "_useUtils", "options", "props", "validator", "timezone", "onError", "adapter", "useLocalizationContext", "previousValidationErrorRef", "useRef", "valueManager", "defaultErrorState", "validationError", "hasValidationError", "<PERSON><PERSON><PERSON><PERSON>", "useEffect", "isSameError", "current", "getValidationErrorForNewValue", "newValue"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/validation/useValidation.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useValidation = useValidation;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useUtils = require(\"../internals/hooks/useUtils\");\n/**\n * Utility hook to check if a given value is valid based on the provided validation props.\n * @template TValue The value type. It will be the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.\n * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.\n * @param {UseValidationOptions<TValue, TError, TValidationProps>} options The options to configure the hook.\n * @param {TValue} options.value The value to validate.\n * @param {PickersTimezone} options.timezone The timezone to use for the validation.\n * @param {Validator<TValue, TError, TValidationProps>} options.validator The validator function to use.\n * @param {TValidationProps} options.props The validation props, they differ depending on the component.\n * @param {(error: TError, value: TValue) => void} options.onError Callback fired when the error associated with the current value changes.\n */\nfunction useValidation(options) {\n  const {\n    props,\n    validator,\n    value,\n    timezone,\n    onError\n  } = options;\n  const adapter = (0, _useUtils.useLocalizationContext)();\n  const previousValidationErrorRef = React.useRef(validator.valueManager.defaultErrorState);\n  const validationError = validator({\n    adapter,\n    value,\n    timezone,\n    props\n  });\n  const hasValidationError = validator.valueManager.hasError(validationError);\n  React.useEffect(() => {\n    if (onError && !validator.valueManager.isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n    previousValidationErrorRef.current = validationError;\n  }, [validator, onError, validationError, value]);\n  const getValidationErrorForNewValue = (0, _useEventCallback.default)(newValue => {\n    return validator({\n      adapter,\n      value: newValue,\n      timezone,\n      props\n    });\n  });\n  return {\n    validationError,\n    hasValidationError,\n    getValidationErrorForNewValue\n  };\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,aAAa,GAAGA,aAAa;AACrC,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,iBAAiB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIU,SAAS,GAAGV,OAAO,CAAC,6BAA6B,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,aAAaA,CAACI,OAAO,EAAE;EAC9B,MAAM;IACJC,KAAK;IACLC,SAAS;IACTP,KAAK;IACLQ,QAAQ;IACRC;EACF,CAAC,GAAGJ,OAAO;EACX,MAAMK,OAAO,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACO,sBAAsB,EAAE,CAAC;EACvD,MAAMC,0BAA0B,GAAGV,KAAK,CAACW,MAAM,CAACN,SAAS,CAACO,YAAY,CAACC,iBAAiB,CAAC;EACzF,MAAMC,eAAe,GAAGT,SAAS,CAAC;IAChCG,OAAO;IACPV,KAAK;IACLQ,QAAQ;IACRF;EACF,CAAC,CAAC;EACF,MAAMW,kBAAkB,GAAGV,SAAS,CAACO,YAAY,CAACI,QAAQ,CAACF,eAAe,CAAC;EAC3Ed,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIV,OAAO,IAAI,CAACF,SAAS,CAACO,YAAY,CAACM,WAAW,CAACJ,eAAe,EAAEJ,0BAA0B,CAACS,OAAO,CAAC,EAAE;MACvGZ,OAAO,CAACO,eAAe,EAAEhB,KAAK,CAAC;IACjC;IACAY,0BAA0B,CAACS,OAAO,GAAGL,eAAe;EACtD,CAAC,EAAE,CAACT,SAAS,EAAEE,OAAO,EAAEO,eAAe,EAAEhB,KAAK,CAAC,CAAC;EAChD,MAAMsB,6BAA6B,GAAG,CAAC,CAAC,EAAEnB,iBAAiB,CAACR,OAAO,EAAE4B,QAAQ,IAAI;IAC/E,OAAOhB,SAAS,CAAC;MACfG,OAAO;MACPV,KAAK,EAAEuB,QAAQ;MACff,QAAQ;MACRF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO;IACLU,eAAe;IACfC,kBAAkB;IAClBK;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}