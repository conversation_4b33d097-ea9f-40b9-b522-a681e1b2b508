{"ast": null, "code": "import axios from 'axios';\n\n// API base URL - will use proxy in development\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor for logging\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  console.error('API Request Error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor for error handling\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  console.error('API Response Error:', error);\n  if (error.response) {\n    // Server responded with error status\n    const {\n      status,\n      data\n    } = error.response;\n    console.error(`API Error ${status}:`, data);\n  } else if (error.request) {\n    // Request was made but no response received\n    console.error('No response received:', error.request);\n  } else {\n    // Something else happened\n    console.error('Request setup error:', error.message);\n  }\n  return Promise.reject(error);\n});\n\n// API service interface\n\n// API service class\nclass ApiService {\n  // Health check\n  async healthCheck() {\n    return api.get('/health');\n  }\n\n  // Transactions\n  async getTransactions(filters = {}) {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n    return api.get(`/transactions?${params.toString()}`);\n  }\n\n  // Analytics\n  async getAnalyticsSummary(fromDate, toDate) {\n    const params = new URLSearchParams();\n    if (fromDate) params.append('from_date', fromDate);\n    if (toDate) params.append('to_date', toDate);\n    return api.get(`/analytics/summary?${params.toString()}`);\n  }\n  async getTopCompanies(options = {}) {\n    const params = new URLSearchParams();\n    Object.entries(options).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n    return api.get(`/analytics/top-companies?${params.toString()}`);\n  }\n\n  // Companies\n  async getCompanyTransactions(symbol, options = {}) {\n    const params = new URLSearchParams();\n    Object.entries(options).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n    return api.get(`/companies/${symbol}/transactions?${params.toString()}`);\n  }\n\n  // Search\n  async search(query, type = 'all', limit = 10) {\n    const params = new URLSearchParams({\n      q: query,\n      type,\n      limit: limit.toString()\n    });\n    return api.get(`/search?${params.toString()}`);\n  }\n\n  // System status\n  async getSystemStatus() {\n    return api.get('/system/status');\n  }\n\n  // Manual scraping\n  async triggerManualScrape(daysBack = 7) {\n    return api.post(`/system/scrape?days_back=${daysBack}`);\n  }\n\n  // Utility method to format API errors\n  formatError(error) {\n    var _error$response, _error$response$data, _error$response2, _error$response2$data;\n    if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n      return error.response.data.detail;\n    } else if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.message) {\n      return error.response.data.message;\n    } else if (error.message) {\n      return error.message;\n    } else {\n      return 'An unexpected error occurred';\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "data", "message", "ApiService", "healthCheck", "get", "getTransactions", "filters", "params", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "append", "toString", "getAnalyticsSummary", "fromDate", "toDate", "getTopCompanies", "options", "getCompanyTransactions", "symbol", "search", "query", "type", "limit", "q", "getSystemStatus", "triggerManualScrape", "daysBack", "post", "formatError", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "detail", "apiService"], "sources": ["D:/chirag/nsescrapper/frontend/src/services/apiService.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\n\n// API base URL - will use proxy in development\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor for logging\napi.interceptors.request.use(\n  (config) => {\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    console.error('API Response Error:', error);\n    \n    if (error.response) {\n      // Server responded with error status\n      const { status, data } = error.response;\n      console.error(`API Error ${status}:`, data);\n    } else if (error.request) {\n      // Request was made but no response received\n      console.error('No response received:', error.request);\n    } else {\n      // Something else happened\n      console.error('Request setup error:', error.message);\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\n// API service interface\nexport interface TransactionFilters {\n  symbol?: string;\n  person_name?: string;\n  person_category?: string;\n  transaction_type?: string;\n  from_date?: string;\n  to_date?: string;\n  min_value?: number;\n  max_value?: number;\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'asc' | 'desc';\n}\n\nexport interface Transaction {\n  id: string;\n  symbol: string;\n  company_name: string;\n  person_name: string;\n  person_category?: string;\n  transaction_date: string;\n  intimation_date?: string;\n  transaction_type?: string;\n  transaction_mode?: string;\n  security_type?: string;\n  exchange_name?: string;\n  buy_value?: number;\n  sell_value?: number;\n  buy_quantity?: number;\n  sell_quantity?: number;\n  security_value?: number;\n  securities_acquired?: number;\n  shares_before_transaction?: number;\n  percentage_before_transaction?: number;\n  shares_after_transaction?: number;\n  percentage_after_transaction?: number;\n  remarks?: string;\n  xbrl_link?: string;\n  created_at: string;\n}\n\nexport interface TransactionListResponse {\n  transactions: Transaction[];\n  total_count: number;\n  page: number;\n  limit: number;\n  total_pages: number;\n}\n\nexport interface AnalyticsSummary {\n  total_transactions: number;\n  total_buy_value: number;\n  total_sell_value: number;\n  unique_companies: number;\n  unique_persons: number;\n  date_range: {\n    earliest: string;\n    latest: string;\n  };\n}\n\nexport interface CompanyActivity {\n  symbol: string;\n  company_name: string;\n  transaction_count: number;\n  total_value: number;\n  total_buy_value: number;\n  total_sell_value: number;\n  unique_insiders: number;\n  latest_transaction_date: string;\n}\n\nexport interface SearchResult {\n  type: string;\n  id?: string;\n  symbol?: string;\n  name: string;\n  category?: string;\n  transaction_count?: number;\n}\n\nexport interface SearchResponse {\n  results: SearchResult[];\n  total_count: number;\n  query: string;\n  search_type: string;\n}\n\nexport interface SystemStatus {\n  api_status: string;\n  database_status: {\n    total_transactions: number;\n    total_companies: number;\n    date_range?: {\n      earliest: string;\n      latest: string;\n    };\n    last_updated?: string;\n  };\n  scraper_status: {\n    last_execution?: string;\n    status: string;\n    records_fetched: number;\n    records_inserted: number;\n    records_skipped: number;\n    error_message?: string;\n  };\n  timestamp: string;\n}\n\nexport interface ManualScrapeResponse {\n  status: 'success' | 'error';\n  message: string;\n  records_fetched?: number;\n  records_inserted?: number;\n  records_skipped?: number;\n  execution_time?: number;\n  timestamp: string;\n}\n\n// API service class\nclass ApiService {\n  // Health check\n  async healthCheck(): Promise<AxiosResponse<any>> {\n    return api.get('/health');\n  }\n\n  // Transactions\n  async getTransactions(filters: TransactionFilters = {}): Promise<AxiosResponse<TransactionListResponse>> {\n    const params = new URLSearchParams();\n    \n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    return api.get(`/transactions?${params.toString()}`);\n  }\n\n  // Analytics\n  async getAnalyticsSummary(fromDate?: string, toDate?: string): Promise<AxiosResponse<AnalyticsSummary>> {\n    const params = new URLSearchParams();\n    if (fromDate) params.append('from_date', fromDate);\n    if (toDate) params.append('to_date', toDate);\n    \n    return api.get(`/analytics/summary?${params.toString()}`);\n  }\n\n  async getTopCompanies(options: {\n    limit?: number;\n    sort_by?: string;\n    from_date?: string;\n    to_date?: string;\n  } = {}): Promise<AxiosResponse<CompanyActivity[]>> {\n    const params = new URLSearchParams();\n    \n    Object.entries(options).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    return api.get(`/analytics/top-companies?${params.toString()}`);\n  }\n\n  // Companies\n  async getCompanyTransactions(\n    symbol: string,\n    options: {\n      from_date?: string;\n      to_date?: string;\n      page?: number;\n      limit?: number;\n    } = {}\n  ): Promise<AxiosResponse<TransactionListResponse>> {\n    const params = new URLSearchParams();\n    \n    Object.entries(options).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        params.append(key, value.toString());\n      }\n    });\n\n    return api.get(`/companies/${symbol}/transactions?${params.toString()}`);\n  }\n\n  // Search\n  async search(\n    query: string,\n    type: 'all' | 'company' | 'person' = 'all',\n    limit: number = 10\n  ): Promise<AxiosResponse<SearchResponse>> {\n    const params = new URLSearchParams({\n      q: query,\n      type,\n      limit: limit.toString(),\n    });\n\n    return api.get(`/search?${params.toString()}`);\n  }\n\n  // System status\n  async getSystemStatus(): Promise<AxiosResponse<SystemStatus>> {\n    return api.get('/system/status');\n  }\n\n  // Manual scraping\n  async triggerManualScrape(daysBack: number = 7): Promise<AxiosResponse<any>> {\n    return api.post(`/system/scrape?days_back=${daysBack}`);\n  }\n\n  // Utility method to format API errors\n  formatError(error: any): string {\n    if (error.response?.data?.detail) {\n      return error.response.data.detail;\n    } else if (error.response?.data?.message) {\n      return error.response.data.message;\n    } else if (error.message) {\n      return error.message;\n    } else {\n      return 'An unexpected error occurred';\n    }\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;;AAE5C;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,iBAAAF,cAAA,GAAgBD,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIL,MAAM,CAACM,GAAG,EAAE,CAAC;EACzE,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACTL,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;EAC1C,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACK,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACTL,OAAO,CAACK,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;EAE3C,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB;IACA,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGL,KAAK,CAACG,QAAQ;IACvCR,OAAO,CAACK,KAAK,CAAC,aAAaI,MAAM,GAAG,EAAEC,IAAI,CAAC;EAC7C,CAAC,MAAM,IAAIL,KAAK,CAACT,OAAO,EAAE;IACxB;IACAI,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACT,OAAO,CAAC;EACvD,CAAC,MAAM;IACL;IACAI,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACM,OAAO,CAAC;EACtD;EAEA,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AA0HA;AACA,MAAMO,UAAU,CAAC;EACf;EACA,MAAMC,WAAWA,CAAA,EAAgC;IAC/C,OAAOvB,GAAG,CAACwB,GAAG,CAAC,SAAS,CAAC;EAC3B;;EAEA;EACA,MAAMC,eAAeA,CAACC,OAA2B,GAAG,CAAC,CAAC,EAAmD;IACvG,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpCC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,OAAOpC,GAAG,CAACwB,GAAG,CAAC,iBAAiBG,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;EACtD;;EAEA;EACA,MAAMC,mBAAmBA,CAACC,QAAiB,EAAEC,MAAe,EAA4C;IACtG,MAAMZ,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IACpC,IAAIU,QAAQ,EAAEX,MAAM,CAACQ,MAAM,CAAC,WAAW,EAAEG,QAAQ,CAAC;IAClD,IAAIC,MAAM,EAAEZ,MAAM,CAACQ,MAAM,CAAC,SAAS,EAAEI,MAAM,CAAC;IAE5C,OAAOvC,GAAG,CAACwB,GAAG,CAAC,sBAAsBG,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC3D;EAEA,MAAMI,eAAeA,CAACC,OAKrB,GAAG,CAAC,CAAC,EAA6C;IACjD,MAAMd,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpCC,MAAM,CAACC,OAAO,CAACW,OAAO,CAAC,CAACV,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,OAAOpC,GAAG,CAACwB,GAAG,CAAC,4BAA4BG,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;EACjE;;EAEA;EACA,MAAMM,sBAAsBA,CAC1BC,MAAc,EACdF,OAKC,GAAG,CAAC,CAAC,EAC2C;IACjD,MAAMd,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpCC,MAAM,CAACC,OAAO,CAACW,OAAO,CAAC,CAACV,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAChD,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACzDN,MAAM,CAACQ,MAAM,CAACH,GAAG,EAAEC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,OAAOpC,GAAG,CAACwB,GAAG,CAAC,cAAcmB,MAAM,iBAAiBhB,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;EAC1E;;EAEA;EACA,MAAMQ,MAAMA,CACVC,KAAa,EACbC,IAAkC,GAAG,KAAK,EAC1CC,KAAa,GAAG,EAAE,EACsB;IACxC,MAAMpB,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCoB,CAAC,EAAEH,KAAK;MACRC,IAAI;MACJC,KAAK,EAAEA,KAAK,CAACX,QAAQ,CAAC;IACxB,CAAC,CAAC;IAEF,OAAOpC,GAAG,CAACwB,GAAG,CAAC,WAAWG,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE,CAAC;EAChD;;EAEA;EACA,MAAMa,eAAeA,CAAA,EAAyC;IAC5D,OAAOjD,GAAG,CAACwB,GAAG,CAAC,gBAAgB,CAAC;EAClC;;EAEA;EACA,MAAM0B,mBAAmBA,CAACC,QAAgB,GAAG,CAAC,EAA+B;IAC3E,OAAOnD,GAAG,CAACoD,IAAI,CAAC,4BAA4BD,QAAQ,EAAE,CAAC;EACzD;;EAEA;EACAE,WAAWA,CAACtC,KAAU,EAAU;IAAA,IAAAuC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IAC9B,KAAAH,eAAA,GAAIvC,KAAK,CAACG,QAAQ,cAAAoC,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBlC,IAAI,cAAAmC,oBAAA,eAApBA,oBAAA,CAAsBG,MAAM,EAAE;MAChC,OAAO3C,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACsC,MAAM;IACnC,CAAC,MAAM,KAAAF,gBAAA,GAAIzC,KAAK,CAACG,QAAQ,cAAAsC,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpC,IAAI,cAAAqC,qBAAA,eAApBA,qBAAA,CAAsBpC,OAAO,EAAE;MACxC,OAAON,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACC,OAAO;IACpC,CAAC,MAAM,IAAIN,KAAK,CAACM,OAAO,EAAE;MACxB,OAAON,KAAK,CAACM,OAAO;IACtB,CAAC,MAAM;MACL,OAAO,8BAA8B;IACvC;EACF;AACF;;AAEA;AACA,OAAO,MAAMsC,UAAU,GAAG,IAAIrC,UAAU,CAAC,CAAC;AAC1C,eAAeqC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}