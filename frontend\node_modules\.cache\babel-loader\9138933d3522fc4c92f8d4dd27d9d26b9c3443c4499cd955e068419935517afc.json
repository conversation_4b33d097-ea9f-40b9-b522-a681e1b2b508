{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldSectionContainerProps = useFieldSectionContainerProps;\nvar React = _interopRequireWildcard(require(\"react\"));\n/**\n * Generate the props to pass to the container element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the container element of each section of the field.\n */\nfunction useFieldSectionContainerProps(parameters) {\n  const {\n    stateResponse: {\n      // Methods to update the states\n      setSelectedSections\n    },\n    internalPropsWithDefaults: {\n      disabled = false\n    }\n  } = parameters;\n  const createHandleClick = React.useCallback(sectionIndex => event => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call to this function is actually intended, or a side effect.\n    if (disabled || event.isDefaultPrevented()) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback(sectionIndex => ({\n    'data-sectionindex': sectionIndex,\n    onClick: createHandleClick(sectionIndex)\n  }), [createHandleClick]);\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "useFieldSectionContainerProps", "React", "parameters", "stateResponse", "setSelectedSections", "internalPropsWithDefaults", "disabled", "createHandleClick", "useCallback", "sectionIndex", "event", "isDefaultPrevented", "onClick"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldSectionContainerProps.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldSectionContainerProps = useFieldSectionContainerProps;\nvar React = _interopRequireWildcard(require(\"react\"));\n/**\n * Generate the props to pass to the container element of each section of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the container element of each section of the field.\n */\nfunction useFieldSectionContainerProps(parameters) {\n  const {\n    stateResponse: {\n      // Methods to update the states\n      setSelectedSections\n    },\n    internalPropsWithDefaults: {\n      disabled = false\n    }\n  } = parameters;\n  const createHandleClick = React.useCallback(sectionIndex => event => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call to this function is actually intended, or a side effect.\n    if (disabled || event.isDefaultPrevented()) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  }, [disabled, setSelectedSections]);\n  return React.useCallback(sectionIndex => ({\n    'data-sectionindex': sectionIndex,\n    onClick: createHandleClick(sectionIndex)\n  }), [createHandleClick]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,6BAA6B,GAAGA,6BAA6B;AACrE,IAAIC,KAAK,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,6BAA6BA,CAACE,UAAU,EAAE;EACjD,MAAM;IACJC,aAAa,EAAE;MACb;MACAC;IACF,CAAC;IACDC,yBAAyB,EAAE;MACzBC,QAAQ,GAAG;IACb;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,iBAAiB,GAAGN,KAAK,CAACO,WAAW,CAACC,YAAY,IAAIC,KAAK,IAAI;IACnE;IACA;IACA,IAAIJ,QAAQ,IAAII,KAAK,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC1C;IACF;IACAP,mBAAmB,CAACK,YAAY,CAAC;EACnC,CAAC,EAAE,CAACH,QAAQ,EAAEF,mBAAmB,CAAC,CAAC;EACnC,OAAOH,KAAK,CAACO,WAAW,CAACC,YAAY,KAAK;IACxC,mBAAmB,EAAEA,YAAY;IACjCG,OAAO,EAAEL,iBAAiB,CAACE,YAAY;EACzC,CAAC,CAAC,EAAE,CAACF,iBAAiB,CAAC,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}