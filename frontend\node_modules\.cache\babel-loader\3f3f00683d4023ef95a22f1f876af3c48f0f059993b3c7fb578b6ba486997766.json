{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _composeClasses = _interopRequireDefault(require(\"@mui/utils/composeClasses\"));\nvar _zeroStyled = require(\"../zero-styled\");\nvar _DefaultPropsProvider = require(\"../DefaultPropsProvider\");\nvar _dialogActionsClasses = require(\"./dialogActionsClasses\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return (0, _composeClasses.default)(slots, _dialogActionsClasses.getDialogActionsUtilityClass, classes);\n};\nconst DialogActionsRoot = (0, _zeroStyled.styled)('div', {\n  name: 'MuiDialogActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  justifyContent: 'flex-end',\n  flex: '0 0 auto',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disableSpacing,\n    style: {\n      '& > :not(style) ~ :not(style)': {\n        marginLeft: 8\n      }\n    }\n  }]\n});\nconst DialogActions = /*#__PURE__*/React.forwardRef(function DialogActions(inProps, ref) {\n  const props = (0, _DefaultPropsProvider.useDefaultProps)({\n    props: inProps,\n    name: 'MuiDialogActions'\n  });\n  const {\n    className,\n    disableSpacing = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    disableSpacing\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(DialogActionsRoot, {\n    className: (0, _clsx.default)(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: _propTypes.default.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: _propTypes.default.object,\n  /**\n   * @ignore\n   */\n  className: _propTypes.default.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: _propTypes.default.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object])\n} : void 0;\nvar _default = exports.default = DialogActions;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "React", "_propTypes", "_clsx", "_composeClasses", "_zeroStyled", "_DefaultPropsProvider", "_dialogActionsClasses", "_jsxRuntime", "useUtilityClasses", "ownerState", "classes", "disableSpacing", "slots", "root", "getDialogActionsUtilityClass", "DialogActionsRoot", "styled", "name", "slot", "overridesResolver", "props", "styles", "spacing", "display", "alignItems", "padding", "justifyContent", "flex", "variants", "style", "marginLeft", "DialogActions", "forwardRef", "inProps", "ref", "useDefaultProps", "className", "other", "jsx", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/DialogActions/DialogActions.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _composeClasses = _interopRequireDefault(require(\"@mui/utils/composeClasses\"));\nvar _zeroStyled = require(\"../zero-styled\");\nvar _DefaultPropsProvider = require(\"../DefaultPropsProvider\");\nvar _dialogActionsClasses = require(\"./dialogActionsClasses\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableSpacing\n  } = ownerState;\n  const slots = {\n    root: ['root', !disableSpacing && 'spacing']\n  };\n  return (0, _composeClasses.default)(slots, _dialogActionsClasses.getDialogActionsUtilityClass, classes);\n};\nconst DialogActionsRoot = (0, _zeroStyled.styled)('div', {\n  name: 'MuiDialogActions',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.disableSpacing && styles.spacing];\n  }\n})({\n  display: 'flex',\n  alignItems: 'center',\n  padding: 8,\n  justifyContent: 'flex-end',\n  flex: '0 0 auto',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disableSpacing,\n    style: {\n      '& > :not(style) ~ :not(style)': {\n        marginLeft: 8\n      }\n    }\n  }]\n});\nconst DialogActions = /*#__PURE__*/React.forwardRef(function DialogActions(inProps, ref) {\n  const props = (0, _DefaultPropsProvider.useDefaultProps)({\n    props: inProps,\n    name: 'MuiDialogActions'\n  });\n  const {\n    className,\n    disableSpacing = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    disableSpacing\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(DialogActionsRoot, {\n    className: (0, _clsx.default)(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogActions.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: _propTypes.default.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: _propTypes.default.object,\n  /**\n   * @ignore\n   */\n  className: _propTypes.default.string,\n  /**\n   * If `true`, the actions do not have additional margin.\n   * @default false\n   */\n  disableSpacing: _propTypes.default.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object])\n} : void 0;\nvar _default = exports.default = DialogActions;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACJ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIM,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,UAAU,GAAGT,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIS,KAAK,GAAGV,sBAAsB,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,IAAIU,eAAe,GAAGX,sBAAsB,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAClF,IAAIW,WAAW,GAAGX,OAAO,CAAC,gBAAgB,CAAC;AAC3C,IAAIY,qBAAqB,GAAGZ,OAAO,CAAC,yBAAyB,CAAC;AAC9D,IAAIa,qBAAqB,GAAGb,OAAO,CAAC,wBAAwB,CAAC;AAC7D,IAAIc,WAAW,GAAGd,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAMe,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACF,cAAc,IAAI,SAAS;EAC7C,CAAC;EACD,OAAO,CAAC,CAAC,EAAER,eAAe,CAACT,OAAO,EAAEkB,KAAK,EAAEN,qBAAqB,CAACQ,4BAA4B,EAAEJ,OAAO,CAAC;AACzG,CAAC;AACD,MAAMK,iBAAiB,GAAG,CAAC,CAAC,EAAEX,WAAW,CAACY,MAAM,EAAE,KAAK,EAAE;EACvDC,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,IAAI,EAAE,CAACJ,UAAU,CAACE,cAAc,IAAIU,MAAM,CAACC,OAAO,CAAC;EACpE;AACF,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,OAAO,EAAE,CAAC;EACVC,cAAc,EAAE,UAAU;EAC1BC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAE,CAAC;IACTR,KAAK,EAAEA,CAAC;MACNX;IACF,CAAC,KAAK,CAACA,UAAU,CAACE,cAAc;IAChCkB,KAAK,EAAE;MACL,+BAA+B,EAAE;QAC/BC,UAAU,EAAE;MACd;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,aAAa,GAAG,aAAa/B,KAAK,CAACgC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMd,KAAK,GAAG,CAAC,CAAC,EAAEf,qBAAqB,CAAC8B,eAAe,EAAE;IACvDf,KAAK,EAAEa,OAAO;IACdhB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJmB,SAAS;IACTzB,cAAc,GAAG,KAAK;IACtB,GAAG0B;EACL,CAAC,GAAGjB,KAAK;EACT,MAAMX,UAAU,GAAG;IACjB,GAAGW,KAAK;IACRT;EACF,CAAC;EACD,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAa,CAAC,CAAC,EAAEF,WAAW,CAAC+B,GAAG,EAAEvB,iBAAiB,EAAE;IAC1DqB,SAAS,EAAE,CAAC,CAAC,EAAElC,KAAK,CAACR,OAAO,EAAEgB,OAAO,CAACG,IAAI,EAAEuB,SAAS,CAAC;IACtD3B,UAAU,EAAEA,UAAU;IACtByB,GAAG,EAAEA,GAAG;IACR,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,aAAa,CAACW,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAE1C,UAAU,CAACP,OAAO,CAACkD,IAAI;EACjC;AACF;AACA;EACElC,OAAO,EAAET,UAAU,CAACP,OAAO,CAACmD,MAAM;EAClC;AACF;AACA;EACET,SAAS,EAAEnC,UAAU,CAACP,OAAO,CAACoD,MAAM;EACpC;AACF;AACA;AACA;EACEnC,cAAc,EAAEV,UAAU,CAACP,OAAO,CAACqD,IAAI;EACvC;AACF;AACA;EACEC,EAAE,EAAE/C,UAAU,CAACP,OAAO,CAACuD,SAAS,CAAC,CAAChD,UAAU,CAACP,OAAO,CAACwD,OAAO,CAACjD,UAAU,CAACP,OAAO,CAACuD,SAAS,CAAC,CAAChD,UAAU,CAACP,OAAO,CAACyD,IAAI,EAAElD,UAAU,CAACP,OAAO,CAACmD,MAAM,EAAE5C,UAAU,CAACP,OAAO,CAACqD,IAAI,CAAC,CAAC,CAAC,EAAE9C,UAAU,CAACP,OAAO,CAACyD,IAAI,EAAElD,UAAU,CAACP,OAAO,CAACmD,MAAM,CAAC;AAChO,CAAC,GAAG,KAAK,CAAC;AACV,IAAIO,QAAQ,GAAGtD,OAAO,CAACJ,OAAO,GAAGqC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}