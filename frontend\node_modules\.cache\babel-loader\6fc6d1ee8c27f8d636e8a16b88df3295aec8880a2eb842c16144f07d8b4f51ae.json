{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getTabScrollButtonUtilityClass = getTabScrollButtonUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getTabScrollButtonUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiTabScrollButton', slot);\n}\nconst tabScrollButtonClasses = (0, _generateUtilityClasses.default)('MuiTabScrollButton', ['root', 'vertical', 'horizontal', 'disabled']);\nvar _default = exports.default = tabScrollButtonClasses;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "getTabScrollButtonUtilityClass", "_generateUtilityClasses", "_generateUtilityClass", "slot", "tabScrollButtonClasses", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/TabScrollButton/tabScrollButtonClasses.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getTabScrollButtonUtilityClass = getTabScrollButtonUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getTabScrollButtonUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiTabScrollButton', slot);\n}\nconst tabScrollButtonClasses = (0, _generateUtilityClasses.default)('MuiTabScrollButton', ['root', 'vertical', 'horizontal', 'disabled']);\nvar _default = exports.default = tabScrollButtonClasses;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxBG,OAAO,CAACE,8BAA8B,GAAGA,8BAA8B;AACvE,IAAIC,uBAAuB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAClG,IAAIQ,qBAAqB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC9F,SAASM,8BAA8BA,CAACG,IAAI,EAAE;EAC5C,OAAO,CAAC,CAAC,EAAED,qBAAqB,CAACP,OAAO,EAAE,oBAAoB,EAAEQ,IAAI,CAAC;AACvE;AACA,MAAMC,sBAAsB,GAAG,CAAC,CAAC,EAAEH,uBAAuB,CAACN,OAAO,EAAE,oBAAoB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AACzI,IAAIU,QAAQ,GAAGP,OAAO,CAACH,OAAO,GAAGS,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}