{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.syncSelectionToDOM = syncSelectionToDOM;\nvar _utils = require(\"../../utils/utils\");\nfunction syncSelectionToDOM(parameters) {\n  const {\n    focused,\n    domGetters,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      state\n    }\n  } = parameters;\n  if (!domGetters.isReady()) {\n    return;\n  }\n  const selection = document.getSelection();\n  if (!selection) {\n    return;\n  }\n  if (parsedSelectedSections == null) {\n    // If the selection contains an element inside the field, we reset it.\n    if (selection.rangeCount > 0 && domGetters.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n      selection.removeAllRanges();\n    }\n    if (focused) {\n      domGetters.getRoot().blur();\n    }\n    return;\n  }\n\n  // On multi input range pickers we want to update selection range only for the active input\n  if (!domGetters.getRoot().contains((0, _utils.getActiveElement)(document))) {\n    return;\n  }\n  const range = new window.Range();\n  let target;\n  if (parsedSelectedSections === 'all') {\n    target = domGetters.getRoot();\n  } else {\n    const section = state.sections[parsedSelectedSections];\n    if (section.type === 'empty') {\n      target = domGetters.getSectionContainer(parsedSelectedSections);\n    } else {\n      target = domGetters.getSectionContent(parsedSelectedSections);\n    }\n  }\n  range.selectNodeContents(target);\n  target.focus();\n  selection.removeAllRanges();\n  selection.addRange(range);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "syncSelectionToDOM", "_utils", "require", "parameters", "focused", "domGetters", "stateResponse", "parsedSelectedSections", "state", "isReady", "selection", "document", "getSelection", "rangeCount", "getRoot", "contains", "getRangeAt", "startContainer", "removeAllRanges", "blur", "getActiveElement", "range", "window", "Range", "target", "section", "sections", "type", "getSectionContainer", "getSectionContent", "selectNodeContents", "focus", "addRange"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/syncSelectionToDOM.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.syncSelectionToDOM = syncSelectionToDOM;\nvar _utils = require(\"../../utils/utils\");\nfunction syncSelectionToDOM(parameters) {\n  const {\n    focused,\n    domGetters,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      state\n    }\n  } = parameters;\n  if (!domGetters.isReady()) {\n    return;\n  }\n  const selection = document.getSelection();\n  if (!selection) {\n    return;\n  }\n  if (parsedSelectedSections == null) {\n    // If the selection contains an element inside the field, we reset it.\n    if (selection.rangeCount > 0 && domGetters.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n      selection.removeAllRanges();\n    }\n    if (focused) {\n      domGetters.getRoot().blur();\n    }\n    return;\n  }\n\n  // On multi input range pickers we want to update selection range only for the active input\n  if (!domGetters.getRoot().contains((0, _utils.getActiveElement)(document))) {\n    return;\n  }\n  const range = new window.Range();\n  let target;\n  if (parsedSelectedSections === 'all') {\n    target = domGetters.getRoot();\n  } else {\n    const section = state.sections[parsedSelectedSections];\n    if (section.type === 'empty') {\n      target = domGetters.getSectionContainer(parsedSelectedSections);\n    } else {\n      target = domGetters.getSectionContent(parsedSelectedSections);\n    }\n  }\n  range.selectNodeContents(target);\n  target.focus();\n  selection.removeAllRanges();\n  selection.addRange(range);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIC,MAAM,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AACzC,SAASF,kBAAkBA,CAACG,UAAU,EAAE;EACtC,MAAM;IACJC,OAAO;IACPC,UAAU;IACVC,aAAa,EAAE;MACb;MACAC,sBAAsB;MACtBC;IACF;EACF,CAAC,GAAGL,UAAU;EACd,IAAI,CAACE,UAAU,CAACI,OAAO,CAAC,CAAC,EAAE;IACzB;EACF;EACA,MAAMC,SAAS,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC;EACzC,IAAI,CAACF,SAAS,EAAE;IACd;EACF;EACA,IAAIH,sBAAsB,IAAI,IAAI,EAAE;IAClC;IACA,IAAIG,SAAS,CAACG,UAAU,GAAG,CAAC,IAAIR,UAAU,CAACS,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACL,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,EAAE;MACrGP,SAAS,CAACQ,eAAe,CAAC,CAAC;IAC7B;IACA,IAAId,OAAO,EAAE;MACXC,UAAU,CAACS,OAAO,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;IAC7B;IACA;EACF;;EAEA;EACA,IAAI,CAACd,UAAU,CAACS,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAEd,MAAM,CAACmB,gBAAgB,EAAET,QAAQ,CAAC,CAAC,EAAE;IAC1E;EACF;EACA,MAAMU,KAAK,GAAG,IAAIC,MAAM,CAACC,KAAK,CAAC,CAAC;EAChC,IAAIC,MAAM;EACV,IAAIjB,sBAAsB,KAAK,KAAK,EAAE;IACpCiB,MAAM,GAAGnB,UAAU,CAACS,OAAO,CAAC,CAAC;EAC/B,CAAC,MAAM;IACL,MAAMW,OAAO,GAAGjB,KAAK,CAACkB,QAAQ,CAACnB,sBAAsB,CAAC;IACtD,IAAIkB,OAAO,CAACE,IAAI,KAAK,OAAO,EAAE;MAC5BH,MAAM,GAAGnB,UAAU,CAACuB,mBAAmB,CAACrB,sBAAsB,CAAC;IACjE,CAAC,MAAM;MACLiB,MAAM,GAAGnB,UAAU,CAACwB,iBAAiB,CAACtB,sBAAsB,CAAC;IAC/D;EACF;EACAc,KAAK,CAACS,kBAAkB,CAACN,MAAM,CAAC;EAChCA,MAAM,CAACO,KAAK,CAAC,CAAC;EACdrB,SAAS,CAACQ,eAAe,CAAC,CAAC;EAC3BR,SAAS,CAACsB,QAAQ,CAACX,KAAK,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}