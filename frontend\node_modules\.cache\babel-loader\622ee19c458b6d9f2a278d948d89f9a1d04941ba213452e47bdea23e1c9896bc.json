{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getTabUtilityClass = getTabUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getTabUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiTab', slot);\n}\nconst tabClasses = (0, _generateUtilityClasses.default)('MuiTab', ['root', 'labelIcon', 'textColorInherit', 'textColorPrimary', 'textColorSecondary', 'selected', 'disabled', 'fullWidth', 'wrapped', 'iconWrapper', 'icon']);\nvar _default = exports.default = tabClasses;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "getTabUtilityClass", "_generateUtilityClasses", "_generateUtilityClass", "slot", "tabClasses", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/Tab/tabClasses.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getTabUtilityClass = getTabUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getTabUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiTab', slot);\n}\nconst tabClasses = (0, _generateUtilityClasses.default)('MuiTab', ['root', 'labelIcon', 'textColorInherit', 'textColorPrimary', 'textColorSecondary', 'selected', 'disabled', 'fullWidth', 'wrapped', 'iconWrapper', 'icon']);\nvar _default = exports.default = tabClasses;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxBG,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIC,uBAAuB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAClG,IAAIQ,qBAAqB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC9F,SAASM,kBAAkBA,CAACG,IAAI,EAAE;EAChC,OAAO,CAAC,CAAC,EAAED,qBAAqB,CAACP,OAAO,EAAE,QAAQ,EAAEQ,IAAI,CAAC;AAC3D;AACA,MAAMC,UAAU,GAAG,CAAC,CAAC,EAAEH,uBAAuB,CAACN,OAAO,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;AAC7N,IAAIU,QAAQ,GAAGP,OAAO,CAACH,OAAO,GAAGS,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}