{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  tabClasses: true\n};\nObject.defineProperty(exports, \"default\", {\n  enumerable: true,\n  get: function () {\n    return _Tab.default;\n  }\n});\nObject.defineProperty(exports, \"tabClasses\", {\n  enumerable: true,\n  get: function () {\n    return _tabClasses.default;\n  }\n});\nvar _Tab = _interopRequireDefault(require(\"./Tab\"));\nvar _tabClasses = _interopRequireWildcard(require(\"./tabClasses\"));\nObject.keys(_tabClasses).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _tabClasses[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _tabClasses[key];\n    }\n  });\n});", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_exportNames", "tabClasses", "enumerable", "get", "_Tab", "_tabClasses", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/Tab/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  tabClasses: true\n};\nObject.defineProperty(exports, \"default\", {\n  enumerable: true,\n  get: function () {\n    return _Tab.default;\n  }\n});\nObject.defineProperty(exports, \"tabClasses\", {\n  enumerable: true,\n  get: function () {\n    return _tabClasses.default;\n  }\n});\nvar _Tab = _interopRequireDefault(require(\"./Tab\"));\nvar _tabClasses = _interopRequireWildcard(require(\"./tabClasses\"));\nObject.keys(_tabClasses).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _tabClasses[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _tabClasses[key];\n    }\n  });\n});"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,IAAIC,YAAY,GAAG;EACjBC,UAAU,EAAE;AACd,CAAC;AACDL,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,SAAS,EAAE;EACxCI,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOC,IAAI,CAACV,OAAO;EACrB;AACF,CAAC,CAAC;AACFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CI,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOE,WAAW,CAACX,OAAO;EAC5B;AACF,CAAC,CAAC;AACF,IAAIU,IAAI,GAAGT,sBAAsB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACnD,IAAIY,WAAW,GAAGb,uBAAuB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AAClEG,MAAM,CAACU,IAAI,CAACD,WAAW,CAAC,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;EAC9C,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,YAAY,EAAE;EAC/C,IAAIZ,MAAM,CAACa,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,YAAY,EAAEQ,GAAG,CAAC,EAAE;EAC7D,IAAIA,GAAG,IAAIV,OAAO,IAAIA,OAAO,CAACU,GAAG,CAAC,KAAKH,WAAW,CAACG,GAAG,CAAC,EAAE;EACzDZ,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEU,GAAG,EAAE;IAClCN,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACf,OAAOE,WAAW,CAACG,GAAG,CAAC;IACzB;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}