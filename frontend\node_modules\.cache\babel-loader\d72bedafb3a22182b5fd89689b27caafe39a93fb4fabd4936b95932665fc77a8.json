{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ScrollbarSize;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _debounce = _interopRequireDefault(require(\"../utils/debounce\"));\nvar _utils = require(\"../utils\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst styles = {\n  width: 99,\n  height: 99,\n  position: 'absolute',\n  top: -9999,\n  overflow: 'scroll'\n};\n\n/**\n * @ignore - internal component.\n * The component originates from https://github.com/STORIS/react-scrollbar-size.\n * It has been moved into the core in order to minimize the bundle size.\n */\nfunction ScrollbarSize(props) {\n  const {\n    onChange,\n    ...other\n  } = props;\n  const scrollbarHeight = React.useRef();\n  const nodeRef = React.useRef(null);\n  const setMeasurements = () => {\n    scrollbarHeight.current = nodeRef.current.offsetHeight - nodeRef.current.clientHeight;\n  };\n  (0, _utils.unstable_useEnhancedEffect)(() => {\n    const handleResize = (0, _debounce.default)(() => {\n      const prevHeight = scrollbarHeight.current;\n      setMeasurements();\n      if (prevHeight !== scrollbarHeight.current) {\n        onChange(scrollbarHeight.current);\n      }\n    });\n    const containerWindow = (0, _utils.ownerWindow)(nodeRef.current);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [onChange]);\n  React.useEffect(() => {\n    setMeasurements();\n    onChange(scrollbarHeight.current);\n  }, [onChange]);\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(\"div\", {\n    style: styles,\n    ...other,\n    ref: nodeRef\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ScrollbarSize.propTypes = {\n  onChange: _propTypes.default.func.isRequired\n} : void 0;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "ScrollbarSize", "React", "_propTypes", "_debounce", "_utils", "_jsxRuntime", "styles", "width", "height", "position", "top", "overflow", "props", "onChange", "other", "scrollbarHeight", "useRef", "nodeRef", "setMeasurements", "current", "offsetHeight", "clientHeight", "unstable_useEnhancedEffect", "handleResize", "prevHeight", "containerWindow", "ownerWindow", "addEventListener", "clear", "removeEventListener", "useEffect", "jsx", "style", "ref", "process", "env", "NODE_ENV", "propTypes", "func", "isRequired"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/Tabs/ScrollbarSize.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ScrollbarSize;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _debounce = _interopRequireDefault(require(\"../utils/debounce\"));\nvar _utils = require(\"../utils\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst styles = {\n  width: 99,\n  height: 99,\n  position: 'absolute',\n  top: -9999,\n  overflow: 'scroll'\n};\n\n/**\n * @ignore - internal component.\n * The component originates from https://github.com/STORIS/react-scrollbar-size.\n * It has been moved into the core in order to minimize the bundle size.\n */\nfunction ScrollbarSize(props) {\n  const {\n    onChange,\n    ...other\n  } = props;\n  const scrollbarHeight = React.useRef();\n  const nodeRef = React.useRef(null);\n  const setMeasurements = () => {\n    scrollbarHeight.current = nodeRef.current.offsetHeight - nodeRef.current.clientHeight;\n  };\n  (0, _utils.unstable_useEnhancedEffect)(() => {\n    const handleResize = (0, _debounce.default)(() => {\n      const prevHeight = scrollbarHeight.current;\n      setMeasurements();\n      if (prevHeight !== scrollbarHeight.current) {\n        onChange(scrollbarHeight.current);\n      }\n    });\n    const containerWindow = (0, _utils.ownerWindow)(nodeRef.current);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [onChange]);\n  React.useEffect(() => {\n    setMeasurements();\n    onChange(scrollbarHeight.current);\n  }, [onChange]);\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(\"div\", {\n    style: styles,\n    ...other,\n    ref: nodeRef\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ScrollbarSize.propTypes = {\n  onChange: _propTypes.default.func.isRequired\n} : void 0;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACJ,OAAO,GAAGM,aAAa;AAC/B,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,UAAU,GAAGV,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIU,SAAS,GAAGX,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACpE,IAAIW,MAAM,GAAGX,OAAO,CAAC,UAAU,CAAC;AAChC,IAAIY,WAAW,GAAGZ,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAMa,MAAM,GAAG;EACbC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,CAAC,IAAI;EACVC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASX,aAAaA,CAACY,KAAK,EAAE;EAC5B,MAAM;IACJC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGF,KAAK;EACT,MAAMG,eAAe,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC;EACtC,MAAMC,OAAO,GAAGhB,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EAClC,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BH,eAAe,CAACI,OAAO,GAAGF,OAAO,CAACE,OAAO,CAACC,YAAY,GAAGH,OAAO,CAACE,OAAO,CAACE,YAAY;EACvF,CAAC;EACD,CAAC,CAAC,EAAEjB,MAAM,CAACkB,0BAA0B,EAAE,MAAM;IAC3C,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAEpB,SAAS,CAACT,OAAO,EAAE,MAAM;MAChD,MAAM8B,UAAU,GAAGT,eAAe,CAACI,OAAO;MAC1CD,eAAe,CAAC,CAAC;MACjB,IAAIM,UAAU,KAAKT,eAAe,CAACI,OAAO,EAAE;QAC1CN,QAAQ,CAACE,eAAe,CAACI,OAAO,CAAC;MACnC;IACF,CAAC,CAAC;IACF,MAAMM,eAAe,GAAG,CAAC,CAAC,EAAErB,MAAM,CAACsB,WAAW,EAAET,OAAO,CAACE,OAAO,CAAC;IAChEM,eAAe,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACxD,OAAO,MAAM;MACXA,YAAY,CAACK,KAAK,CAAC,CAAC;MACpBH,eAAe,CAACI,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,CAACV,QAAQ,CAAC,CAAC;EACdZ,KAAK,CAAC6B,SAAS,CAAC,MAAM;IACpBZ,eAAe,CAAC,CAAC;IACjBL,QAAQ,CAACE,eAAe,CAACI,OAAO,CAAC;EACnC,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EACd,OAAO,aAAa,CAAC,CAAC,EAAER,WAAW,CAAC0B,GAAG,EAAE,KAAK,EAAE;IAC9CC,KAAK,EAAE1B,MAAM;IACb,GAAGQ,KAAK;IACRmB,GAAG,EAAEhB;EACP,CAAC,CAAC;AACJ;AACAiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpC,aAAa,CAACqC,SAAS,GAAG;EAChExB,QAAQ,EAAEX,UAAU,CAACR,OAAO,CAAC4C,IAAI,CAACC;AACpC,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}