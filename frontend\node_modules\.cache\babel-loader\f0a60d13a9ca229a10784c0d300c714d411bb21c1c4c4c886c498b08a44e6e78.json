{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MuiPickersAdapterContext = exports.LocalizationProvider = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _styles = require(\"@mui/material/styles\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"localeText\"];\nconst MuiPickersAdapterContext = exports.MuiPickersAdapterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") MuiPickersAdapterContext.displayName = \"MuiPickersAdapterContext\";\n/**\n * Demos:\n *\n * - [Date format and localization](https://mui.com/x/react-date-pickers/adapters-locale/)\n * - [Calendar systems](https://mui.com/x/react-date-pickers/calendar-systems/)\n * - [Translated components](https://mui.com/x/react-date-pickers/localization/)\n * - [UTC and timezones](https://mui.com/x/react-date-pickers/timezone/)\n *\n * API:\n *\n * - [LocalizationProvider API](https://mui.com/x/api/date-pickers/localization-provider/)\n */\nconst LocalizationProvider = exports.LocalizationProvider = function LocalizationProvider(inProps) {\n  const {\n      localeText: inLocaleText\n    } = inProps,\n    otherInProps = (0, _objectWithoutPropertiesLoose2.default)(inProps, _excluded);\n  const {\n    utils: parentUtils,\n    localeText: parentLocaleText\n  } = React.useContext(MuiPickersAdapterContext) ?? {\n    utils: undefined,\n    localeText: undefined\n  };\n  const props = (0, _styles.useThemeProps)({\n    // We don't want to pass the `localeText` prop to the theme, that way it will always return the theme value,\n    // We will then merge this theme value with our value manually\n    props: otherInProps,\n    name: 'MuiLocalizationProvider'\n  });\n  const {\n    children,\n    dateAdapter: DateAdapter,\n    dateFormats,\n    dateLibInstance,\n    adapterLocale,\n    localeText: themeLocaleText\n  } = props;\n  const localeText = React.useMemo(() => (0, _extends2.default)({}, themeLocaleText, parentLocaleText, inLocaleText), [themeLocaleText, parentLocaleText, inLocaleText]);\n  const utils = React.useMemo(() => {\n    if (!DateAdapter) {\n      if (parentUtils) {\n        return parentUtils;\n      }\n      return null;\n    }\n    const adapter = new DateAdapter({\n      locale: adapterLocale,\n      formats: dateFormats,\n      instance: dateLibInstance\n    });\n    if (!adapter.isMUIAdapter) {\n      throw new Error(['MUI X: The date adapter should be imported from `@mui/x-date-pickers` or `@mui/x-date-pickers-pro`, not from `@date-io`', \"For example, `import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'` instead of `import AdapterDayjs from '@date-io/dayjs'`\", 'More information on the installation documentation: https://mui.com/x/react-date-pickers/quickstart/#installation'].join(`\\n`));\n    }\n    return adapter;\n  }, [DateAdapter, adapterLocale, dateFormats, dateLibInstance, parentUtils]);\n  const defaultDates = React.useMemo(() => {\n    if (!utils) {\n      return null;\n    }\n    return {\n      minDate: utils.date('1900-01-01T00:00:00.000'),\n      maxDate: utils.date('2099-12-31T00:00:00.000')\n    };\n  }, [utils]);\n  const contextValue = React.useMemo(() => {\n    return {\n      utils,\n      defaultDates,\n      localeText\n    };\n  }, [defaultDates, utils, localeText]);\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(MuiPickersAdapterContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n};\nif (process.env.NODE_ENV !== \"production\") LocalizationProvider.displayName = \"LocalizationProvider\";\nprocess.env.NODE_ENV !== \"production\" ? LocalizationProvider.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Locale for the date library you are using\n   */\n  adapterLocale: _propTypes.default.any,\n  children: _propTypes.default.node,\n  /**\n   * Date library adapter class function.\n   * @see See the localization provider {@link https://mui.com/x/react-date-pickers/quickstart/#integrate-provider-and-adapter date adapter setup section} for more details.\n   */\n  dateAdapter: _propTypes.default.func,\n  /**\n   * Formats that are used for any child pickers\n   */\n  dateFormats: _propTypes.default.shape({\n    dayOfMonth: _propTypes.default.string,\n    dayOfMonthFull: _propTypes.default.string,\n    fullDate: _propTypes.default.string,\n    fullTime12h: _propTypes.default.string,\n    fullTime24h: _propTypes.default.string,\n    hours12h: _propTypes.default.string,\n    hours24h: _propTypes.default.string,\n    keyboardDate: _propTypes.default.string,\n    keyboardDateTime12h: _propTypes.default.string,\n    keyboardDateTime24h: _propTypes.default.string,\n    meridiem: _propTypes.default.string,\n    minutes: _propTypes.default.string,\n    month: _propTypes.default.string,\n    monthShort: _propTypes.default.string,\n    normalDate: _propTypes.default.string,\n    normalDateWithWeekday: _propTypes.default.string,\n    seconds: _propTypes.default.string,\n    shortDate: _propTypes.default.string,\n    weekday: _propTypes.default.string,\n    weekdayShort: _propTypes.default.string,\n    year: _propTypes.default.string\n  }),\n  /**\n   * Date library instance you are using, if it has some global overrides\n   * ```jsx\n   * dateLibInstance={momentTimeZone}\n   * ```\n   */\n  dateLibInstance: _propTypes.default.any,\n  /**\n   * Locale for components texts\n   */\n  localeText: _propTypes.default.object\n} : void 0;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "MuiPickersAdapterContext", "LocalizationProvider", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_propTypes", "_styles", "_jsxRuntime", "_excluded", "createContext", "process", "env", "NODE_ENV", "displayName", "inProps", "localeText", "inLocaleText", "otherInProps", "utils", "parentUtils", "parentLocaleText", "useContext", "undefined", "props", "useThemeProps", "name", "children", "dateAdapter", "DateAdapter", "dateFormats", "dateLibInstance", "adapterLocale", "themeLocaleText", "useMemo", "adapter", "locale", "formats", "instance", "isMUIAdapter", "Error", "join", "defaultDates", "minDate", "date", "maxDate", "contextValue", "jsx", "Provider", "propTypes", "any", "node", "func", "shape", "dayOfMonth", "string", "dayOfMonthFull", "fullDate", "fullTime12h", "fullTime24h", "hours12h", "hours24h", "keyboardDate", "keyboardDateTime12h", "keyboardDateTime24h", "meridiem", "minutes", "month", "monthShort", "normalDate", "normalDateWithWeekday", "seconds", "shortDate", "weekday", "weekdayShort", "year", "object"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MuiPickersAdapterContext = exports.LocalizationProvider = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _styles = require(\"@mui/material/styles\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"localeText\"];\nconst MuiPickersAdapterContext = exports.MuiPickersAdapterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") MuiPickersAdapterContext.displayName = \"MuiPickersAdapterContext\";\n/**\n * Demos:\n *\n * - [Date format and localization](https://mui.com/x/react-date-pickers/adapters-locale/)\n * - [Calendar systems](https://mui.com/x/react-date-pickers/calendar-systems/)\n * - [Translated components](https://mui.com/x/react-date-pickers/localization/)\n * - [UTC and timezones](https://mui.com/x/react-date-pickers/timezone/)\n *\n * API:\n *\n * - [LocalizationProvider API](https://mui.com/x/api/date-pickers/localization-provider/)\n */\nconst LocalizationProvider = exports.LocalizationProvider = function LocalizationProvider(inProps) {\n  const {\n      localeText: inLocaleText\n    } = inProps,\n    otherInProps = (0, _objectWithoutPropertiesLoose2.default)(inProps, _excluded);\n  const {\n    utils: parentUtils,\n    localeText: parentLocaleText\n  } = React.useContext(MuiPickersAdapterContext) ?? {\n    utils: undefined,\n    localeText: undefined\n  };\n  const props = (0, _styles.useThemeProps)({\n    // We don't want to pass the `localeText` prop to the theme, that way it will always return the theme value,\n    // We will then merge this theme value with our value manually\n    props: otherInProps,\n    name: 'MuiLocalizationProvider'\n  });\n  const {\n    children,\n    dateAdapter: DateAdapter,\n    dateFormats,\n    dateLibInstance,\n    adapterLocale,\n    localeText: themeLocaleText\n  } = props;\n  const localeText = React.useMemo(() => (0, _extends2.default)({}, themeLocaleText, parentLocaleText, inLocaleText), [themeLocaleText, parentLocaleText, inLocaleText]);\n  const utils = React.useMemo(() => {\n    if (!DateAdapter) {\n      if (parentUtils) {\n        return parentUtils;\n      }\n      return null;\n    }\n    const adapter = new DateAdapter({\n      locale: adapterLocale,\n      formats: dateFormats,\n      instance: dateLibInstance\n    });\n    if (!adapter.isMUIAdapter) {\n      throw new Error(['MUI X: The date adapter should be imported from `@mui/x-date-pickers` or `@mui/x-date-pickers-pro`, not from `@date-io`', \"For example, `import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'` instead of `import AdapterDayjs from '@date-io/dayjs'`\", 'More information on the installation documentation: https://mui.com/x/react-date-pickers/quickstart/#installation'].join(`\\n`));\n    }\n    return adapter;\n  }, [DateAdapter, adapterLocale, dateFormats, dateLibInstance, parentUtils]);\n  const defaultDates = React.useMemo(() => {\n    if (!utils) {\n      return null;\n    }\n    return {\n      minDate: utils.date('1900-01-01T00:00:00.000'),\n      maxDate: utils.date('2099-12-31T00:00:00.000')\n    };\n  }, [utils]);\n  const contextValue = React.useMemo(() => {\n    return {\n      utils,\n      defaultDates,\n      localeText\n    };\n  }, [defaultDates, utils, localeText]);\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(MuiPickersAdapterContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n};\nif (process.env.NODE_ENV !== \"production\") LocalizationProvider.displayName = \"LocalizationProvider\";\nprocess.env.NODE_ENV !== \"production\" ? LocalizationProvider.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Locale for the date library you are using\n   */\n  adapterLocale: _propTypes.default.any,\n  children: _propTypes.default.node,\n  /**\n   * Date library adapter class function.\n   * @see See the localization provider {@link https://mui.com/x/react-date-pickers/quickstart/#integrate-provider-and-adapter date adapter setup section} for more details.\n   */\n  dateAdapter: _propTypes.default.func,\n  /**\n   * Formats that are used for any child pickers\n   */\n  dateFormats: _propTypes.default.shape({\n    dayOfMonth: _propTypes.default.string,\n    dayOfMonthFull: _propTypes.default.string,\n    fullDate: _propTypes.default.string,\n    fullTime12h: _propTypes.default.string,\n    fullTime24h: _propTypes.default.string,\n    hours12h: _propTypes.default.string,\n    hours24h: _propTypes.default.string,\n    keyboardDate: _propTypes.default.string,\n    keyboardDateTime12h: _propTypes.default.string,\n    keyboardDateTime24h: _propTypes.default.string,\n    meridiem: _propTypes.default.string,\n    minutes: _propTypes.default.string,\n    month: _propTypes.default.string,\n    monthShort: _propTypes.default.string,\n    normalDate: _propTypes.default.string,\n    normalDateWithWeekday: _propTypes.default.string,\n    seconds: _propTypes.default.string,\n    shortDate: _propTypes.default.string,\n    weekday: _propTypes.default.string,\n    weekdayShort: _propTypes.default.string,\n    year: _propTypes.default.string\n  }),\n  /**\n   * Date library instance you are using, if it has some global overrides\n   * ```jsx\n   * dateLibInstance={momentTimeZone}\n   * ```\n   */\n  dateLibInstance: _propTypes.default.any,\n  /**\n   * Locale for components texts\n   */\n  localeText: _propTypes.default.object\n} : void 0;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,wBAAwB,GAAGF,OAAO,CAACG,oBAAoB,GAAG,KAAK,CAAC;AACxE,IAAIC,SAAS,GAAGV,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIU,8BAA8B,GAAGX,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIW,KAAK,GAAGT,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIY,UAAU,GAAGb,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIa,OAAO,GAAGb,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIc,WAAW,GAAGd,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAMe,SAAS,GAAG,CAAC,YAAY,CAAC;AAChC,MAAMR,wBAAwB,GAAGF,OAAO,CAACE,wBAAwB,GAAG,aAAaI,KAAK,CAACK,aAAa,CAAC,IAAI,CAAC;AAC1G,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEZ,wBAAwB,CAACa,WAAW,GAAG,0BAA0B;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,oBAAoB,GAAGH,OAAO,CAACG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACa,OAAO,EAAE;EACjG,MAAM;MACFC,UAAU,EAAEC;IACd,CAAC,GAAGF,OAAO;IACXG,YAAY,GAAG,CAAC,CAAC,EAAEd,8BAA8B,CAACT,OAAO,EAAEoB,OAAO,EAAEN,SAAS,CAAC;EAChF,MAAM;IACJU,KAAK,EAAEC,WAAW;IAClBJ,UAAU,EAAEK;EACd,CAAC,GAAGhB,KAAK,CAACiB,UAAU,CAACrB,wBAAwB,CAAC,IAAI;IAChDkB,KAAK,EAAEI,SAAS;IAChBP,UAAU,EAAEO;EACd,CAAC;EACD,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEjB,OAAO,CAACkB,aAAa,EAAE;IACvC;IACA;IACAD,KAAK,EAAEN,YAAY;IACnBQ,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJC,QAAQ;IACRC,WAAW,EAAEC,WAAW;IACxBC,WAAW;IACXC,eAAe;IACfC,aAAa;IACbhB,UAAU,EAAEiB;EACd,CAAC,GAAGT,KAAK;EACT,MAAMR,UAAU,GAAGX,KAAK,CAAC6B,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE/B,SAAS,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEsC,eAAe,EAAEZ,gBAAgB,EAAEJ,YAAY,CAAC,EAAE,CAACgB,eAAe,EAAEZ,gBAAgB,EAAEJ,YAAY,CAAC,CAAC;EACtK,MAAME,KAAK,GAAGd,KAAK,CAAC6B,OAAO,CAAC,MAAM;IAChC,IAAI,CAACL,WAAW,EAAE;MAChB,IAAIT,WAAW,EAAE;QACf,OAAOA,WAAW;MACpB;MACA,OAAO,IAAI;IACb;IACA,MAAMe,OAAO,GAAG,IAAIN,WAAW,CAAC;MAC9BO,MAAM,EAAEJ,aAAa;MACrBK,OAAO,EAAEP,WAAW;MACpBQ,QAAQ,EAAEP;IACZ,CAAC,CAAC;IACF,IAAI,CAACI,OAAO,CAACI,YAAY,EAAE;MACzB,MAAM,IAAIC,KAAK,CAAC,CAAC,yHAAyH,EAAE,uIAAuI,EAAE,mHAAmH,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvZ;IACA,OAAON,OAAO;EAChB,CAAC,EAAE,CAACN,WAAW,EAAEG,aAAa,EAAEF,WAAW,EAAEC,eAAe,EAAEX,WAAW,CAAC,CAAC;EAC3E,MAAMsB,YAAY,GAAGrC,KAAK,CAAC6B,OAAO,CAAC,MAAM;IACvC,IAAI,CAACf,KAAK,EAAE;MACV,OAAO,IAAI;IACb;IACA,OAAO;MACLwB,OAAO,EAAExB,KAAK,CAACyB,IAAI,CAAC,yBAAyB,CAAC;MAC9CC,OAAO,EAAE1B,KAAK,CAACyB,IAAI,CAAC,yBAAyB;IAC/C,CAAC;EACH,CAAC,EAAE,CAACzB,KAAK,CAAC,CAAC;EACX,MAAM2B,YAAY,GAAGzC,KAAK,CAAC6B,OAAO,CAAC,MAAM;IACvC,OAAO;MACLf,KAAK;MACLuB,YAAY;MACZ1B;IACF,CAAC;EACH,CAAC,EAAE,CAAC0B,YAAY,EAAEvB,KAAK,EAAEH,UAAU,CAAC,CAAC;EACrC,OAAO,aAAa,CAAC,CAAC,EAAER,WAAW,CAACuC,GAAG,EAAE9C,wBAAwB,CAAC+C,QAAQ,EAAE;IAC1EhD,KAAK,EAAE8C,YAAY;IACnBnB,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ,CAAC;AACD,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEX,oBAAoB,CAACY,WAAW,GAAG,sBAAsB;AACpGH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,oBAAoB,CAAC+C,SAAS,GAAG;EACvE;EACA;EACA;EACA;EACA;AACF;AACA;EACEjB,aAAa,EAAE1B,UAAU,CAACX,OAAO,CAACuD,GAAG;EACrCvB,QAAQ,EAAErB,UAAU,CAACX,OAAO,CAACwD,IAAI;EACjC;AACF;AACA;AACA;EACEvB,WAAW,EAAEtB,UAAU,CAACX,OAAO,CAACyD,IAAI;EACpC;AACF;AACA;EACEtB,WAAW,EAAExB,UAAU,CAACX,OAAO,CAAC0D,KAAK,CAAC;IACpCC,UAAU,EAAEhD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACrCC,cAAc,EAAElD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACzCE,QAAQ,EAAEnD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACnCG,WAAW,EAAEpD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACtCI,WAAW,EAAErD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACtCK,QAAQ,EAAEtD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACnCM,QAAQ,EAAEvD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACnCO,YAAY,EAAExD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACvCQ,mBAAmB,EAAEzD,UAAU,CAACX,OAAO,CAAC4D,MAAM;IAC9CS,mBAAmB,EAAE1D,UAAU,CAACX,OAAO,CAAC4D,MAAM;IAC9CU,QAAQ,EAAE3D,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACnCW,OAAO,EAAE5D,UAAU,CAACX,OAAO,CAAC4D,MAAM;IAClCY,KAAK,EAAE7D,UAAU,CAACX,OAAO,CAAC4D,MAAM;IAChCa,UAAU,EAAE9D,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACrCc,UAAU,EAAE/D,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACrCe,qBAAqB,EAAEhE,UAAU,CAACX,OAAO,CAAC4D,MAAM;IAChDgB,OAAO,EAAEjE,UAAU,CAACX,OAAO,CAAC4D,MAAM;IAClCiB,SAAS,EAAElE,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACpCkB,OAAO,EAAEnE,UAAU,CAACX,OAAO,CAAC4D,MAAM;IAClCmB,YAAY,EAAEpE,UAAU,CAACX,OAAO,CAAC4D,MAAM;IACvCoB,IAAI,EAAErE,UAAU,CAACX,OAAO,CAAC4D;EAC3B,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;EACExB,eAAe,EAAEzB,UAAU,CAACX,OAAO,CAACuD,GAAG;EACvC;AACF;AACA;EACElC,UAAU,EAAEV,UAAU,CAACX,OAAO,CAACiF;AACjC,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}