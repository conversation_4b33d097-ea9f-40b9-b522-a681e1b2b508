"""
Automated Scheduler for NSE Insider Trading Data Scraper
Features:
- Scheduled execution every 2 minutes during market hours
- Health monitoring and alerting
- Graceful error handling and recovery
- Configuration management
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime, time, timezone
from typing import Optional
from pathlib import Path
import json
import threading
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR

# Import our scraper
from enhanced_scraper import EnhancedNSEScraper, ScrapingResult

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScraperScheduler:
    """Automated scheduler for NSE insider trading data scraper"""
    
    def __init__(self, config_file: str = "config/scheduler_config.json"):
        self.config_file = config_file
        self.scheduler = AsyncIOScheduler()
        self.scraper = EnhancedNSEScraper()
        self.is_running = False
        self.config = self.load_config()
        
        # Ensure directories exist
        Path("logs").mkdir(exist_ok=True)
        Path("config").mkdir(exist_ok=True)
        
        # Setup scheduler event listeners
        self.scheduler.add_listener(self.job_executed_listener, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self.job_error_listener, EVENT_JOB_ERROR)
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def load_config(self) -> dict:
        """Load scheduler configuration"""
        default_config = {
            "scraping": {
                "interval_minutes": 2,
                "market_hours_only": True,
                "market_start_time": "09:15",
                "market_end_time": "15:30",
                "timezone": "Asia/Kolkata",
                "weekdays_only": True
            },
            "health_check": {
                "enabled": True,
                "interval_minutes": 60,
                "max_consecutive_failures": 3
            },
            "alerts": {
                "enabled": False,
                "email_recipients": [],
                "webhook_url": None
            },
            "data_retention": {
                "log_retention_days": 30,
                "cleanup_enabled": True
            }
        }
        
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                        elif isinstance(value, dict):
                            for subkey, subvalue in value.items():
                                if subkey not in config[key]:
                                    config[key][subkey] = subvalue
                    return config
            else:
                # Create default config file
                with open(self.config_file, 'w') as f:
                    json.dump(default_config, f, indent=2)
                logger.info(f"Created default config file: {self.config_file}")
                return default_config
                
        except Exception as e:
            logger.error(f"Failed to load config, using defaults: {e}")
            return default_config
    
    def is_market_hours(self) -> bool:
        """Check if current time is within market hours"""
        if not self.config["scraping"]["market_hours_only"]:
            return True
        
        now = datetime.now()
        
        # Check if weekday (Monday=0, Sunday=6)
        if self.config["scraping"]["weekdays_only"] and now.weekday() >= 5:
            return False
        
        # Check time range
        market_start = time.fromisoformat(self.config["scraping"]["market_start_time"])
        market_end = time.fromisoformat(self.config["scraping"]["market_end_time"])
        current_time = now.time()
        
        return market_start <= current_time <= market_end
    
    async def scheduled_scrape(self):
        """Execute scheduled scraping job"""
        job_id = f"scrape_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            logger.info(f"Starting scheduled scrape job: {job_id}")
            
            # Check if we should run during market hours
            if not self.is_market_hours():
                logger.info("Outside market hours, skipping scrape")
                return
            
            # Perform incremental scrape
            result = self.scraper.incremental_scrape()
            
            if result.success:
                logger.info(f"Scheduled scrape completed successfully: {result.records_inserted} new records")
                await self.send_success_notification(result)
            else:
                logger.error(f"Scheduled scrape failed: {result.error_message}")
                await self.send_failure_notification(result)
                
        except Exception as e:
            logger.error(f"Scheduled scrape job {job_id} failed with exception: {e}")
            await self.send_failure_notification(ScrapingResult(
                success=False,
                error_message=str(e)
            ))
    
    async def health_check(self):
        """Perform health check of the scraping system"""
        try:
            logger.info("Performing health check...")
            
            # Check database connectivity
            from database.connection import db_manager
            conn = db_manager.get_sync_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                logger.info("Database connectivity: OK")
            finally:
                db_manager.return_sync_connection(conn)
            
            # Check recent scraper performance
            conn = db_manager.get_sync_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT status, COUNT(*) 
                        FROM scraper_logs 
                        WHERE execution_start > NOW() - INTERVAL '24 hours'
                        GROUP BY status
                    """)
                    results = cursor.fetchall()
                    
                    status_counts = dict(results)
                    failed_count = status_counts.get('FAILED', 0)
                    success_count = status_counts.get('SUCCESS', 0)
                    
                    if failed_count > self.config["health_check"]["max_consecutive_failures"]:
                        logger.warning(f"High failure rate detected: {failed_count} failures in last 24 hours")
                        await self.send_health_alert(f"High failure rate: {failed_count} failures, {success_count} successes")
                    else:
                        logger.info(f"Health check passed: {success_count} successes, {failed_count} failures in last 24 hours")
                        
            finally:
                db_manager.return_sync_connection(conn)
                
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            await self.send_health_alert(f"Health check failed: {str(e)}")
    
    async def cleanup_old_logs(self):
        """Clean up old log entries"""
        if not self.config["data_retention"]["cleanup_enabled"]:
            return
        
        try:
            retention_days = self.config["data_retention"]["log_retention_days"]
            logger.info(f"Cleaning up logs older than {retention_days} days")
            
            from database.connection import db_manager
            conn = db_manager.get_sync_connection()
            try:
                with conn.cursor() as cursor:
                    # Clean scraper logs
                    cursor.execute("""
                        DELETE FROM scraper_logs 
                        WHERE execution_start < NOW() - INTERVAL '%s days'
                    """, (retention_days,))
                    
                    scraper_deleted = cursor.rowcount
                    
                    # Clean API usage logs
                    cursor.execute("""
                        DELETE FROM api_usage_log 
                        WHERE created_at < NOW() - INTERVAL '%s days'
                    """, (retention_days,))
                    
                    api_deleted = cursor.rowcount
                    conn.commit()
                    
                    logger.info(f"Cleanup completed: {scraper_deleted} scraper logs, {api_deleted} API logs deleted")
                    
            finally:
                db_manager.return_sync_connection(conn)
                
        except Exception as e:
            logger.error(f"Log cleanup failed: {e}")
    
    async def send_success_notification(self, result: ScrapingResult):
        """Send success notification"""
        if not self.config["alerts"]["enabled"]:
            return
        
        message = f"NSE Scraper Success: {result.records_inserted} new records inserted"
        await self.send_notification(message, "success")
    
    async def send_failure_notification(self, result: ScrapingResult):
        """Send failure notification"""
        if not self.config["alerts"]["enabled"]:
            return
        
        message = f"NSE Scraper Failed: {result.error_message}"
        await self.send_notification(message, "error")
    
    async def send_health_alert(self, message: str):
        """Send health alert notification"""
        if not self.config["alerts"]["enabled"]:
            return
        
        await self.send_notification(f"NSE Scraper Health Alert: {message}", "warning")
    
    async def send_notification(self, message: str, level: str):
        """Send notification via configured channels"""
        try:
            # Log the notification
            logger.info(f"Notification ({level}): {message}")
            
            # TODO: Implement email notifications
            # TODO: Implement webhook notifications
            # TODO: Implement Slack/Teams notifications
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
    
    def job_executed_listener(self, event):
        """Handle job execution events"""
        logger.debug(f"Job {event.job_id} executed successfully")
    
    def job_error_listener(self, event):
        """Handle job error events"""
        logger.error(f"Job {event.job_id} failed: {event.exception}")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    def start(self):
        """Start the scheduler"""
        try:
            logger.info("Starting NSE Insider Trading Data Scraper Scheduler")
            
            # Add scraping job
            scraping_interval = self.config["scraping"]["interval_minutes"]
            self.scheduler.add_job(
                self.scheduled_scrape,
                trigger=IntervalTrigger(minutes=scraping_interval),
                id='scrape_job',
                name='NSE Insider Trading Data Scrape',
                max_instances=1,
                coalesce=True
            )
            
            # Add health check job
            if self.config["health_check"]["enabled"]:
                health_interval = self.config["health_check"]["interval_minutes"]
                self.scheduler.add_job(
                    self.health_check,
                    trigger=IntervalTrigger(minutes=health_interval),
                    id='health_check_job',
                    name='Health Check',
                    max_instances=1
                )
            
            # Add cleanup job (daily at 2 AM)
            if self.config["data_retention"]["cleanup_enabled"]:
                self.scheduler.add_job(
                    self.cleanup_old_logs,
                    trigger=CronTrigger(hour=2, minute=0),
                    id='cleanup_job',
                    name='Log Cleanup',
                    max_instances=1
                )
            
            # Start scheduler
            self.scheduler.start()
            self.is_running = True
            
            logger.info(f"Scheduler started with {scraping_interval}-minute intervals")
            logger.info("Press Ctrl+C to stop the scheduler")
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            raise
    
    def stop(self):
        """Stop the scheduler"""
        if self.is_running:
            logger.info("Stopping scheduler...")
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            logger.info("Scheduler stopped")
    
    async def run_forever(self):
        """Run the scheduler indefinitely"""
        self.start()
        
        try:
            # Keep the event loop running
            while self.is_running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        finally:
            self.stop()

async def main():
    """Main entry point"""
    scheduler = ScraperScheduler()
    await scheduler.run_forever()

if __name__ == "__main__":
    asyncio.run(main())
