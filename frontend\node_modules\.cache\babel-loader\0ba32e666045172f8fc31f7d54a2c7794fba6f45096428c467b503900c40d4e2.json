{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  tabsClasses: true\n};\nObject.defineProperty(exports, \"default\", {\n  enumerable: true,\n  get: function () {\n    return _Tabs.default;\n  }\n});\nObject.defineProperty(exports, \"tabsClasses\", {\n  enumerable: true,\n  get: function () {\n    return _tabsClasses.default;\n  }\n});\nvar _Tabs = _interopRequireDefault(require(\"./Tabs\"));\nvar _tabsClasses = _interopRequireWildcard(require(\"./tabsClasses\"));\nObject.keys(_tabsClasses).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _tabsClasses[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _tabsClasses[key];\n    }\n  });\n});", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "_exportNames", "tabsClasses", "enumerable", "get", "_Tabs", "_tabsClasses", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/Tabs/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  tabsClasses: true\n};\nObject.defineProperty(exports, \"default\", {\n  enumerable: true,\n  get: function () {\n    return _Tabs.default;\n  }\n});\nObject.defineProperty(exports, \"tabsClasses\", {\n  enumerable: true,\n  get: function () {\n    return _tabsClasses.default;\n  }\n});\nvar _Tabs = _interopRequireDefault(require(\"./Tabs\"));\nvar _tabsClasses = _interopRequireWildcard(require(\"./tabsClasses\"));\nObject.keys(_tabsClasses).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _tabsClasses[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _tabsClasses[key];\n    }\n  });\n});"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,IAAIC,YAAY,GAAG;EACjBC,WAAW,EAAE;AACf,CAAC;AACDL,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,SAAS,EAAE;EACxCI,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOC,KAAK,CAACV,OAAO;EACtB;AACF,CAAC,CAAC;AACFE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,aAAa,EAAE;EAC5CI,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOE,YAAY,CAACX,OAAO;EAC7B;AACF,CAAC,CAAC;AACF,IAAIU,KAAK,GAAGT,sBAAsB,CAACF,OAAO,CAAC,QAAQ,CAAC,CAAC;AACrD,IAAIY,YAAY,GAAGb,uBAAuB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AACpEG,MAAM,CAACU,IAAI,CAACD,YAAY,CAAC,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;EAC/C,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,YAAY,EAAE;EAC/C,IAAIZ,MAAM,CAACa,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,YAAY,EAAEQ,GAAG,CAAC,EAAE;EAC7D,IAAIA,GAAG,IAAIV,OAAO,IAAIA,OAAO,CAACU,GAAG,CAAC,KAAKH,YAAY,CAACG,GAAG,CAAC,EAAE;EAC1DZ,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEU,GAAG,EAAE;IAClCN,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACf,OAAOE,YAAY,CAACG,GAAG,CAAC;IAC1B;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}