{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\pages\\\\Analytics.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CircularProgress, Alert } from '@mui/material';\nimport { apiService } from '../services/apiService';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport StatCard from '../components/StatCard';\nimport { TrendingUp, TrendingDown, Business } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Analytics = () => {\n  _s();\n  const [summary, setSummary] = useState(null);\n  const [topCompanies, setTopCompanies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchAnalyticsData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const [summaryResponse, companiesResponse] = await Promise.all([apiService.getAnalyticsSummary(), apiService.getTopCompanies({\n          limit: 20\n        })]);\n        setSummary(summaryResponse.data);\n        setTopCompanies(companiesResponse.data);\n      } catch (err) {\n        console.error('Error fetching analytics data:', err);\n        setError('Failed to load analytics data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAnalyticsData();\n  }, []);\n  const formatCurrency = value => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  const netValue = summary ? summary.net_value : 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Detailed analysis of insider trading patterns and trends\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), summary && /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Buy Value\",\n          value: formatCurrency(summary.total_buy_value),\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 21\n          }, this),\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Sell Value\",\n          value: formatCurrency(summary.total_sell_value),\n          icon: /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 21\n          }, this),\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Net Value\",\n          value: formatCurrency(Math.abs(netValue)),\n          subtitle: netValue >= 0 ? 'Net Buying' : 'Net Selling',\n          icon: netValue >= 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 37\n          }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 54\n          }, this),\n          color: netValue >= 0 ? 'success' : 'error'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Active Companies\",\n          value: summary.unique_companies.toLocaleString(),\n          icon: /*#__PURE__*/_jsxDEV(Business, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 21\n          }, this),\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          lg: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Transaction Trends (Last 30 Days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TransactionTrendsChart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          lg: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Top Companies by Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                height: 400,\n                overflow: 'auto'\n              },\n              children: topCompanies.slice(0, 10).map((company, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  p: 1,\n                  borderBottom: index < 9 ? '1px solid' : 'none',\n                  borderBottomColor: 'divider'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: 600,\n                    children: company.symbol\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [company.transaction_count, \" transactions\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: 600,\n                  color: \"primary.main\",\n                  children: formatCurrency(company.total_value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this)]\n              }, company.symbol, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Top 10 Companies by Transaction Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TopCompaniesChart, {\n              data: topCompanies\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"mVTJgXm8t0M807usrZXweZsWxBY=\");\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "apiService", "TopCompaniesChart", "TransactionTrendsChart", "StatCard", "TrendingUp", "TrendingDown", "Business", "jsxDEV", "_jsxDEV", "Analytics", "_s", "summary", "set<PERSON>ummary", "topCompanies", "setTopCompanies", "loading", "setLoading", "error", "setError", "fetchAnalyticsData", "summaryResponse", "companiesResponse", "Promise", "all", "getAnalyticsSummary", "getTopCompanies", "limit", "data", "err", "console", "formatCurrency", "value", "undefined", "isNaN", "absValue", "Math", "abs", "toFixed", "toLocaleString", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "severity", "netValue", "net_value", "mb", "variant", "gutterBottom", "color", "container", "spacing", "sx", "xs", "sm", "md", "title", "total_buy_value", "icon", "total_sell_value", "subtitle", "unique_companies", "lg", "height", "overflow", "slice", "map", "company", "index", "borderBottom", "borderBottomColor", "fontWeight", "symbol", "transaction_count", "total_value", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Analytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n} from '@mui/material';\nimport { apiService } from '../services/apiService';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport StatCard from '../components/StatCard';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Business,\n  Person,\n} from '@mui/icons-material';\n\nconst Analytics: React.FC = () => {\n  const [summary, setSummary] = useState<any>(null);\n  const [topCompanies, setTopCompanies] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchAnalyticsData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const [summaryResponse, companiesResponse] = await Promise.all([\n          apiService.getAnalyticsSummary(),\n          apiService.getTopCompanies({ limit: 20 }),\n        ]);\n\n        setSummary(summaryResponse.data);\n        setTopCompanies(companiesResponse.data);\n      } catch (err) {\n        console.error('Error fetching analytics data:', err);\n        setError('Failed to load analytics data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchAnalyticsData();\n  }, []);\n\n  const formatCurrency = (value: number | null | undefined) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  const netValue = summary ? summary.net_value : 0;\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Analytics\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Detailed analysis of insider trading patterns and trends\n        </Typography>\n      </Box>\n\n      {/* Summary Stats */}\n      {summary && (\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Total Buy Value\"\n              value={formatCurrency(summary.total_buy_value)}\n              icon={<TrendingUp />}\n              color=\"success\"\n            />\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Total Sell Value\"\n              value={formatCurrency(summary.total_sell_value)}\n              icon={<TrendingDown />}\n              color=\"error\"\n            />\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Net Value\"\n              value={formatCurrency(Math.abs(netValue))}\n              subtitle={netValue >= 0 ? 'Net Buying' : 'Net Selling'}\n              icon={netValue >= 0 ? <TrendingUp /> : <TrendingDown />}\n              color={netValue >= 0 ? 'success' : 'error'}\n            />\n          </Grid>\n          <Grid size={{ xs: 12, sm: 6, md: 3 }}>\n            <StatCard\n              title=\"Active Companies\"\n              value={summary.unique_companies.toLocaleString()}\n              icon={<Business />}\n              color=\"info\"\n            />\n          </Grid>\n        </Grid>\n      )}\n\n      {/* Charts */}\n      <Grid container spacing={3}>\n        {/* Transaction Trends */}\n        <Grid size={{ xs: 12, lg: 8 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Transaction Trends (Last 30 Days)\n              </Typography>\n              <TransactionTrendsChart />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Top Companies */}\n        <Grid size={{ xs: 12, lg: 4 }}>\n          <Card sx={{ height: '100%' }}>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top Companies by Value\n              </Typography>\n              <Box sx={{ height: 400, overflow: 'auto' }}>\n                {topCompanies.slice(0, 10).map((company, index) => (\n                  <Box\n                    key={company.symbol}\n                    sx={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      p: 1,\n                      borderBottom: index < 9 ? '1px solid' : 'none',\n                      borderBottomColor: 'divider',\n                    }}\n                  >\n                    <Box>\n                      <Typography variant=\"body2\" fontWeight={600}>\n                        {company.symbol}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {company.transaction_count} transactions\n                      </Typography>\n                    </Box>\n                    <Typography variant=\"body2\" fontWeight={600} color=\"primary.main\">\n                      {formatCurrency(company.total_value)}\n                    </Typography>\n                  </Box>\n                ))}\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Top Companies Chart */}\n        <Grid size={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top 10 Companies by Transaction Value\n              </Typography>\n              <TopCompaniesChart data={topCompanies} />\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Analytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,SACEC,UAAU,EACVC,YAAY,EACZC,QAAQ,QAEH,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAM,IAAI,CAAC;EACjD,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAQ,EAAE,CAAC;EAC3D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAM2B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QAEd,MAAM,CAACE,eAAe,EAAEC,iBAAiB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7DvB,UAAU,CAACwB,mBAAmB,CAAC,CAAC,EAChCxB,UAAU,CAACyB,eAAe,CAAC;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC,CAC1C,CAAC;QAEFd,UAAU,CAACQ,eAAe,CAACO,IAAI,CAAC;QAChCb,eAAe,CAACO,iBAAiB,CAACM,IAAI,CAAC;MACzC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACZ,KAAK,CAAC,gCAAgC,EAAEW,GAAG,CAAC;QACpDV,QAAQ,CAAC,+BAA+B,CAAC;MAC3C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,cAAc,GAAIC,KAAgC,IAAK;IAC3D,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIC,KAAK,CAACF,KAAK,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC;IAChC,IAAIG,QAAQ,IAAI,QAAQ,EAAE;MACxB,OAAO,IAAI,CAACH,KAAK,GAAG,QAAQ,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI;IAC9C,CAAC,MAAM,IAAIH,QAAQ,IAAI,MAAM,EAAE;MAC7B,OAAO,IAAI,CAACH,KAAK,GAAG,MAAM,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3C,CAAC,MAAM;MACL,OAAO,IAAIN,KAAK,CAACO,cAAc,CAAC,CAAC,EAAE;IACrC;EACF,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBACEP,OAAA,CAACf,GAAG;MAAC8C,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EnC,OAAA,CAACV,gBAAgB;QAAC8C,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAI/B,KAAK,EAAE;IACT,oBACET,OAAA,CAACf,GAAG;MAACwD,CAAC,EAAE,CAAE;MAAAN,QAAA,eACRnC,OAAA,CAACT,KAAK;QAACmD,QAAQ,EAAC,OAAO;QAAAP,QAAA,EAAE1B;MAAK;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,MAAMG,QAAQ,GAAGxC,OAAO,GAAGA,OAAO,CAACyC,SAAS,GAAG,CAAC;EAEhD,oBACE5C,OAAA,CAACf,GAAG;IAAAkD,QAAA,gBAEFnC,OAAA,CAACf,GAAG;MAAC4D,EAAE,EAAE,CAAE;MAAAV,QAAA,gBACTnC,OAAA,CAACd,UAAU;QAAC4D,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAZ,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxC,OAAA,CAACd,UAAU;QAAC4D,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,gBAAgB;QAAAb,QAAA,EAAC;MAEnD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLrC,OAAO,iBACNH,OAAA,CAACb,IAAI;MAAC8D,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,gBACxCnC,OAAA,CAACb,IAAI;QAACiD,IAAI,EAAE;UAAEgB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,eACnCnC,OAAA,CAACL,QAAQ;UACP4D,KAAK,EAAC,iBAAiB;UACvBhC,KAAK,EAAED,cAAc,CAACnB,OAAO,CAACqD,eAAe,CAAE;UAC/CC,IAAI,eAAEzD,OAAA,CAACJ,UAAU;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBQ,KAAK,EAAC;QAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPxC,OAAA,CAACb,IAAI;QAACiD,IAAI,EAAE;UAAEgB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,eACnCnC,OAAA,CAACL,QAAQ;UACP4D,KAAK,EAAC,kBAAkB;UACxBhC,KAAK,EAAED,cAAc,CAACnB,OAAO,CAACuD,gBAAgB,CAAE;UAChDD,IAAI,eAAEzD,OAAA,CAACH,YAAY;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBQ,KAAK,EAAC;QAAO;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPxC,OAAA,CAACb,IAAI;QAACiD,IAAI,EAAE;UAAEgB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,eACnCnC,OAAA,CAACL,QAAQ;UACP4D,KAAK,EAAC,WAAW;UACjBhC,KAAK,EAAED,cAAc,CAACK,IAAI,CAACC,GAAG,CAACe,QAAQ,CAAC,CAAE;UAC1CgB,QAAQ,EAAEhB,QAAQ,IAAI,CAAC,GAAG,YAAY,GAAG,aAAc;UACvDc,IAAI,EAAEd,QAAQ,IAAI,CAAC,gBAAG3C,OAAA,CAACJ,UAAU;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGxC,OAAA,CAACH,YAAY;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxDQ,KAAK,EAAEL,QAAQ,IAAI,CAAC,GAAG,SAAS,GAAG;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPxC,OAAA,CAACb,IAAI;QAACiD,IAAI,EAAE;UAAEgB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,eACnCnC,OAAA,CAACL,QAAQ;UACP4D,KAAK,EAAC,kBAAkB;UACxBhC,KAAK,EAAEpB,OAAO,CAACyD,gBAAgB,CAAC9B,cAAc,CAAC,CAAE;UACjD2B,IAAI,eAAEzD,OAAA,CAACF,QAAQ;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP,eAGDxC,OAAA,CAACb,IAAI;MAAC8D,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAf,QAAA,gBAEzBnC,OAAA,CAACb,IAAI;QAACiD,IAAI,EAAE;UAAEgB,EAAE,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAA1B,QAAA,eAC5BnC,OAAA,CAACZ,IAAI;UAAA+C,QAAA,eACHnC,OAAA,CAACX,WAAW;YAAA8C,QAAA,gBACVnC,OAAA,CAACd,UAAU;cAAC4D,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxC,OAAA,CAACN,sBAAsB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPxC,OAAA,CAACb,IAAI;QAACiD,IAAI,EAAE;UAAEgB,EAAE,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAA1B,QAAA,eAC5BnC,OAAA,CAACZ,IAAI;UAAC+D,EAAE,EAAE;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAA3B,QAAA,eAC3BnC,OAAA,CAACX,WAAW;YAAA8C,QAAA,gBACVnC,OAAA,CAACd,UAAU;cAAC4D,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxC,OAAA,CAACf,GAAG;cAACkE,EAAE,EAAE;gBAAEW,MAAM,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA5B,QAAA,EACxC9B,YAAY,CAAC2D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC5CnE,OAAA,CAACf,GAAG;gBAEFkE,EAAE,EAAE;kBACFpB,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBQ,CAAC,EAAE,CAAC;kBACJ2B,YAAY,EAAED,KAAK,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM;kBAC9CE,iBAAiB,EAAE;gBACrB,CAAE;gBAAAlC,QAAA,gBAEFnC,OAAA,CAACf,GAAG;kBAAAkD,QAAA,gBACFnC,OAAA,CAACd,UAAU;oBAAC4D,OAAO,EAAC,OAAO;oBAACwB,UAAU,EAAE,GAAI;oBAAAnC,QAAA,EACzC+B,OAAO,CAACK;kBAAM;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACbxC,OAAA,CAACd,UAAU;oBAAC4D,OAAO,EAAC,SAAS;oBAACE,KAAK,EAAC,gBAAgB;oBAAAb,QAAA,GACjD+B,OAAO,CAACM,iBAAiB,EAAC,eAC7B;kBAAA;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxC,OAAA,CAACd,UAAU;kBAAC4D,OAAO,EAAC,OAAO;kBAACwB,UAAU,EAAE,GAAI;kBAACtB,KAAK,EAAC,cAAc;kBAAAb,QAAA,EAC9Db,cAAc,CAAC4C,OAAO,CAACO,WAAW;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA,GApBR0B,OAAO,CAACK,MAAM;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqBhB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPxC,OAAA,CAACb,IAAI;QAACiD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACbnC,OAAA,CAACZ,IAAI;UAAA+C,QAAA,eACHnC,OAAA,CAACX,WAAW;YAAA8C,QAAA,gBACVnC,OAAA,CAACd,UAAU;cAAC4D,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxC,OAAA,CAACP,iBAAiB;cAAC0B,IAAI,EAAEd;YAAa;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtC,EAAA,CApLID,SAAmB;AAAAyE,EAAA,GAAnBzE,SAAmB;AAsLzB,eAAeA,SAAS;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}