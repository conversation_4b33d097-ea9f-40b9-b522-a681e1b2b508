{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\components\\\\TransactionTrendsChart.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';\nimport { Box, Typography, useTheme, CircularProgress } from '@mui/material';\nimport { format, subDays } from 'date-fns';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TransactionTrendsChart = () => {\n  _s();\n  const theme = useTheme();\n  const [data, setData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchTrendData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Get data for last 30 days\n        const endDate = new Date();\n        const startDate = subDays(endDate, 30);\n\n        // For now, we'll simulate trend data since we don't have a specific trends endpoint\n        // In a real implementation, you'd call a dedicated trends API\n        const response = await apiService.getTransactions({\n          from_date: startDate.toISOString().split('T')[0],\n          to_date: endDate.toISOString().split('T')[0],\n          limit: 1000 // Get more data for aggregation\n        });\n\n        // Aggregate data by date\n        const aggregatedData = {};\n        response.data.transactions.forEach(transaction => {\n          const date = format(new Date(transaction.transaction_date), 'yyyy-MM-dd');\n          if (!aggregatedData[date]) {\n            aggregatedData[date] = {\n              date,\n              buy_value: 0,\n              sell_value: 0,\n              transaction_count: 0,\n              net_value: 0\n            };\n          }\n          aggregatedData[date].transaction_count += 1;\n          if (transaction.buy_value) {\n            aggregatedData[date].buy_value += transaction.buy_value;\n          }\n          if (transaction.sell_value) {\n            aggregatedData[date].sell_value += transaction.sell_value;\n          }\n        });\n\n        // Calculate net values and convert to array\n        const trendData = Object.values(aggregatedData).map(item => ({\n          ...item,\n          net_value: item.buy_value - item.sell_value,\n          displayDate: format(new Date(item.date), 'MMM dd')\n        })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()).slice(-30); // Last 30 days\n\n        setData(trendData);\n      } catch (err) {\n        console.error('Error fetching trend data:', err);\n        setError('Failed to load trend data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTrendData();\n  }, []);\n  const formatCurrency = value => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${(value / 1000).toFixed(1)}K`;\n    }\n  };\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          backgroundColor: 'background.paper',\n          border: 1,\n          borderColor: 'divider',\n          borderRadius: 1,\n          p: 2,\n          boxShadow: theme.shadows[4]\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          children: format(new Date(data.date), 'MMM dd, yyyy')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"success.main\",\n          children: [\"Buy Value: \", formatCurrency(data.buy_value)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"error.main\",\n          children: [\"Sell Value: \", formatCurrency(data.sell_value)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"primary.main\",\n          fontWeight: 600,\n          children: [\"Net Value: \", formatCurrency(data.net_value)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Transactions: \", data.transaction_count]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"300px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"300px\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this);\n  }\n  if (!data || data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"300px\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"No trend data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      height: 400\n    },\n    children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n      width: \"100%\",\n      height: \"100%\",\n      children: /*#__PURE__*/_jsxDEV(LineChart, {\n        data: data,\n        margin: {\n          top: 20,\n          right: 30,\n          left: 20,\n          bottom: 20\n        },\n        children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n          strokeDasharray: \"3 3\",\n          stroke: theme.palette.divider\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n          dataKey: \"displayDate\",\n          tick: {\n            fontSize: 12,\n            fill: theme.palette.text.secondary\n          },\n          interval: \"preserveStartEnd\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n          tick: {\n            fontSize: 12,\n            fill: theme.palette.text.secondary\n          },\n          tickFormatter: value => {\n            if (Math.abs(value) >= 10000000) {\n              return `₹${(value / 10000000).toFixed(0)}Cr`;\n            } else if (Math.abs(value) >= 100000) {\n              return `₹${(value / 100000).toFixed(0)}L`;\n            } else {\n              return `₹${(value / 1000).toFixed(0)}K`;\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          type: \"monotone\",\n          dataKey: \"buy_value\",\n          stroke: theme.palette.success.main,\n          strokeWidth: 2,\n          dot: {\n            fill: theme.palette.success.main,\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"Buy Value\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          type: \"monotone\",\n          dataKey: \"sell_value\",\n          stroke: theme.palette.error.main,\n          strokeWidth: 2,\n          dot: {\n            fill: theme.palette.error.main,\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"Sell Value\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          type: \"monotone\",\n          dataKey: \"net_value\",\n          stroke: theme.palette.primary.main,\n          strokeWidth: 3,\n          dot: {\n            fill: theme.palette.primary.main,\n            strokeWidth: 2,\n            r: 5\n          },\n          name: \"Net Value\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(TransactionTrendsChart, \"S6wlLfdx1HOatXuMYTjHzm7R5Z0=\", false, function () {\n  return [useTheme];\n});\n_c = TransactionTrendsChart;\nexport default TransactionTrendsChart;\nvar _c;\n$RefreshReg$(_c, \"TransactionTrendsChart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "Legend", "Box", "Typography", "useTheme", "CircularProgress", "format", "subDays", "apiService", "jsxDEV", "_jsxDEV", "TransactionTrendsChart", "_s", "theme", "data", "setData", "loading", "setLoading", "error", "setError", "fetchTrendData", "endDate", "Date", "startDate", "response", "getTransactions", "from_date", "toISOString", "split", "to_date", "limit", "aggregatedData", "transactions", "for<PERSON>ach", "transaction", "date", "transaction_date", "buy_value", "sell_value", "transaction_count", "net_value", "trendData", "Object", "values", "map", "item", "displayDate", "sort", "a", "b", "getTime", "slice", "err", "console", "formatCurrency", "value", "undefined", "isNaN", "absValue", "Math", "abs", "toFixed", "CustomTooltip", "active", "payload", "label", "length", "sx", "backgroundColor", "border", "borderColor", "borderRadius", "p", "boxShadow", "shadows", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontWeight", "display", "justifyContent", "alignItems", "minHeight", "width", "height", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke", "palette", "divider", "dataKey", "tick", "fontSize", "fill", "text", "secondary", "interval", "tick<PERSON><PERSON><PERSON><PERSON>", "content", "type", "success", "main", "strokeWidth", "dot", "r", "name", "primary", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/components/TransactionTrendsChart.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Line,\n  XAxis,\n  <PERSON>Axis,\n  CartesianGrid,\n  <PERSON>lt<PERSON>,\n  Responsive<PERSON><PERSON><PERSON>,\n  <PERSON>,\n} from 'recharts';\nimport { Box, Typography, useTheme, CircularProgress } from '@mui/material';\nimport { format, subDays, startOfDay } from 'date-fns';\nimport { apiService } from '../services/apiService';\n\ninterface TrendData {\n  date: string;\n  buy_value: number;\n  sell_value: number;\n  transaction_count: number;\n  net_value: number;\n}\n\nconst TransactionTrendsChart: React.FC = () => {\n  const theme = useTheme();\n  const [data, setData] = useState<TrendData[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchTrendData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Get data for last 30 days\n        const endDate = new Date();\n        const startDate = subDays(endDate, 30);\n\n        // For now, we'll simulate trend data since we don't have a specific trends endpoint\n        // In a real implementation, you'd call a dedicated trends API\n        const response = await apiService.getTransactions({\n          from_date: startDate.toISOString().split('T')[0],\n          to_date: endDate.toISOString().split('T')[0],\n          limit: 1000, // Get more data for aggregation\n        });\n\n        // Aggregate data by date\n        const aggregatedData: { [key: string]: TrendData } = {};\n        \n        response.data.transactions.forEach((transaction) => {\n          const date = format(new Date(transaction.transaction_date), 'yyyy-MM-dd');\n          \n          if (!aggregatedData[date]) {\n            aggregatedData[date] = {\n              date,\n              buy_value: 0,\n              sell_value: 0,\n              transaction_count: 0,\n              net_value: 0,\n            };\n          }\n\n          aggregatedData[date].transaction_count += 1;\n          \n          if (transaction.buy_value) {\n            aggregatedData[date].buy_value += transaction.buy_value;\n          }\n          \n          if (transaction.sell_value) {\n            aggregatedData[date].sell_value += transaction.sell_value;\n          }\n        });\n\n        // Calculate net values and convert to array\n        const trendData = Object.values(aggregatedData)\n          .map((item) => ({\n            ...item,\n            net_value: item.buy_value - item.sell_value,\n            displayDate: format(new Date(item.date), 'MMM dd'),\n          }))\n          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n          .slice(-30); // Last 30 days\n\n        setData(trendData);\n      } catch (err) {\n        console.error('Error fetching trend data:', err);\n        setError('Failed to load trend data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTrendData();\n  }, []);\n\n  const formatCurrency = (value: number | null | undefined) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${(value / 1000).toFixed(1)}K`;\n    }\n  };\n\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <Box\n          sx={{\n            backgroundColor: 'background.paper',\n            border: 1,\n            borderColor: 'divider',\n            borderRadius: 1,\n            p: 2,\n            boxShadow: theme.shadows[4],\n          }}\n        >\n          <Typography variant=\"subtitle2\" gutterBottom>\n            {format(new Date(data.date), 'MMM dd, yyyy')}\n          </Typography>\n          <Typography variant=\"body2\" color=\"success.main\">\n            Buy Value: {formatCurrency(data.buy_value)}\n          </Typography>\n          <Typography variant=\"body2\" color=\"error.main\">\n            Sell Value: {formatCurrency(data.sell_value)}\n          </Typography>\n          <Typography variant=\"body2\" color=\"primary.main\" fontWeight={600}>\n            Net Value: {formatCurrency(data.net_value)}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Transactions: {data.transaction_count}\n          </Typography>\n        </Box>\n      );\n    }\n    return null;\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"300px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"300px\">\n        <Typography variant=\"body1\" color=\"error\">\n          {error}\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (!data || data.length === 0) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"300px\">\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          No trend data available\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ width: '100%', height: 400 }}>\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <LineChart\n          data={data}\n          margin={{\n            top: 20,\n            right: 30,\n            left: 20,\n            bottom: 20,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" stroke={theme.palette.divider} />\n          <XAxis\n            dataKey=\"displayDate\"\n            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}\n            interval=\"preserveStartEnd\"\n          />\n          <YAxis\n            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}\n            tickFormatter={(value) => {\n              if (Math.abs(value) >= 10000000) {\n                return `₹${(value / 10000000).toFixed(0)}Cr`;\n              } else if (Math.abs(value) >= 100000) {\n                return `₹${(value / 100000).toFixed(0)}L`;\n              } else {\n                return `₹${(value / 1000).toFixed(0)}K`;\n              }\n            }}\n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Legend />\n          <Line\n            type=\"monotone\"\n            dataKey=\"buy_value\"\n            stroke={theme.palette.success.main}\n            strokeWidth={2}\n            dot={{ fill: theme.palette.success.main, strokeWidth: 2, r: 4 }}\n            name=\"Buy Value\"\n          />\n          <Line\n            type=\"monotone\"\n            dataKey=\"sell_value\"\n            stroke={theme.palette.error.main}\n            strokeWidth={2}\n            dot={{ fill: theme.palette.error.main, strokeWidth: 2, r: 4 }}\n            name=\"Sell Value\"\n          />\n          <Line\n            type=\"monotone\"\n            dataKey=\"net_value\"\n            stroke={theme.palette.primary.main}\n            strokeWidth={3}\n            dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 5 }}\n            name=\"Net Value\"\n          />\n        </LineChart>\n      </ResponsiveContainer>\n    </Box>\n  );\n};\n\nexport default TransactionTrendsChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,mBAAmB,EACnBC,MAAM,QACD,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAC3E,SAASC,MAAM,EAAEC,OAAO,QAAoB,UAAU;AACtD,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUpD,MAAMC,sBAAgC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7C,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAc,EAAE,CAAC;EACjD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAM2B,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,MAAME,OAAO,GAAG,IAAIC,IAAI,CAAC,CAAC;QAC1B,MAAMC,SAAS,GAAGhB,OAAO,CAACc,OAAO,EAAE,EAAE,CAAC;;QAEtC;QACA;QACA,MAAMG,QAAQ,GAAG,MAAMhB,UAAU,CAACiB,eAAe,CAAC;UAChDC,SAAS,EAAEH,SAAS,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAChDC,OAAO,EAAER,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC5CE,KAAK,EAAE,IAAI,CAAE;QACf,CAAC,CAAC;;QAEF;QACA,MAAMC,cAA4C,GAAG,CAAC,CAAC;QAEvDP,QAAQ,CAACV,IAAI,CAACkB,YAAY,CAACC,OAAO,CAAEC,WAAW,IAAK;UAClD,MAAMC,IAAI,GAAG7B,MAAM,CAAC,IAAIgB,IAAI,CAACY,WAAW,CAACE,gBAAgB,CAAC,EAAE,YAAY,CAAC;UAEzE,IAAI,CAACL,cAAc,CAACI,IAAI,CAAC,EAAE;YACzBJ,cAAc,CAACI,IAAI,CAAC,GAAG;cACrBA,IAAI;cACJE,SAAS,EAAE,CAAC;cACZC,UAAU,EAAE,CAAC;cACbC,iBAAiB,EAAE,CAAC;cACpBC,SAAS,EAAE;YACb,CAAC;UACH;UAEAT,cAAc,CAACI,IAAI,CAAC,CAACI,iBAAiB,IAAI,CAAC;UAE3C,IAAIL,WAAW,CAACG,SAAS,EAAE;YACzBN,cAAc,CAACI,IAAI,CAAC,CAACE,SAAS,IAAIH,WAAW,CAACG,SAAS;UACzD;UAEA,IAAIH,WAAW,CAACI,UAAU,EAAE;YAC1BP,cAAc,CAACI,IAAI,CAAC,CAACG,UAAU,IAAIJ,WAAW,CAACI,UAAU;UAC3D;QACF,CAAC,CAAC;;QAEF;QACA,MAAMG,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACZ,cAAc,CAAC,CAC5Ca,GAAG,CAAEC,IAAI,KAAM;UACd,GAAGA,IAAI;UACPL,SAAS,EAAEK,IAAI,CAACR,SAAS,GAAGQ,IAAI,CAACP,UAAU;UAC3CQ,WAAW,EAAExC,MAAM,CAAC,IAAIgB,IAAI,CAACuB,IAAI,CAACV,IAAI,CAAC,EAAE,QAAQ;QACnD,CAAC,CAAC,CAAC,CACFY,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI3B,IAAI,CAAC0B,CAAC,CAACb,IAAI,CAAC,CAACe,OAAO,CAAC,CAAC,GAAG,IAAI5B,IAAI,CAAC2B,CAAC,CAACd,IAAI,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CACvEC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEfpC,OAAO,CAAC0B,SAAS,CAAC;MACpB,CAAC,CAAC,OAAOW,GAAG,EAAE;QACZC,OAAO,CAACnC,KAAK,CAAC,4BAA4B,EAAEkC,GAAG,CAAC;QAChDjC,QAAQ,CAAC,2BAA2B,CAAC;MACvC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkC,cAAc,GAAIC,KAAgC,IAAK;IAC3D,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIC,KAAK,CAACF,KAAK,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC;IAChC,IAAIG,QAAQ,IAAI,QAAQ,EAAE;MACxB,OAAO,IAAI,CAACH,KAAK,GAAG,QAAQ,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI;IAC9C,CAAC,MAAM,IAAIH,QAAQ,IAAI,MAAM,EAAE;MAC7B,OAAO,IAAI,CAACH,KAAK,GAAG,MAAM,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3C,CAAC,MAAM;MACL,OAAO,IAAI,CAACN,KAAK,GAAG,IAAI,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;IACzC;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAW,CAAC,KAAK;IACzD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;MACvC,MAAMpD,IAAI,GAAGkD,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO;MAC/B,oBACEtD,OAAA,CAACR,GAAG;QACFiE,EAAE,EAAE;UACFC,eAAe,EAAE,kBAAkB;UACnCC,MAAM,EAAE,CAAC;UACTC,WAAW,EAAE,SAAS;UACtBC,YAAY,EAAE,CAAC;UACfC,CAAC,EAAE,CAAC;UACJC,SAAS,EAAE5D,KAAK,CAAC6D,OAAO,CAAC,CAAC;QAC5B,CAAE;QAAAC,QAAA,gBAEFjE,OAAA,CAACP,UAAU;UAACyE,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EACzCrE,MAAM,CAAC,IAAIgB,IAAI,CAACR,IAAI,CAACqB,IAAI,CAAC,EAAE,cAAc;QAAC;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACbvE,OAAA,CAACP,UAAU;UAACyE,OAAO,EAAC,OAAO;UAACM,KAAK,EAAC,cAAc;UAAAP,QAAA,GAAC,aACpC,EAACrB,cAAc,CAACxC,IAAI,CAACuB,SAAS,CAAC;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACbvE,OAAA,CAACP,UAAU;UAACyE,OAAO,EAAC,OAAO;UAACM,KAAK,EAAC,YAAY;UAAAP,QAAA,GAAC,cACjC,EAACrB,cAAc,CAACxC,IAAI,CAACwB,UAAU,CAAC;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACbvE,OAAA,CAACP,UAAU;UAACyE,OAAO,EAAC,OAAO;UAACM,KAAK,EAAC,cAAc;UAACC,UAAU,EAAE,GAAI;UAAAR,QAAA,GAAC,aACrD,EAACrB,cAAc,CAACxC,IAAI,CAAC0B,SAAS,CAAC;QAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACbvE,OAAA,CAACP,UAAU;UAACyE,OAAO,EAAC,OAAO;UAACM,KAAK,EAAC,gBAAgB;UAAAP,QAAA,GAAC,gBACnC,EAAC7D,IAAI,CAACyB,iBAAiB;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,IAAIjE,OAAO,EAAE;IACX,oBACEN,OAAA,CAACR,GAAG;MAACkF,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAZ,QAAA,eAC/EjE,OAAA,CAACL,gBAAgB;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,IAAI/D,KAAK,EAAE;IACT,oBACER,OAAA,CAACR,GAAG;MAACkF,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAZ,QAAA,eAC/EjE,OAAA,CAACP,UAAU;QAACyE,OAAO,EAAC,OAAO;QAACM,KAAK,EAAC,OAAO;QAAAP,QAAA,EACtCzD;MAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,IAAI,CAACnE,IAAI,IAAIA,IAAI,CAACoD,MAAM,KAAK,CAAC,EAAE;IAC9B,oBACExD,OAAA,CAACR,GAAG;MAACkF,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAZ,QAAA,eAC/EjE,OAAA,CAACP,UAAU;QAACyE,OAAO,EAAC,OAAO;QAACM,KAAK,EAAC,gBAAgB;QAAAP,QAAA,EAAC;MAEnD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEvE,OAAA,CAACR,GAAG;IAACiE,EAAE,EAAE;MAAEqB,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAI,CAAE;IAAAd,QAAA,eACtCjE,OAAA,CAACV,mBAAmB;MAACwF,KAAK,EAAC,MAAM;MAACC,MAAM,EAAC,MAAM;MAAAd,QAAA,eAC7CjE,OAAA,CAAChB,SAAS;QACRoB,IAAI,EAAEA,IAAK;QACX4E,MAAM,EAAE;UACNC,GAAG,EAAE,EAAE;UACPC,KAAK,EAAE,EAAE;UACTC,IAAI,EAAE,EAAE;UACRC,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,gBAEFjE,OAAA,CAACZ,aAAa;UAACiG,eAAe,EAAC,KAAK;UAACC,MAAM,EAAEnF,KAAK,CAACoF,OAAO,CAACC;QAAQ;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtEvE,OAAA,CAACd,KAAK;UACJuG,OAAO,EAAC,aAAa;UACrBC,IAAI,EAAE;YAAEC,QAAQ,EAAE,EAAE;YAAEC,IAAI,EAAEzF,KAAK,CAACoF,OAAO,CAACM,IAAI,CAACC;UAAU,CAAE;UAC3DC,QAAQ,EAAC;QAAkB;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFvE,OAAA,CAACb,KAAK;UACJuG,IAAI,EAAE;YAAEC,QAAQ,EAAE,EAAE;YAAEC,IAAI,EAAEzF,KAAK,CAACoF,OAAO,CAACM,IAAI,CAACC;UAAU,CAAE;UAC3DE,aAAa,EAAGnD,KAAK,IAAK;YACxB,IAAII,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,IAAI,QAAQ,EAAE;cAC/B,OAAO,IAAI,CAACA,KAAK,GAAG,QAAQ,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI;YAC9C,CAAC,MAAM,IAAIF,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,IAAI,MAAM,EAAE;cACpC,OAAO,IAAI,CAACA,KAAK,GAAG,MAAM,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;YAC3C,CAAC,MAAM;cACL,OAAO,IAAI,CAACN,KAAK,GAAG,IAAI,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;YACzC;UACF;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvE,OAAA,CAACX,OAAO;UAAC4G,OAAO,eAAEjG,OAAA,CAACoD,aAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvCvE,OAAA,CAACT,MAAM;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVvE,OAAA,CAACf,IAAI;UACHiH,IAAI,EAAC,UAAU;UACfT,OAAO,EAAC,WAAW;UACnBH,MAAM,EAAEnF,KAAK,CAACoF,OAAO,CAACY,OAAO,CAACC,IAAK;UACnCC,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEV,IAAI,EAAEzF,KAAK,CAACoF,OAAO,CAACY,OAAO,CAACC,IAAI;YAAEC,WAAW,EAAE,CAAC;YAAEE,CAAC,EAAE;UAAE,CAAE;UAChEC,IAAI,EAAC;QAAW;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACFvE,OAAA,CAACf,IAAI;UACHiH,IAAI,EAAC,UAAU;UACfT,OAAO,EAAC,YAAY;UACpBH,MAAM,EAAEnF,KAAK,CAACoF,OAAO,CAAC/E,KAAK,CAAC4F,IAAK;UACjCC,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEV,IAAI,EAAEzF,KAAK,CAACoF,OAAO,CAAC/E,KAAK,CAAC4F,IAAI;YAAEC,WAAW,EAAE,CAAC;YAAEE,CAAC,EAAE;UAAE,CAAE;UAC9DC,IAAI,EAAC;QAAY;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACFvE,OAAA,CAACf,IAAI;UACHiH,IAAI,EAAC,UAAU;UACfT,OAAO,EAAC,WAAW;UACnBH,MAAM,EAAEnF,KAAK,CAACoF,OAAO,CAACkB,OAAO,CAACL,IAAK;UACnCC,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEV,IAAI,EAAEzF,KAAK,CAACoF,OAAO,CAACkB,OAAO,CAACL,IAAI;YAAEC,WAAW,EAAE,CAAC;YAAEE,CAAC,EAAE;UAAE,CAAE;UAChEC,IAAI,EAAC;QAAW;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV,CAAC;AAACrE,EAAA,CAnNID,sBAAgC;EAAA,QACtBP,QAAQ;AAAA;AAAAgH,EAAA,GADlBzG,sBAAgC;AAqNtC,eAAeA,sBAAsB;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}