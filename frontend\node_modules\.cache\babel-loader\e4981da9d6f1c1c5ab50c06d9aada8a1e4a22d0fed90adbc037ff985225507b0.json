{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getSectionTypeGranularity = exports.getDefaultReferenceDate = exports.SECTION_TYPE_GRANULARITY = void 0;\nvar _timeUtils = require(\"./time-utils\");\nvar _dateUtils = require(\"./date-utils\");\nconst SECTION_TYPE_GRANULARITY = exports.SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nconst getSectionTypeGranularity = sections => Math.max(...sections.map(section => SECTION_TYPE_GRANULARITY[section.type] ?? 1));\nexports.getSectionTypeGranularity = getSectionTypeGranularity;\nconst roundDate = (utils, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return utils.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return utils.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return utils.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = utils.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = utils.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = utils.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nconst getDefaultReferenceDate = ({\n  props,\n  utils,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(utils, granularity, (0, _dateUtils.getTodayDate)(utils, timezone));\n  if (props.minDate != null && utils.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.minDate);\n  }\n  if (props.maxDate != null && utils.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.maxDate);\n  }\n  const isAfter = (0, _timeUtils.createIsAfterIgnoreDatePart)(props.disableIgnoringDatePartForTimeValidation ?? false, utils);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : (0, _dateUtils.mergeDateAndTime)(utils, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : (0, _dateUtils.mergeDateAndTime)(utils, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};\nexports.getDefaultReferenceDate = getDefaultReferenceDate;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getSectionTypeGranularity", "getDefaultReferenceDate", "SECTION_TYPE_GRANULARITY", "_timeUtils", "require", "_dateUtils", "year", "month", "day", "hours", "minutes", "seconds", "milliseconds", "sections", "Math", "max", "map", "section", "type", "roundDate", "utils", "granularity", "date", "startOfYear", "startOfMonth", "startOfDay", "roundedDate", "setMinutes", "setSeconds", "setMilliseconds", "props", "timezone", "getTodayDate", "inGetTodayDate", "referenceDate", "minDate", "isAfterDay", "maxDate", "isBeforeDay", "isAfter", "createIsAfterIgnoreDatePart", "disableIgnoringDatePartForTimeValidation", "minTime", "mergeDateAndTime", "maxTime"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/utils/getDefaultReferenceDate.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getSectionTypeGranularity = exports.getDefaultReferenceDate = exports.SECTION_TYPE_GRANULARITY = void 0;\nvar _timeUtils = require(\"./time-utils\");\nvar _dateUtils = require(\"./date-utils\");\nconst SECTION_TYPE_GRANULARITY = exports.SECTION_TYPE_GRANULARITY = {\n  year: 1,\n  month: 2,\n  day: 3,\n  hours: 4,\n  minutes: 5,\n  seconds: 6,\n  milliseconds: 7\n};\nconst getSectionTypeGranularity = sections => Math.max(...sections.map(section => SECTION_TYPE_GRANULARITY[section.type] ?? 1));\nexports.getSectionTypeGranularity = getSectionTypeGranularity;\nconst roundDate = (utils, granularity, date) => {\n  if (granularity === SECTION_TYPE_GRANULARITY.year) {\n    return utils.startOfYear(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.month) {\n    return utils.startOfMonth(date);\n  }\n  if (granularity === SECTION_TYPE_GRANULARITY.day) {\n    return utils.startOfDay(date);\n  }\n\n  // We don't have startOfHour / startOfMinute / startOfSecond\n  let roundedDate = date;\n  if (granularity < SECTION_TYPE_GRANULARITY.minutes) {\n    roundedDate = utils.setMinutes(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.seconds) {\n    roundedDate = utils.setSeconds(roundedDate, 0);\n  }\n  if (granularity < SECTION_TYPE_GRANULARITY.milliseconds) {\n    roundedDate = utils.setMilliseconds(roundedDate, 0);\n  }\n  return roundedDate;\n};\nconst getDefaultReferenceDate = ({\n  props,\n  utils,\n  granularity,\n  timezone,\n  getTodayDate: inGetTodayDate\n}) => {\n  let referenceDate = inGetTodayDate ? inGetTodayDate() : roundDate(utils, granularity, (0, _dateUtils.getTodayDate)(utils, timezone));\n  if (props.minDate != null && utils.isAfterDay(props.minDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.minDate);\n  }\n  if (props.maxDate != null && utils.isBeforeDay(props.maxDate, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.maxDate);\n  }\n  const isAfter = (0, _timeUtils.createIsAfterIgnoreDatePart)(props.disableIgnoringDatePartForTimeValidation ?? false, utils);\n  if (props.minTime != null && isAfter(props.minTime, referenceDate)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.minTime : (0, _dateUtils.mergeDateAndTime)(utils, referenceDate, props.minTime));\n  }\n  if (props.maxTime != null && isAfter(referenceDate, props.maxTime)) {\n    referenceDate = roundDate(utils, granularity, props.disableIgnoringDatePartForTimeValidation ? props.maxTime : (0, _dateUtils.mergeDateAndTime)(utils, referenceDate, props.maxTime));\n  }\n  return referenceDate;\n};\nexports.getDefaultReferenceDate = getDefaultReferenceDate;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,yBAAyB,GAAGF,OAAO,CAACG,uBAAuB,GAAGH,OAAO,CAACI,wBAAwB,GAAG,KAAK,CAAC;AAC/G,IAAIC,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AACxC,IAAIC,UAAU,GAAGD,OAAO,CAAC,cAAc,CAAC;AACxC,MAAMF,wBAAwB,GAAGJ,OAAO,CAACI,wBAAwB,GAAG;EAClEI,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,YAAY,EAAE;AAChB,CAAC;AACD,MAAMZ,yBAAyB,GAAGa,QAAQ,IAAIC,IAAI,CAACC,GAAG,CAAC,GAAGF,QAAQ,CAACG,GAAG,CAACC,OAAO,IAAIf,wBAAwB,CAACe,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/HpB,OAAO,CAACE,yBAAyB,GAAGA,yBAAyB;AAC7D,MAAMmB,SAAS,GAAGA,CAACC,KAAK,EAAEC,WAAW,EAAEC,IAAI,KAAK;EAC9C,IAAID,WAAW,KAAKnB,wBAAwB,CAACI,IAAI,EAAE;IACjD,OAAOc,KAAK,CAACG,WAAW,CAACD,IAAI,CAAC;EAChC;EACA,IAAID,WAAW,KAAKnB,wBAAwB,CAACK,KAAK,EAAE;IAClD,OAAOa,KAAK,CAACI,YAAY,CAACF,IAAI,CAAC;EACjC;EACA,IAAID,WAAW,KAAKnB,wBAAwB,CAACM,GAAG,EAAE;IAChD,OAAOY,KAAK,CAACK,UAAU,CAACH,IAAI,CAAC;EAC/B;;EAEA;EACA,IAAII,WAAW,GAAGJ,IAAI;EACtB,IAAID,WAAW,GAAGnB,wBAAwB,CAACQ,OAAO,EAAE;IAClDgB,WAAW,GAAGN,KAAK,CAACO,UAAU,CAACD,WAAW,EAAE,CAAC,CAAC;EAChD;EACA,IAAIL,WAAW,GAAGnB,wBAAwB,CAACS,OAAO,EAAE;IAClDe,WAAW,GAAGN,KAAK,CAACQ,UAAU,CAACF,WAAW,EAAE,CAAC,CAAC;EAChD;EACA,IAAIL,WAAW,GAAGnB,wBAAwB,CAACU,YAAY,EAAE;IACvDc,WAAW,GAAGN,KAAK,CAACS,eAAe,CAACH,WAAW,EAAE,CAAC,CAAC;EACrD;EACA,OAAOA,WAAW;AACpB,CAAC;AACD,MAAMzB,uBAAuB,GAAGA,CAAC;EAC/B6B,KAAK;EACLV,KAAK;EACLC,WAAW;EACXU,QAAQ;EACRC,YAAY,EAAEC;AAChB,CAAC,KAAK;EACJ,IAAIC,aAAa,GAAGD,cAAc,GAAGA,cAAc,CAAC,CAAC,GAAGd,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAE,CAAC,CAAC,EAAEhB,UAAU,CAAC2B,YAAY,EAAEZ,KAAK,EAAEW,QAAQ,CAAC,CAAC;EACpI,IAAID,KAAK,CAACK,OAAO,IAAI,IAAI,IAAIf,KAAK,CAACgB,UAAU,CAACN,KAAK,CAACK,OAAO,EAAED,aAAa,CAAC,EAAE;IAC3EA,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAES,KAAK,CAACK,OAAO,CAAC;EAC9D;EACA,IAAIL,KAAK,CAACO,OAAO,IAAI,IAAI,IAAIjB,KAAK,CAACkB,WAAW,CAACR,KAAK,CAACO,OAAO,EAAEH,aAAa,CAAC,EAAE;IAC5EA,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAES,KAAK,CAACO,OAAO,CAAC;EAC9D;EACA,MAAME,OAAO,GAAG,CAAC,CAAC,EAAEpC,UAAU,CAACqC,2BAA2B,EAAEV,KAAK,CAACW,wCAAwC,IAAI,KAAK,EAAErB,KAAK,CAAC;EAC3H,IAAIU,KAAK,CAACY,OAAO,IAAI,IAAI,IAAIH,OAAO,CAACT,KAAK,CAACY,OAAO,EAAER,aAAa,CAAC,EAAE;IAClEA,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAES,KAAK,CAACW,wCAAwC,GAAGX,KAAK,CAACY,OAAO,GAAG,CAAC,CAAC,EAAErC,UAAU,CAACsC,gBAAgB,EAAEvB,KAAK,EAAEc,aAAa,EAAEJ,KAAK,CAACY,OAAO,CAAC,CAAC;EACvL;EACA,IAAIZ,KAAK,CAACc,OAAO,IAAI,IAAI,IAAIL,OAAO,CAACL,aAAa,EAAEJ,KAAK,CAACc,OAAO,CAAC,EAAE;IAClEV,aAAa,GAAGf,SAAS,CAACC,KAAK,EAAEC,WAAW,EAAES,KAAK,CAACW,wCAAwC,GAAGX,KAAK,CAACc,OAAO,GAAG,CAAC,CAAC,EAAEvC,UAAU,CAACsC,gBAAgB,EAAEvB,KAAK,EAAEc,aAAa,EAAEJ,KAAK,CAACc,OAAO,CAAC,CAAC;EACvL;EACA,OAAOV,aAAa;AACtB,CAAC;AACDpC,OAAO,CAACG,uBAAuB,GAAGA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}