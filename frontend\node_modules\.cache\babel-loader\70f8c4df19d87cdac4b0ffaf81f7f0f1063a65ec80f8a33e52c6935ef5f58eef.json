{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldInternalPropsWithDefaults = useFieldInternalPropsWithDefaults;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _useNullablePickerContext = require(\"../useNullablePickerContext\");\nvar _useNullableFieldPrivateContext = require(\"../useNullableFieldPrivateContext\");\n/**\n * Applies the default values to the field internal props.\n * This is a temporary hook that will be removed during a follow up when `useField` will receive the internal props without the defaults.\n * It is only here to allow the migration to be done in smaller steps.\n */\nfunction useFieldInternalPropsWithDefaults(parameters) {\n  const {\n    manager: {\n      internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToFieldInternalProps\n    },\n    internalProps,\n    skipContextFieldRefAssignment\n  } = parameters;\n  const pickerContext = (0, _useNullablePickerContext.useNullablePickerContext)();\n  const fieldPrivateContext = (0, _useNullableFieldPrivateContext.useNullableFieldPrivateContext)();\n  const handleFieldRef = (0, _useForkRef.default)(internalProps.unstableFieldRef, skipContextFieldRefAssignment ? null : fieldPrivateContext?.fieldRef);\n  const setValue = pickerContext?.setValue;\n  const handleChangeFromPicker = React.useCallback((newValue, ctx) => {\n    return setValue?.(newValue, {\n      validationError: ctx.validationError,\n      shouldClose: false\n    });\n  }, [setValue]);\n  const internalPropsWithDefaultsFromContext = React.useMemo(() => {\n    // If one of the context is null,\n    // Then the field is used as a standalone component and the other context will be null as well.\n    if (fieldPrivateContext != null && pickerContext != null) {\n      return (0, _extends2.default)({\n        value: pickerContext.value,\n        onChange: handleChangeFromPicker,\n        timezone: pickerContext.timezone,\n        disabled: pickerContext.disabled,\n        readOnly: pickerContext.readOnly,\n        autoFocus: pickerContext.autoFocus && !pickerContext.open,\n        focused: pickerContext.open ? true : undefined,\n        format: pickerContext.fieldFormat,\n        formatDensity: fieldPrivateContext.formatDensity,\n        enableAccessibleFieldDOMStructure: fieldPrivateContext.enableAccessibleFieldDOMStructure,\n        selectedSections: fieldPrivateContext.selectedSections,\n        onSelectedSectionsChange: fieldPrivateContext.onSelectedSectionsChange,\n        unstableFieldRef: handleFieldRef\n      }, internalProps);\n    }\n    return internalProps;\n  }, [pickerContext, fieldPrivateContext, internalProps, handleChangeFromPicker, handleFieldRef]);\n  return useApplyDefaultValuesToFieldInternalProps(internalPropsWithDefaultsFromContext);\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "useFieldInternalPropsWithDefaults", "_extends2", "React", "_useForkRef", "_useNullablePickerContext", "_useNullableFieldPrivateContext", "parameters", "manager", "internal_useApplyDefaultValuesToFieldInternalProps", "useApplyDefaultValuesToFieldInternalProps", "internalProps", "skipContextFieldRefAssignment", "picker<PERSON>ontext", "useNullablePickerContext", "fieldPrivateContext", "useNullableFieldPrivateContext", "handleFieldRef", "unstableFieldRef", "fieldRef", "setValue", "handleChangeFromPicker", "useCallback", "newValue", "ctx", "validationError", "shouldClose", "internalPropsWithDefaultsFromContext", "useMemo", "onChange", "timezone", "disabled", "readOnly", "autoFocus", "open", "focused", "undefined", "format", "fieldFormat", "formatDensity", "enableAccessibleFieldDOMStructure", "selectedSections", "onSelectedSectionsChange"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldInternalPropsWithDefaults.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldInternalPropsWithDefaults = useFieldInternalPropsWithDefaults;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _useNullablePickerContext = require(\"../useNullablePickerContext\");\nvar _useNullableFieldPrivateContext = require(\"../useNullableFieldPrivateContext\");\n/**\n * Applies the default values to the field internal props.\n * This is a temporary hook that will be removed during a follow up when `useField` will receive the internal props without the defaults.\n * It is only here to allow the migration to be done in smaller steps.\n */\nfunction useFieldInternalPropsWithDefaults(parameters) {\n  const {\n    manager: {\n      internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToFieldInternalProps\n    },\n    internalProps,\n    skipContextFieldRefAssignment\n  } = parameters;\n  const pickerContext = (0, _useNullablePickerContext.useNullablePickerContext)();\n  const fieldPrivateContext = (0, _useNullableFieldPrivateContext.useNullableFieldPrivateContext)();\n  const handleFieldRef = (0, _useForkRef.default)(internalProps.unstableFieldRef, skipContextFieldRefAssignment ? null : fieldPrivateContext?.fieldRef);\n  const setValue = pickerContext?.setValue;\n  const handleChangeFromPicker = React.useCallback((newValue, ctx) => {\n    return setValue?.(newValue, {\n      validationError: ctx.validationError,\n      shouldClose: false\n    });\n  }, [setValue]);\n  const internalPropsWithDefaultsFromContext = React.useMemo(() => {\n    // If one of the context is null,\n    // Then the field is used as a standalone component and the other context will be null as well.\n    if (fieldPrivateContext != null && pickerContext != null) {\n      return (0, _extends2.default)({\n        value: pickerContext.value,\n        onChange: handleChangeFromPicker,\n        timezone: pickerContext.timezone,\n        disabled: pickerContext.disabled,\n        readOnly: pickerContext.readOnly,\n        autoFocus: pickerContext.autoFocus && !pickerContext.open,\n        focused: pickerContext.open ? true : undefined,\n        format: pickerContext.fieldFormat,\n        formatDensity: fieldPrivateContext.formatDensity,\n        enableAccessibleFieldDOMStructure: fieldPrivateContext.enableAccessibleFieldDOMStructure,\n        selectedSections: fieldPrivateContext.selectedSections,\n        onSelectedSectionsChange: fieldPrivateContext.onSelectedSectionsChange,\n        unstableFieldRef: handleFieldRef\n      }, internalProps);\n    }\n    return internalProps;\n  }, [pickerContext, fieldPrivateContext, internalProps, handleChangeFromPicker, handleFieldRef]);\n  return useApplyDefaultValuesToFieldInternalProps(internalPropsWithDefaultsFromContext);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,iCAAiC,GAAGA,iCAAiC;AAC7E,IAAIC,SAAS,GAAGN,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIS,KAAK,GAAGV,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIU,WAAW,GAAGR,sBAAsB,CAACF,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIW,yBAAyB,GAAGX,OAAO,CAAC,6BAA6B,CAAC;AACtE,IAAIY,+BAA+B,GAAGZ,OAAO,CAAC,mCAAmC,CAAC;AAClF;AACA;AACA;AACA;AACA;AACA,SAASO,iCAAiCA,CAACM,UAAU,EAAE;EACrD,MAAM;IACJC,OAAO,EAAE;MACPC,kDAAkD,EAAEC;IACtD,CAAC;IACDC,aAAa;IACbC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,aAAa,GAAG,CAAC,CAAC,EAAER,yBAAyB,CAACS,wBAAwB,EAAE,CAAC;EAC/E,MAAMC,mBAAmB,GAAG,CAAC,CAAC,EAAET,+BAA+B,CAACU,8BAA8B,EAAE,CAAC;EACjG,MAAMC,cAAc,GAAG,CAAC,CAAC,EAAEb,WAAW,CAACT,OAAO,EAAEgB,aAAa,CAACO,gBAAgB,EAAEN,6BAA6B,GAAG,IAAI,GAAGG,mBAAmB,EAAEI,QAAQ,CAAC;EACrJ,MAAMC,QAAQ,GAAGP,aAAa,EAAEO,QAAQ;EACxC,MAAMC,sBAAsB,GAAGlB,KAAK,CAACmB,WAAW,CAAC,CAACC,QAAQ,EAAEC,GAAG,KAAK;IAClE,OAAOJ,QAAQ,GAAGG,QAAQ,EAAE;MAC1BE,eAAe,EAAED,GAAG,CAACC,eAAe;MACpCC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EACd,MAAMO,oCAAoC,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM;IAC/D;IACA;IACA,IAAIb,mBAAmB,IAAI,IAAI,IAAIF,aAAa,IAAI,IAAI,EAAE;MACxD,OAAO,CAAC,CAAC,EAAEX,SAAS,CAACP,OAAO,EAAE;QAC5BK,KAAK,EAAEa,aAAa,CAACb,KAAK;QAC1B6B,QAAQ,EAAER,sBAAsB;QAChCS,QAAQ,EAAEjB,aAAa,CAACiB,QAAQ;QAChCC,QAAQ,EAAElB,aAAa,CAACkB,QAAQ;QAChCC,QAAQ,EAAEnB,aAAa,CAACmB,QAAQ;QAChCC,SAAS,EAAEpB,aAAa,CAACoB,SAAS,IAAI,CAACpB,aAAa,CAACqB,IAAI;QACzDC,OAAO,EAAEtB,aAAa,CAACqB,IAAI,GAAG,IAAI,GAAGE,SAAS;QAC9CC,MAAM,EAAExB,aAAa,CAACyB,WAAW;QACjCC,aAAa,EAAExB,mBAAmB,CAACwB,aAAa;QAChDC,iCAAiC,EAAEzB,mBAAmB,CAACyB,iCAAiC;QACxFC,gBAAgB,EAAE1B,mBAAmB,CAAC0B,gBAAgB;QACtDC,wBAAwB,EAAE3B,mBAAmB,CAAC2B,wBAAwB;QACtExB,gBAAgB,EAAED;MACpB,CAAC,EAAEN,aAAa,CAAC;IACnB;IACA,OAAOA,aAAa;EACtB,CAAC,EAAE,CAACE,aAAa,EAAEE,mBAAmB,EAAEJ,aAAa,EAAEU,sBAAsB,EAAEJ,cAAc,CAAC,CAAC;EAC/F,OAAOP,yCAAyC,CAACiB,oCAAoC,CAAC;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}