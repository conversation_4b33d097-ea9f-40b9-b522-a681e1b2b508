{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.YearCalendar = void 0;\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _createStyled = require(\"@mui/system/createStyled\");\nvar _styles = require(\"@mui/material/styles\");\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _composeClasses = _interopRequireDefault(require(\"@mui/utils/composeClasses\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _YearCalendarButton = require(\"./YearCalendarButton\");\nvar _useUtils = require(\"../internals/hooks/useUtils\");\nvar _yearCalendarClasses = require(\"./yearCalendarClasses\");\nvar _valueManagers = require(\"../internals/utils/valueManagers\");\nvar _getDefaultReferenceDate = require(\"../internals/utils/getDefaultReferenceDate\");\nvar _useControlledValue = require(\"../internals/hooks/useControlledValue\");\nvar _dimensions = require(\"../internals/constants/dimensions\");\nvar _usePickerPrivateContext = require(\"../internals/hooks/usePickerPrivateContext\");\nvar _useDateManager = require(\"../managers/useDateManager\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"autoFocus\", \"className\", \"classes\", \"value\", \"defaultValue\", \"referenceDate\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onChange\", \"readOnly\", \"shouldDisableYear\", \"disableHighlightToday\", \"onYearFocus\", \"hasFocus\", \"onFocusedViewChange\", \"yearsOrder\", \"yearsPerRow\", \"timezone\", \"gridLabelId\", \"slots\", \"slotProps\"];\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return (0, _composeClasses.default)(slots, _yearCalendarClasses.getYearCalendarUtilityClass, classes);\n};\nfunction useYearCalendarDefaultizedProps(props, name) {\n  const themeProps = (0, _styles.useThemeProps)({\n    props,\n    name\n  });\n  const validationProps = (0, _useDateManager.useApplyDefaultValuesToDateValidationProps)(themeProps);\n  return (0, _extends2.default)({}, themeProps, validationProps, {\n    yearsPerRow: themeProps.yearsPerRow ?? 3,\n    yearsOrder: themeProps.yearsOrder ?? 'asc'\n  });\n}\nconst YearCalendarRoot = (0, _styles.styled)('div', {\n  name: 'MuiYearCalendar',\n  slot: 'Root',\n  shouldForwardProp: prop => (0, _createStyled.shouldForwardProp)(prop) && prop !== 'yearsPerRow'\n})({\n  display: 'flex',\n  flexWrap: 'wrap',\n  justifyContent: 'space-evenly',\n  rowGap: 12,\n  padding: '6px 0',\n  overflowY: 'auto',\n  height: '100%',\n  width: _dimensions.DIALOG_WIDTH,\n  maxHeight: _dimensions.MAX_CALENDAR_HEIGHT,\n  // avoid padding increasing width over defined\n  boxSizing: 'border-box',\n  position: 'relative',\n  variants: [{\n    props: {\n      yearsPerRow: 3\n    },\n    style: {\n      columnGap: 24\n    }\n  }, {\n    props: {\n      yearsPerRow: 4\n    },\n    style: {\n      columnGap: 0,\n      padding: '0 2px'\n    }\n  }]\n});\nconst YearCalendarButtonFiller = (0, _styles.styled)('div', {\n  name: 'MuiYearCalendar',\n  slot: 'ButtonFiller'\n})({\n  height: 36,\n  width: 72\n});\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n *\n * API:\n *\n * - [YearCalendar API](https://mui.com/x/api/date-pickers/year-calendar/)\n */\nconst YearCalendar = exports.YearCalendar = /*#__PURE__*/React.forwardRef(function YearCalendar(inProps, ref) {\n  const props = useYearCalendarDefaultizedProps(inProps, 'MuiYearCalendar');\n  const {\n      autoFocus,\n      className,\n      classes: classesProp,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onChange,\n      readOnly,\n      shouldDisableYear,\n      onYearFocus,\n      hasFocus,\n      onFocusedViewChange,\n      yearsOrder,\n      yearsPerRow,\n      timezone: timezoneProp,\n      gridLabelId,\n      slots,\n      slotProps\n    } = props,\n    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = (0, _useControlledValue.useControlledValue)({\n    name: 'YearCalendar',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: _valueManagers.singleItemValueManager\n  });\n  const now = (0, _useUtils.useNow)(timezone);\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const utils = (0, _useUtils.useUtils)();\n  const {\n    ownerState\n  } = (0, _usePickerPrivateContext.usePickerPrivateContext)();\n  const referenceDate = React.useMemo(() => _valueManagers.singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    timezone,\n    referenceDate: referenceDateProp,\n    granularity: _getDefaultReferenceDate.SECTION_TYPE_GRANULARITY.year\n  }), [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const classes = useUtilityClasses(classesProp);\n  const todayYear = React.useMemo(() => utils.getYear(now), [utils, now]);\n  const selectedYear = React.useMemo(() => {\n    if (value != null) {\n      return utils.getYear(value);\n    }\n    return null;\n  }, [value, utils]);\n  const [focusedYear, setFocusedYear] = React.useState(() => selectedYear || utils.getYear(referenceDate));\n  const [internalHasFocus, setInternalHasFocus] = (0, _useControlled.default)({\n    name: 'YearCalendar',\n    state: 'hasFocus',\n    controlled: hasFocus,\n    default: autoFocus ?? false\n  });\n  const changeHasFocus = (0, _useEventCallback.default)(newHasFocus => {\n    setInternalHasFocus(newHasFocus);\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  });\n  const isYearDisabled = React.useCallback(dateToValidate => {\n    if (disablePast && utils.isBeforeYear(dateToValidate, now)) {\n      return true;\n    }\n    if (disableFuture && utils.isAfterYear(dateToValidate, now)) {\n      return true;\n    }\n    if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {\n      return true;\n    }\n    if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {\n      return true;\n    }\n    if (!shouldDisableYear) {\n      return false;\n    }\n    const yearToValidate = utils.startOfYear(dateToValidate);\n    return shouldDisableYear(yearToValidate);\n  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils]);\n  const handleYearSelection = (0, _useEventCallback.default)((event, year) => {\n    if (readOnly) {\n      return;\n    }\n    const newDate = utils.setYear(value ?? referenceDate, year);\n    handleValueChange(newDate);\n  });\n  const focusYear = (0, _useEventCallback.default)(year => {\n    if (!isYearDisabled(utils.setYear(value ?? referenceDate, year))) {\n      setFocusedYear(year);\n      changeHasFocus(true);\n      onYearFocus?.(year);\n    }\n  });\n  React.useEffect(() => {\n    setFocusedYear(prevFocusedYear => selectedYear !== null && prevFocusedYear !== selectedYear ? selectedYear : prevFocusedYear);\n  }, [selectedYear]);\n  const verticalDirection = yearsOrder !== 'desc' ? yearsPerRow * 1 : yearsPerRow * -1;\n  const horizontalDirection = isRtl && yearsOrder === 'asc' || !isRtl && yearsOrder === 'desc' ? -1 : 1;\n  const handleKeyDown = (0, _useEventCallback.default)((event, year) => {\n    switch (event.key) {\n      case 'ArrowUp':\n        focusYear(year - verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusYear(year + verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        focusYear(year - horizontalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowRight':\n        focusYear(year + horizontalDirection);\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  });\n  const handleYearFocus = (0, _useEventCallback.default)((event, year) => {\n    focusYear(year);\n  });\n  const handleYearBlur = (0, _useEventCallback.default)((event, year) => {\n    if (focusedYear === year) {\n      changeHasFocus(false);\n    }\n  });\n  const scrollerRef = React.useRef(null);\n  const handleRef = (0, _useForkRef.default)(ref, scrollerRef);\n  React.useEffect(() => {\n    if (autoFocus || scrollerRef.current === null) {\n      return;\n    }\n    const tabbableButton = scrollerRef.current.querySelector('[tabindex=\"0\"]');\n    if (!tabbableButton) {\n      return;\n    }\n\n    // Taken from useScroll in x-data-grid, but vertically centered\n    const offsetHeight = tabbableButton.offsetHeight;\n    const offsetTop = tabbableButton.offsetTop;\n    const clientHeight = scrollerRef.current.clientHeight;\n    const scrollTop = scrollerRef.current.scrollTop;\n    const elementBottom = offsetTop + offsetHeight;\n    if (offsetHeight > clientHeight || offsetTop < scrollTop) {\n      // Button already visible\n      return;\n    }\n    scrollerRef.current.scrollTop = elementBottom - clientHeight / 2 - offsetHeight / 2;\n  }, [autoFocus]);\n  const yearRange = utils.getYearRange([minDate, maxDate]);\n  if (yearsOrder === 'desc') {\n    yearRange.reverse();\n  }\n  let fillerAmount = yearsPerRow - yearRange.length % yearsPerRow;\n  if (fillerAmount === yearsPerRow) {\n    fillerAmount = 0;\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(YearCalendarRoot, (0, _extends2.default)({\n    ref: handleRef,\n    className: (0, _clsx.default)(classes.root, className),\n    ownerState: ownerState,\n    role: \"radiogroup\",\n    \"aria-labelledby\": gridLabelId,\n    yearsPerRow: yearsPerRow\n  }, other, {\n    children: [yearRange.map(year => {\n      const yearNumber = utils.getYear(year);\n      const isSelected = yearNumber === selectedYear;\n      const isDisabled = disabled || isYearDisabled(year);\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_YearCalendarButton.YearCalendarButton, {\n        selected: isSelected,\n        value: yearNumber,\n        onClick: handleYearSelection,\n        onKeyDown: handleKeyDown,\n        autoFocus: internalHasFocus && yearNumber === focusedYear,\n        disabled: isDisabled,\n        tabIndex: yearNumber === focusedYear && !isDisabled ? 0 : -1,\n        onFocus: handleYearFocus,\n        onBlur: handleYearBlur,\n        \"aria-current\": todayYear === yearNumber ? 'date' : undefined,\n        slots: slots,\n        slotProps: slotProps,\n        classes: classesProp,\n        children: utils.format(year, 'year')\n      }, utils.format(year, 'year'));\n    }), Array.from({\n      length: fillerAmount\n    }, (_, index) => /*#__PURE__*/(0, _jsxRuntime.jsx)(YearCalendarButtonFiller, {}, index))]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") YearCalendar.displayName = \"YearCalendar\";\nprocess.env.NODE_ENV !== \"production\" ? YearCalendar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoFocus: _propTypes.default.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: _propTypes.default.object,\n  className: _propTypes.default.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: _propTypes.default.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: _propTypes.default.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: _propTypes.default.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: _propTypes.default.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: _propTypes.default.bool,\n  gridLabelId: _propTypes.default.string,\n  hasFocus: _propTypes.default.bool,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: _propTypes.default.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: _propTypes.default.object,\n  /**\n   * Callback fired when the value changes.\n   * @param {PickerValidDate} value The new value.\n   */\n  onChange: _propTypes.default.func,\n  onFocusedViewChange: _propTypes.default.func,\n  onYearFocus: _propTypes.default.func,\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: _propTypes.default.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid year using the validation props, except callbacks such as `shouldDisableYear`.\n   */\n  referenceDate: _propTypes.default.object,\n  /**\n   * Disable specific year.\n   * @param {PickerValidDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: _propTypes.default.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: _propTypes.default.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: _propTypes.default.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: _propTypes.default.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: _propTypes.default.object,\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: _propTypes.default.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: _propTypes.default.oneOf([3, 4])\n} : void 0;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "YearCalendar", "_objectWithoutPropertiesLoose2", "_extends2", "React", "_propTypes", "_clsx", "_RtlProvider", "_createStyled", "_styles", "_useForkRef", "_composeClasses", "_useControlled", "_useEventCallback", "_YearCalendar<PERSON><PERSON><PERSON>", "_useUtils", "_yearCalendarClasses", "_valueManagers", "_getDefaultReferenceDate", "_useControlledValue", "_dimensions", "_usePickerPrivateContext", "_useDateManager", "_jsxRuntime", "_excluded", "useUtilityClasses", "classes", "slots", "root", "getYearCalendarUtilityClass", "useYearCalendarDefaultizedProps", "props", "name", "themeProps", "useThemeProps", "validationProps", "useApplyDefaultValuesToDateValidationProps", "yearsPerRow", "yearsOrder", "YearCalendarRoot", "styled", "slot", "shouldForwardProp", "prop", "display", "flexWrap", "justifyContent", "rowGap", "padding", "overflowY", "height", "width", "DIALOG_WIDTH", "maxHeight", "MAX_CALENDAR_HEIGHT", "boxSizing", "position", "variants", "style", "columnGap", "YearCalendarButtonFiller", "forwardRef", "inProps", "ref", "autoFocus", "className", "classesProp", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onChange", "readOnly", "shouldDisableYear", "onYearFocus", "hasFocus", "onFocusedViewChange", "timezone", "timezoneProp", "gridLabelId", "slotProps", "other", "handleValueChange", "useControlledValue", "valueManager", "singleItemValueManager", "now", "useNow", "isRtl", "useRtl", "utils", "useUtils", "ownerState", "usePickerPrivateContext", "useMemo", "getInitialReferenceValue", "granularity", "SECTION_TYPE_GRANULARITY", "year", "todayYear", "getYear", "selected<PERSON>ear", "focusedYear", "setFocusedYear", "useState", "internalHasFocus", "setInternalHasFocus", "state", "controlled", "changeHasFocus", "newHasFocus", "isYearDisabled", "useCallback", "dateToValidate", "isBeforeYear", "isAfterYear", "yearToValidate", "startOfYear", "handleYearSelection", "event", "newDate", "setYear", "focusYear", "useEffect", "prevFocusedYear", "verticalDirection", "horizontalDirection", "handleKeyDown", "key", "preventDefault", "handleYearFocus", "handleYearBlur", "scrollerRef", "useRef", "handleRef", "current", "tabbableButton", "querySelector", "offsetHeight", "offsetTop", "clientHeight", "scrollTop", "elementBottom", "year<PERSON><PERSON><PERSON>", "getYearRange", "reverse", "fillerAmount", "length", "jsxs", "role", "children", "map", "yearNumber", "isSelected", "isDisabled", "jsx", "YearCalendarButton", "selected", "onClick", "onKeyDown", "tabIndex", "onFocus", "onBlur", "undefined", "format", "Array", "from", "_", "index", "process", "env", "NODE_ENV", "displayName", "propTypes", "bool", "object", "string", "disableHighlightToday", "func", "sx", "oneOfType", "arrayOf", "oneOf"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/YearCalendar/YearCalendar.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.YearCalendar = void 0;\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _createStyled = require(\"@mui/system/createStyled\");\nvar _styles = require(\"@mui/material/styles\");\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _composeClasses = _interopRequireDefault(require(\"@mui/utils/composeClasses\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _YearCalendarButton = require(\"./YearCalendarButton\");\nvar _useUtils = require(\"../internals/hooks/useUtils\");\nvar _yearCalendarClasses = require(\"./yearCalendarClasses\");\nvar _valueManagers = require(\"../internals/utils/valueManagers\");\nvar _getDefaultReferenceDate = require(\"../internals/utils/getDefaultReferenceDate\");\nvar _useControlledValue = require(\"../internals/hooks/useControlledValue\");\nvar _dimensions = require(\"../internals/constants/dimensions\");\nvar _usePickerPrivateContext = require(\"../internals/hooks/usePickerPrivateContext\");\nvar _useDateManager = require(\"../managers/useDateManager\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst _excluded = [\"autoFocus\", \"className\", \"classes\", \"value\", \"defaultValue\", \"referenceDate\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onChange\", \"readOnly\", \"shouldDisableYear\", \"disableHighlightToday\", \"onYearFocus\", \"hasFocus\", \"onFocusedViewChange\", \"yearsOrder\", \"yearsPerRow\", \"timezone\", \"gridLabelId\", \"slots\", \"slotProps\"];\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return (0, _composeClasses.default)(slots, _yearCalendarClasses.getYearCalendarUtilityClass, classes);\n};\nfunction useYearCalendarDefaultizedProps(props, name) {\n  const themeProps = (0, _styles.useThemeProps)({\n    props,\n    name\n  });\n  const validationProps = (0, _useDateManager.useApplyDefaultValuesToDateValidationProps)(themeProps);\n  return (0, _extends2.default)({}, themeProps, validationProps, {\n    yearsPerRow: themeProps.yearsPerRow ?? 3,\n    yearsOrder: themeProps.yearsOrder ?? 'asc'\n  });\n}\nconst YearCalendarRoot = (0, _styles.styled)('div', {\n  name: 'MuiYearCalendar',\n  slot: 'Root',\n  shouldForwardProp: prop => (0, _createStyled.shouldForwardProp)(prop) && prop !== 'yearsPerRow'\n})({\n  display: 'flex',\n  flexWrap: 'wrap',\n  justifyContent: 'space-evenly',\n  rowGap: 12,\n  padding: '6px 0',\n  overflowY: 'auto',\n  height: '100%',\n  width: _dimensions.DIALOG_WIDTH,\n  maxHeight: _dimensions.MAX_CALENDAR_HEIGHT,\n  // avoid padding increasing width over defined\n  boxSizing: 'border-box',\n  position: 'relative',\n  variants: [{\n    props: {\n      yearsPerRow: 3\n    },\n    style: {\n      columnGap: 24\n    }\n  }, {\n    props: {\n      yearsPerRow: 4\n    },\n    style: {\n      columnGap: 0,\n      padding: '0 2px'\n    }\n  }]\n});\nconst YearCalendarButtonFiller = (0, _styles.styled)('div', {\n  name: 'MuiYearCalendar',\n  slot: 'ButtonFiller'\n})({\n  height: 36,\n  width: 72\n});\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n *\n * API:\n *\n * - [YearCalendar API](https://mui.com/x/api/date-pickers/year-calendar/)\n */\nconst YearCalendar = exports.YearCalendar = /*#__PURE__*/React.forwardRef(function YearCalendar(inProps, ref) {\n  const props = useYearCalendarDefaultizedProps(inProps, 'MuiYearCalendar');\n  const {\n      autoFocus,\n      className,\n      classes: classesProp,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onChange,\n      readOnly,\n      shouldDisableYear,\n      onYearFocus,\n      hasFocus,\n      onFocusedViewChange,\n      yearsOrder,\n      yearsPerRow,\n      timezone: timezoneProp,\n      gridLabelId,\n      slots,\n      slotProps\n    } = props,\n    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = (0, _useControlledValue.useControlledValue)({\n    name: 'YearCalendar',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: _valueManagers.singleItemValueManager\n  });\n  const now = (0, _useUtils.useNow)(timezone);\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const utils = (0, _useUtils.useUtils)();\n  const {\n    ownerState\n  } = (0, _usePickerPrivateContext.usePickerPrivateContext)();\n  const referenceDate = React.useMemo(() => _valueManagers.singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    timezone,\n    referenceDate: referenceDateProp,\n    granularity: _getDefaultReferenceDate.SECTION_TYPE_GRANULARITY.year\n  }), [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const classes = useUtilityClasses(classesProp);\n  const todayYear = React.useMemo(() => utils.getYear(now), [utils, now]);\n  const selectedYear = React.useMemo(() => {\n    if (value != null) {\n      return utils.getYear(value);\n    }\n    return null;\n  }, [value, utils]);\n  const [focusedYear, setFocusedYear] = React.useState(() => selectedYear || utils.getYear(referenceDate));\n  const [internalHasFocus, setInternalHasFocus] = (0, _useControlled.default)({\n    name: 'YearCalendar',\n    state: 'hasFocus',\n    controlled: hasFocus,\n    default: autoFocus ?? false\n  });\n  const changeHasFocus = (0, _useEventCallback.default)(newHasFocus => {\n    setInternalHasFocus(newHasFocus);\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  });\n  const isYearDisabled = React.useCallback(dateToValidate => {\n    if (disablePast && utils.isBeforeYear(dateToValidate, now)) {\n      return true;\n    }\n    if (disableFuture && utils.isAfterYear(dateToValidate, now)) {\n      return true;\n    }\n    if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {\n      return true;\n    }\n    if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {\n      return true;\n    }\n    if (!shouldDisableYear) {\n      return false;\n    }\n    const yearToValidate = utils.startOfYear(dateToValidate);\n    return shouldDisableYear(yearToValidate);\n  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils]);\n  const handleYearSelection = (0, _useEventCallback.default)((event, year) => {\n    if (readOnly) {\n      return;\n    }\n    const newDate = utils.setYear(value ?? referenceDate, year);\n    handleValueChange(newDate);\n  });\n  const focusYear = (0, _useEventCallback.default)(year => {\n    if (!isYearDisabled(utils.setYear(value ?? referenceDate, year))) {\n      setFocusedYear(year);\n      changeHasFocus(true);\n      onYearFocus?.(year);\n    }\n  });\n  React.useEffect(() => {\n    setFocusedYear(prevFocusedYear => selectedYear !== null && prevFocusedYear !== selectedYear ? selectedYear : prevFocusedYear);\n  }, [selectedYear]);\n  const verticalDirection = yearsOrder !== 'desc' ? yearsPerRow * 1 : yearsPerRow * -1;\n  const horizontalDirection = isRtl && yearsOrder === 'asc' || !isRtl && yearsOrder === 'desc' ? -1 : 1;\n  const handleKeyDown = (0, _useEventCallback.default)((event, year) => {\n    switch (event.key) {\n      case 'ArrowUp':\n        focusYear(year - verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusYear(year + verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        focusYear(year - horizontalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowRight':\n        focusYear(year + horizontalDirection);\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  });\n  const handleYearFocus = (0, _useEventCallback.default)((event, year) => {\n    focusYear(year);\n  });\n  const handleYearBlur = (0, _useEventCallback.default)((event, year) => {\n    if (focusedYear === year) {\n      changeHasFocus(false);\n    }\n  });\n  const scrollerRef = React.useRef(null);\n  const handleRef = (0, _useForkRef.default)(ref, scrollerRef);\n  React.useEffect(() => {\n    if (autoFocus || scrollerRef.current === null) {\n      return;\n    }\n    const tabbableButton = scrollerRef.current.querySelector('[tabindex=\"0\"]');\n    if (!tabbableButton) {\n      return;\n    }\n\n    // Taken from useScroll in x-data-grid, but vertically centered\n    const offsetHeight = tabbableButton.offsetHeight;\n    const offsetTop = tabbableButton.offsetTop;\n    const clientHeight = scrollerRef.current.clientHeight;\n    const scrollTop = scrollerRef.current.scrollTop;\n    const elementBottom = offsetTop + offsetHeight;\n    if (offsetHeight > clientHeight || offsetTop < scrollTop) {\n      // Button already visible\n      return;\n    }\n    scrollerRef.current.scrollTop = elementBottom - clientHeight / 2 - offsetHeight / 2;\n  }, [autoFocus]);\n  const yearRange = utils.getYearRange([minDate, maxDate]);\n  if (yearsOrder === 'desc') {\n    yearRange.reverse();\n  }\n  let fillerAmount = yearsPerRow - yearRange.length % yearsPerRow;\n  if (fillerAmount === yearsPerRow) {\n    fillerAmount = 0;\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(YearCalendarRoot, (0, _extends2.default)({\n    ref: handleRef,\n    className: (0, _clsx.default)(classes.root, className),\n    ownerState: ownerState,\n    role: \"radiogroup\",\n    \"aria-labelledby\": gridLabelId,\n    yearsPerRow: yearsPerRow\n  }, other, {\n    children: [yearRange.map(year => {\n      const yearNumber = utils.getYear(year);\n      const isSelected = yearNumber === selectedYear;\n      const isDisabled = disabled || isYearDisabled(year);\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_YearCalendarButton.YearCalendarButton, {\n        selected: isSelected,\n        value: yearNumber,\n        onClick: handleYearSelection,\n        onKeyDown: handleKeyDown,\n        autoFocus: internalHasFocus && yearNumber === focusedYear,\n        disabled: isDisabled,\n        tabIndex: yearNumber === focusedYear && !isDisabled ? 0 : -1,\n        onFocus: handleYearFocus,\n        onBlur: handleYearBlur,\n        \"aria-current\": todayYear === yearNumber ? 'date' : undefined,\n        slots: slots,\n        slotProps: slotProps,\n        classes: classesProp,\n        children: utils.format(year, 'year')\n      }, utils.format(year, 'year'));\n    }), Array.from({\n      length: fillerAmount\n    }, (_, index) => /*#__PURE__*/(0, _jsxRuntime.jsx)(YearCalendarButtonFiller, {}, index))]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") YearCalendar.displayName = \"YearCalendar\";\nprocess.env.NODE_ENV !== \"production\" ? YearCalendar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoFocus: _propTypes.default.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: _propTypes.default.object,\n  className: _propTypes.default.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: _propTypes.default.object,\n  /**\n   * If `true`, the component is disabled.\n   * When disabled, the value cannot be changed and no interaction is possible.\n   * @default false\n   */\n  disabled: _propTypes.default.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: _propTypes.default.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: _propTypes.default.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: _propTypes.default.bool,\n  gridLabelId: _propTypes.default.string,\n  hasFocus: _propTypes.default.bool,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: _propTypes.default.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: _propTypes.default.object,\n  /**\n   * Callback fired when the value changes.\n   * @param {PickerValidDate} value The new value.\n   */\n  onChange: _propTypes.default.func,\n  onFocusedViewChange: _propTypes.default.func,\n  onYearFocus: _propTypes.default.func,\n  /**\n   * If `true`, the component is read-only.\n   * When read-only, the value cannot be changed but the user can interact with the interface.\n   * @default false\n   */\n  readOnly: _propTypes.default.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid year using the validation props, except callbacks such as `shouldDisableYear`.\n   */\n  referenceDate: _propTypes.default.object,\n  /**\n   * Disable specific year.\n   * @param {PickerValidDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: _propTypes.default.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: _propTypes.default.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: _propTypes.default.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: _propTypes.default.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: _propTypes.default.object,\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: _propTypes.default.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: _propTypes.default.oneOf([3, 4])\n} : void 0;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAIC,8BAA8B,GAAGT,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIS,SAAS,GAAGV,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIU,KAAK,GAAGR,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIW,UAAU,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIY,KAAK,GAAGb,sBAAsB,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,IAAIa,YAAY,GAAGb,OAAO,CAAC,yBAAyB,CAAC;AACrD,IAAIc,aAAa,GAAGd,OAAO,CAAC,0BAA0B,CAAC;AACvD,IAAIe,OAAO,GAAGf,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIgB,WAAW,GAAGjB,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIiB,eAAe,GAAGlB,sBAAsB,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAClF,IAAIkB,cAAc,GAAGnB,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAImB,iBAAiB,GAAGpB,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIoB,mBAAmB,GAAGpB,OAAO,CAAC,sBAAsB,CAAC;AACzD,IAAIqB,SAAS,GAAGrB,OAAO,CAAC,6BAA6B,CAAC;AACtD,IAAIsB,oBAAoB,GAAGtB,OAAO,CAAC,uBAAuB,CAAC;AAC3D,IAAIuB,cAAc,GAAGvB,OAAO,CAAC,kCAAkC,CAAC;AAChE,IAAIwB,wBAAwB,GAAGxB,OAAO,CAAC,4CAA4C,CAAC;AACpF,IAAIyB,mBAAmB,GAAGzB,OAAO,CAAC,uCAAuC,CAAC;AAC1E,IAAI0B,WAAW,GAAG1B,OAAO,CAAC,mCAAmC,CAAC;AAC9D,IAAI2B,wBAAwB,GAAG3B,OAAO,CAAC,4CAA4C,CAAC;AACpF,IAAI4B,eAAe,GAAG5B,OAAO,CAAC,4BAA4B,CAAC;AAC3D,IAAI6B,WAAW,GAAG7B,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAM8B,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,aAAa,EAAE,UAAU,EAAE,qBAAqB,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC;AACzW,MAAMC,iBAAiB,GAAGC,OAAO,IAAI;EACnC,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAO,CAAC,CAAC,EAAEjB,eAAe,CAAChB,OAAO,EAAEgC,KAAK,EAAEX,oBAAoB,CAACa,2BAA2B,EAAEH,OAAO,CAAC;AACvG,CAAC;AACD,SAASI,+BAA+BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACpD,MAAMC,UAAU,GAAG,CAAC,CAAC,EAAExB,OAAO,CAACyB,aAAa,EAAE;IAC5CH,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAMG,eAAe,GAAG,CAAC,CAAC,EAAEb,eAAe,CAACc,0CAA0C,EAAEH,UAAU,CAAC;EACnG,OAAO,CAAC,CAAC,EAAE9B,SAAS,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEsC,UAAU,EAAEE,eAAe,EAAE;IAC7DE,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI,CAAC;IACxCC,UAAU,EAAEL,UAAU,CAACK,UAAU,IAAI;EACvC,CAAC,CAAC;AACJ;AACA,MAAMC,gBAAgB,GAAG,CAAC,CAAC,EAAE9B,OAAO,CAAC+B,MAAM,EAAE,KAAK,EAAE;EAClDR,IAAI,EAAE,iBAAiB;EACvBS,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEC,IAAI,IAAI,CAAC,CAAC,EAAEnC,aAAa,CAACkC,iBAAiB,EAAEC,IAAI,CAAC,IAAIA,IAAI,KAAK;AACpF,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,MAAM;EAChBC,cAAc,EAAE,cAAc;EAC9BC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,MAAM;EACjBC,MAAM,EAAE,MAAM;EACdC,KAAK,EAAE/B,WAAW,CAACgC,YAAY;EAC/BC,SAAS,EAAEjC,WAAW,CAACkC,mBAAmB;EAC1C;EACAC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,CAAC;IACT1B,KAAK,EAAE;MACLM,WAAW,EAAE;IACf,CAAC;IACDqB,KAAK,EAAE;MACLC,SAAS,EAAE;IACb;EACF,CAAC,EAAE;IACD5B,KAAK,EAAE;MACLM,WAAW,EAAE;IACf,CAAC;IACDqB,KAAK,EAAE;MACLC,SAAS,EAAE,CAAC;MACZX,OAAO,EAAE;IACX;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMY,wBAAwB,GAAG,CAAC,CAAC,EAAEnD,OAAO,CAAC+B,MAAM,EAAE,KAAK,EAAE;EAC1DR,IAAI,EAAE,iBAAiB;EACvBS,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDS,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMlD,YAAY,GAAGF,OAAO,CAACE,YAAY,GAAG,aAAaG,KAAK,CAACyD,UAAU,CAAC,SAAS5D,YAAYA,CAAC6D,OAAO,EAAEC,GAAG,EAAE;EAC5G,MAAMhC,KAAK,GAAGD,+BAA+B,CAACgC,OAAO,EAAE,iBAAiB,CAAC;EACzE,MAAM;MACFE,SAAS;MACTC,SAAS;MACTvC,OAAO,EAAEwC,WAAW;MACpBlE,KAAK,EAAEmE,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,QAAQ;MACRC,aAAa;MACbC,WAAW;MACXC,OAAO;MACPC,OAAO;MACPC,QAAQ;MACRC,QAAQ;MACRC,iBAAiB;MACjBC,WAAW;MACXC,QAAQ;MACRC,mBAAmB;MACnB3C,UAAU;MACVD,WAAW;MACX6C,QAAQ,EAAEC,YAAY;MACtBC,WAAW;MACXzD,KAAK;MACL0D;IACF,CAAC,GAAGtD,KAAK;IACTuD,KAAK,GAAG,CAAC,CAAC,EAAEpF,8BAA8B,CAACP,OAAO,EAAEoC,KAAK,EAAEP,SAAS,CAAC;EACvE,MAAM;IACJxB,KAAK;IACLuF,iBAAiB;IACjBL;EACF,CAAC,GAAG,CAAC,CAAC,EAAE/D,mBAAmB,CAACqE,kBAAkB,EAAE;IAC9CxD,IAAI,EAAE,cAAc;IACpBkD,QAAQ,EAAEC,YAAY;IACtBnF,KAAK,EAAEmE,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCM,QAAQ;IACRa,YAAY,EAAExE,cAAc,CAACyE;EAC/B,CAAC,CAAC;EACF,MAAMC,GAAG,GAAG,CAAC,CAAC,EAAE5E,SAAS,CAAC6E,MAAM,EAAEV,QAAQ,CAAC;EAC3C,MAAMW,KAAK,GAAG,CAAC,CAAC,EAAEtF,YAAY,CAACuF,MAAM,EAAE,CAAC;EACxC,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEhF,SAAS,CAACiF,QAAQ,EAAE,CAAC;EACvC,MAAM;IACJC;EACF,CAAC,GAAG,CAAC,CAAC,EAAE5E,wBAAwB,CAAC6E,uBAAuB,EAAE,CAAC;EAC3D,MAAM7B,aAAa,GAAGjE,KAAK,CAAC+F,OAAO,CAAC,MAAMlF,cAAc,CAACyE,sBAAsB,CAACU,wBAAwB,CAAC;IACvGpG,KAAK;IACL+F,KAAK;IACLhE,KAAK;IACLmD,QAAQ;IACRb,aAAa,EAAEC,iBAAiB;IAChC+B,WAAW,EAAEnF,wBAAwB,CAACoF,wBAAwB,CAACC;EACjE,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,CAAC;EACD,MAAM7E,OAAO,GAAGD,iBAAiB,CAACyC,WAAW,CAAC;EAC9C,MAAMsC,SAAS,GAAGpG,KAAK,CAAC+F,OAAO,CAAC,MAAMJ,KAAK,CAACU,OAAO,CAACd,GAAG,CAAC,EAAE,CAACI,KAAK,EAAEJ,GAAG,CAAC,CAAC;EACvE,MAAMe,YAAY,GAAGtG,KAAK,CAAC+F,OAAO,CAAC,MAAM;IACvC,IAAInG,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO+F,KAAK,CAACU,OAAO,CAACzG,KAAK,CAAC;IAC7B;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACA,KAAK,EAAE+F,KAAK,CAAC,CAAC;EAClB,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGxG,KAAK,CAACyG,QAAQ,CAAC,MAAMH,YAAY,IAAIX,KAAK,CAACU,OAAO,CAACpC,aAAa,CAAC,CAAC;EACxG,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG,CAAC,CAAC,EAAEnG,cAAc,CAACjB,OAAO,EAAE;IAC1EqC,IAAI,EAAE,cAAc;IACpBgF,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAEjC,QAAQ;IACpBrF,OAAO,EAAEqE,SAAS,IAAI;EACxB,CAAC,CAAC;EACF,MAAMkD,cAAc,GAAG,CAAC,CAAC,EAAErG,iBAAiB,CAAClB,OAAO,EAAEwH,WAAW,IAAI;IACnEJ,mBAAmB,CAACI,WAAW,CAAC;IAChC,IAAIlC,mBAAmB,EAAE;MACvBA,mBAAmB,CAACkC,WAAW,CAAC;IAClC;EACF,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGhH,KAAK,CAACiH,WAAW,CAACC,cAAc,IAAI;IACzD,IAAI7C,WAAW,IAAIsB,KAAK,CAACwB,YAAY,CAACD,cAAc,EAAE3B,GAAG,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAInB,aAAa,IAAIuB,KAAK,CAACyB,WAAW,CAACF,cAAc,EAAE3B,GAAG,CAAC,EAAE;MAC3D,OAAO,IAAI;IACb;IACA,IAAIhB,OAAO,IAAIoB,KAAK,CAACwB,YAAY,CAACD,cAAc,EAAE3C,OAAO,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAID,OAAO,IAAIqB,KAAK,CAACyB,WAAW,CAACF,cAAc,EAAE5C,OAAO,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IACA,IAAI,CAACI,iBAAiB,EAAE;MACtB,OAAO,KAAK;IACd;IACA,MAAM2C,cAAc,GAAG1B,KAAK,CAAC2B,WAAW,CAACJ,cAAc,CAAC;IACxD,OAAOxC,iBAAiB,CAAC2C,cAAc,CAAC;EAC1C,CAAC,EAAE,CAACjD,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEgB,GAAG,EAAEb,iBAAiB,EAAEiB,KAAK,CAAC,CAAC;EACjF,MAAM4B,mBAAmB,GAAG,CAAC,CAAC,EAAE9G,iBAAiB,CAAClB,OAAO,EAAE,CAACiI,KAAK,EAAErB,IAAI,KAAK;IAC1E,IAAI1B,QAAQ,EAAE;MACZ;IACF;IACA,MAAMgD,OAAO,GAAG9B,KAAK,CAAC+B,OAAO,CAAC9H,KAAK,IAAIqE,aAAa,EAAEkC,IAAI,CAAC;IAC3DhB,iBAAiB,CAACsC,OAAO,CAAC;EAC5B,CAAC,CAAC;EACF,MAAME,SAAS,GAAG,CAAC,CAAC,EAAElH,iBAAiB,CAAClB,OAAO,EAAE4G,IAAI,IAAI;IACvD,IAAI,CAACa,cAAc,CAACrB,KAAK,CAAC+B,OAAO,CAAC9H,KAAK,IAAIqE,aAAa,EAAEkC,IAAI,CAAC,CAAC,EAAE;MAChEK,cAAc,CAACL,IAAI,CAAC;MACpBW,cAAc,CAAC,IAAI,CAAC;MACpBnC,WAAW,GAAGwB,IAAI,CAAC;IACrB;EACF,CAAC,CAAC;EACFnG,KAAK,CAAC4H,SAAS,CAAC,MAAM;IACpBpB,cAAc,CAACqB,eAAe,IAAIvB,YAAY,KAAK,IAAI,IAAIuB,eAAe,KAAKvB,YAAY,GAAGA,YAAY,GAAGuB,eAAe,CAAC;EAC/H,CAAC,EAAE,CAACvB,YAAY,CAAC,CAAC;EAClB,MAAMwB,iBAAiB,GAAG5F,UAAU,KAAK,MAAM,GAAGD,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;EACpF,MAAM8F,mBAAmB,GAAGtC,KAAK,IAAIvD,UAAU,KAAK,KAAK,IAAI,CAACuD,KAAK,IAAIvD,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;EACrG,MAAM8F,aAAa,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAAClB,OAAO,EAAE,CAACiI,KAAK,EAAErB,IAAI,KAAK;IACpE,QAAQqB,KAAK,CAACS,GAAG;MACf,KAAK,SAAS;QACZN,SAAS,CAACxB,IAAI,GAAG2B,iBAAiB,CAAC;QACnCN,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,WAAW;QACdP,SAAS,CAACxB,IAAI,GAAG2B,iBAAiB,CAAC;QACnCN,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,WAAW;QACdP,SAAS,CAACxB,IAAI,GAAG4B,mBAAmB,CAAC;QACrCP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,YAAY;QACfP,SAAS,CAACxB,IAAI,GAAG4B,mBAAmB,CAAC;QACrCP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF;QACE;IACJ;EACF,CAAC,CAAC;EACF,MAAMC,eAAe,GAAG,CAAC,CAAC,EAAE1H,iBAAiB,CAAClB,OAAO,EAAE,CAACiI,KAAK,EAAErB,IAAI,KAAK;IACtEwB,SAAS,CAACxB,IAAI,CAAC;EACjB,CAAC,CAAC;EACF,MAAMiC,cAAc,GAAG,CAAC,CAAC,EAAE3H,iBAAiB,CAAClB,OAAO,EAAE,CAACiI,KAAK,EAAErB,IAAI,KAAK;IACrE,IAAII,WAAW,KAAKJ,IAAI,EAAE;MACxBW,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMuB,WAAW,GAAGrI,KAAK,CAACsI,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAEjI,WAAW,CAACf,OAAO,EAAEoE,GAAG,EAAE0E,WAAW,CAAC;EAC5DrI,KAAK,CAAC4H,SAAS,CAAC,MAAM;IACpB,IAAIhE,SAAS,IAAIyE,WAAW,CAACG,OAAO,KAAK,IAAI,EAAE;MAC7C;IACF;IACA,MAAMC,cAAc,GAAGJ,WAAW,CAACG,OAAO,CAACE,aAAa,CAAC,gBAAgB,CAAC;IAC1E,IAAI,CAACD,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAME,YAAY,GAAGF,cAAc,CAACE,YAAY;IAChD,MAAMC,SAAS,GAAGH,cAAc,CAACG,SAAS;IAC1C,MAAMC,YAAY,GAAGR,WAAW,CAACG,OAAO,CAACK,YAAY;IACrD,MAAMC,SAAS,GAAGT,WAAW,CAACG,OAAO,CAACM,SAAS;IAC/C,MAAMC,aAAa,GAAGH,SAAS,GAAGD,YAAY;IAC9C,IAAIA,YAAY,GAAGE,YAAY,IAAID,SAAS,GAAGE,SAAS,EAAE;MACxD;MACA;IACF;IACAT,WAAW,CAACG,OAAO,CAACM,SAAS,GAAGC,aAAa,GAAGF,YAAY,GAAG,CAAC,GAAGF,YAAY,GAAG,CAAC;EACrF,CAAC,EAAE,CAAC/E,SAAS,CAAC,CAAC;EACf,MAAMoF,SAAS,GAAGrD,KAAK,CAACsD,YAAY,CAAC,CAAC1E,OAAO,EAAED,OAAO,CAAC,CAAC;EACxD,IAAIpC,UAAU,KAAK,MAAM,EAAE;IACzB8G,SAAS,CAACE,OAAO,CAAC,CAAC;EACrB;EACA,IAAIC,YAAY,GAAGlH,WAAW,GAAG+G,SAAS,CAACI,MAAM,GAAGnH,WAAW;EAC/D,IAAIkH,YAAY,KAAKlH,WAAW,EAAE;IAChCkH,YAAY,GAAG,CAAC;EAClB;EACA,OAAO,aAAa,CAAC,CAAC,EAAEhI,WAAW,CAACkI,IAAI,EAAElH,gBAAgB,EAAE,CAAC,CAAC,EAAEpC,SAAS,CAACR,OAAO,EAAE;IACjFoE,GAAG,EAAE4E,SAAS;IACd1E,SAAS,EAAE,CAAC,CAAC,EAAE3D,KAAK,CAACX,OAAO,EAAE+B,OAAO,CAACE,IAAI,EAAEqC,SAAS,CAAC;IACtDgC,UAAU,EAAEA,UAAU;IACtByD,IAAI,EAAE,YAAY;IAClB,iBAAiB,EAAEtE,WAAW;IAC9B/C,WAAW,EAAEA;EACf,CAAC,EAAEiD,KAAK,EAAE;IACRqE,QAAQ,EAAE,CAACP,SAAS,CAACQ,GAAG,CAACrD,IAAI,IAAI;MAC/B,MAAMsD,UAAU,GAAG9D,KAAK,CAACU,OAAO,CAACF,IAAI,CAAC;MACtC,MAAMuD,UAAU,GAAGD,UAAU,KAAKnD,YAAY;MAC9C,MAAMqD,UAAU,GAAGxF,QAAQ,IAAI6C,cAAc,CAACb,IAAI,CAAC;MACnD,OAAO,aAAa,CAAC,CAAC,EAAEhF,WAAW,CAACyI,GAAG,EAAElJ,mBAAmB,CAACmJ,kBAAkB,EAAE;QAC/EC,QAAQ,EAAEJ,UAAU;QACpB9J,KAAK,EAAE6J,UAAU;QACjBM,OAAO,EAAExC,mBAAmB;QAC5ByC,SAAS,EAAEhC,aAAa;QACxBpE,SAAS,EAAE8C,gBAAgB,IAAI+C,UAAU,KAAKlD,WAAW;QACzDpC,QAAQ,EAAEwF,UAAU;QACpBM,QAAQ,EAAER,UAAU,KAAKlD,WAAW,IAAI,CAACoD,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5DO,OAAO,EAAE/B,eAAe;QACxBgC,MAAM,EAAE/B,cAAc;QACtB,cAAc,EAAEhC,SAAS,KAAKqD,UAAU,GAAG,MAAM,GAAGW,SAAS;QAC7D7I,KAAK,EAAEA,KAAK;QACZ0D,SAAS,EAAEA,SAAS;QACpB3D,OAAO,EAAEwC,WAAW;QACpByF,QAAQ,EAAE5D,KAAK,CAAC0E,MAAM,CAAClE,IAAI,EAAE,MAAM;MACrC,CAAC,EAAER,KAAK,CAAC0E,MAAM,CAAClE,IAAI,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC,EAAEmE,KAAK,CAACC,IAAI,CAAC;MACbnB,MAAM,EAAED;IACV,CAAC,EAAE,CAACqB,CAAC,EAAEC,KAAK,KAAK,aAAa,CAAC,CAAC,EAAEtJ,WAAW,CAACyI,GAAG,EAAEpG,wBAAwB,EAAE,CAAC,CAAC,EAAEiH,KAAK,CAAC,CAAC;EAC1F,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE/K,YAAY,CAACgL,WAAW,GAAG,cAAc;AACpFH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/K,YAAY,CAACiL,SAAS,GAAG;EAC/D;EACA;EACA;EACA;EACAlH,SAAS,EAAE3D,UAAU,CAACV,OAAO,CAACwL,IAAI;EAClC;AACF;AACA;EACEzJ,OAAO,EAAErB,UAAU,CAACV,OAAO,CAACyL,MAAM;EAClCnH,SAAS,EAAE5D,UAAU,CAACV,OAAO,CAAC0L,MAAM;EACpC;AACF;AACA;AACA;EACEjH,YAAY,EAAE/D,UAAU,CAACV,OAAO,CAACyL,MAAM;EACvC;AACF;AACA;AACA;AACA;EACE7G,QAAQ,EAAElE,UAAU,CAACV,OAAO,CAACwL,IAAI;EACjC;AACF;AACA;AACA;EACE3G,aAAa,EAAEnE,UAAU,CAACV,OAAO,CAACwL,IAAI;EACtC;AACF;AACA;AACA;EACEG,qBAAqB,EAAEjL,UAAU,CAACV,OAAO,CAACwL,IAAI;EAC9C;AACF;AACA;AACA;EACE1G,WAAW,EAAEpE,UAAU,CAACV,OAAO,CAACwL,IAAI;EACpC/F,WAAW,EAAE/E,UAAU,CAACV,OAAO,CAAC0L,MAAM;EACtCrG,QAAQ,EAAE3E,UAAU,CAACV,OAAO,CAACwL,IAAI;EACjC;AACF;AACA;AACA;EACEzG,OAAO,EAAErE,UAAU,CAACV,OAAO,CAACyL,MAAM;EAClC;AACF;AACA;AACA;EACEzG,OAAO,EAAEtE,UAAU,CAACV,OAAO,CAACyL,MAAM;EAClC;AACF;AACA;AACA;EACExG,QAAQ,EAAEvE,UAAU,CAACV,OAAO,CAAC4L,IAAI;EACjCtG,mBAAmB,EAAE5E,UAAU,CAACV,OAAO,CAAC4L,IAAI;EAC5CxG,WAAW,EAAE1E,UAAU,CAACV,OAAO,CAAC4L,IAAI;EACpC;AACF;AACA;AACA;AACA;EACE1G,QAAQ,EAAExE,UAAU,CAACV,OAAO,CAACwL,IAAI;EACjC;AACF;AACA;AACA;EACE9G,aAAa,EAAEhE,UAAU,CAACV,OAAO,CAACyL,MAAM;EACxC;AACF;AACA;AACA;AACA;EACEtG,iBAAiB,EAAEzE,UAAU,CAACV,OAAO,CAAC4L,IAAI;EAC1C;AACF;AACA;AACA;EACElG,SAAS,EAAEhF,UAAU,CAACV,OAAO,CAACyL,MAAM;EACpC;AACF;AACA;AACA;EACEzJ,KAAK,EAAEtB,UAAU,CAACV,OAAO,CAACyL,MAAM;EAChC;AACF;AACA;EACEI,EAAE,EAAEnL,UAAU,CAACV,OAAO,CAAC8L,SAAS,CAAC,CAACpL,UAAU,CAACV,OAAO,CAAC+L,OAAO,CAACrL,UAAU,CAACV,OAAO,CAAC8L,SAAS,CAAC,CAACpL,UAAU,CAACV,OAAO,CAAC4L,IAAI,EAAElL,UAAU,CAACV,OAAO,CAACyL,MAAM,EAAE/K,UAAU,CAACV,OAAO,CAACwL,IAAI,CAAC,CAAC,CAAC,EAAE9K,UAAU,CAACV,OAAO,CAAC4L,IAAI,EAAElL,UAAU,CAACV,OAAO,CAACyL,MAAM,CAAC,CAAC;EAC/N;AACF;AACA;AACA;AACA;AACA;AACA;EACElG,QAAQ,EAAE7E,UAAU,CAACV,OAAO,CAAC0L,MAAM;EACnC;AACF;AACA;AACA;EACErL,KAAK,EAAEK,UAAU,CAACV,OAAO,CAACyL,MAAM;EAChC;AACF;AACA;AACA;AACA;EACE9I,UAAU,EAAEjC,UAAU,CAACV,OAAO,CAACgM,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EACrD;AACF;AACA;AACA;EACEtJ,WAAW,EAAEhC,UAAU,CAACV,OAAO,CAACgM,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9C,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}