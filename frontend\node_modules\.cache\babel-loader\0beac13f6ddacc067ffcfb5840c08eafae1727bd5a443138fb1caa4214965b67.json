{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert } from '@mui/material';\nimport { TrendingUp, TrendingDown, Business, AccountBalance } from '@mui/icons-material';\nimport { apiService } from '../services/apiService';\nimport StatCard from '../components/StatCard';\nimport RecentTransactions from '../components/RecentTransactions';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport LastFetchedStatus from '../components/LastFetchedStatus';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Fetch all dashboard data in parallel\n        const [summaryResponse, recentTransactionsResponse, topCompaniesResponse, systemStatusResponse] = await Promise.all([apiService.getAnalyticsSummary(), apiService.getTransactions({\n          limit: 10,\n          sort_by: 'transaction_date',\n          sort_order: 'desc'\n        }), apiService.getTopCompanies({\n          limit: 10\n        }), apiService.getSystemStatus()]);\n        setData({\n          summary: summaryResponse.data,\n          recent_transactions: recentTransactionsResponse.data.transactions,\n          top_companies: topCompaniesResponse.data,\n          system_status: systemStatusResponse.data\n        });\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err);\n        setError('Failed to load dashboard data. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDashboardData();\n\n    // Set up auto-refresh every 5 minutes\n    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  if (!data) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"No data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this);\n  }\n  const {\n    summary,\n    recent_transactions,\n    top_companies,\n    system_status\n  } = data;\n  const formatCurrency = value => {\n    if (value >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (value >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n  const netValue = summary.total_buy_value - summary.total_sell_value;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Overview of NSE insider trading activities\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(LastFetchedStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Transactions\",\n          value: summary.total_transactions.toLocaleString(),\n          icon: /*#__PURE__*/_jsxDEV(AccountBalance, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 19\n          }, this),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Buy Value\",\n          value: formatCurrency(summary.total_buy_value),\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 19\n          }, this),\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Sell Value\",\n          value: formatCurrency(summary.total_sell_value),\n          icon: /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 19\n          }, this),\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Net Value\",\n          value: formatCurrency(Math.abs(netValue)),\n          subtitle: netValue >= 0 ? 'Net Buying' : 'Net Selling',\n          icon: netValue >= 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 35\n          }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 52\n          }, this),\n          color: netValue >= 0 ? 'success' : 'error'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Companies\",\n          value: summary.unique_companies.toLocaleString(),\n          icon: /*#__PURE__*/_jsxDEV(Business, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 19\n          }, this),\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          lg: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Top Companies by Transaction Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TopCompaniesChart, {\n              data: top_companies\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          lg: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Transaction Trends (Last 30 Days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TransactionTrendsChart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Recent Transactions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecentTransactions, {\n              transactions: recent_transactions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"RiL7vLwmC7ZWXKL/bXt2EIBjBYk=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "TrendingUp", "TrendingDown", "Business", "AccountBalance", "apiService", "StatCard", "RecentTransactions", "TopCompaniesChart", "TransactionTrendsChart", "LastFetchedStatus", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "fetchDashboardData", "summaryResponse", "recentTransactionsResponse", "topCompaniesResponse", "systemStatusResponse", "Promise", "all", "getAnalyticsSummary", "getTransactions", "limit", "sort_by", "sort_order", "getTopCompanies", "getSystemStatus", "summary", "recent_transactions", "transactions", "top_companies", "system_status", "err", "console", "interval", "setInterval", "clearInterval", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "severity", "formatCurrency", "value", "toFixed", "toLocaleString", "netValue", "total_buy_value", "total_sell_value", "mb", "variant", "gutterBottom", "color", "container", "spacing", "sx", "xs", "sm", "md", "title", "total_transactions", "icon", "Math", "abs", "subtitle", "unique_companies", "lg", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  CircularProgress,\n  Al<PERSON>,\n  Chip,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Business,\n  Person,\n  AccountBalance,\n} from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport { apiService } from '../services/apiService';\nimport StatCard from '../components/StatCard';\nimport RecentTransactions from '../components/RecentTransactions';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport LastFetchedStatus from '../components/LastFetchedStatus';\n\ninterface DashboardData {\n  summary: {\n    total_transactions: number;\n    total_buy_value: number;\n    total_sell_value: number;\n    unique_companies: number;\n    unique_persons: number;\n    date_range: {\n      earliest: string;\n      latest: string;\n    };\n  };\n  recent_transactions: any[];\n  top_companies: any[];\n  system_status: {\n    api_status: string;\n    database_status: {\n      total_transactions: number;\n      total_companies: number;\n      date_range?: {\n        earliest: string;\n        latest: string;\n      };\n      last_updated?: string;\n    };\n    scraper_status: {\n      last_execution?: string;\n      status: string;\n      records_fetched: number;\n      records_inserted: number;\n      records_skipped: number;\n      error_message?: string;\n    };\n    timestamp: string;\n  };\n}\n\nconst Dashboard: React.FC = () => {\n  const [data, setData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Fetch all dashboard data in parallel\n        const [summaryResponse, recentTransactionsResponse, topCompaniesResponse, systemStatusResponse] = await Promise.all([\n          apiService.getAnalyticsSummary(),\n          apiService.getTransactions({ limit: 10, sort_by: 'transaction_date', sort_order: 'desc' }),\n          apiService.getTopCompanies({ limit: 10 }),\n          apiService.getSystemStatus(),\n        ]);\n\n        setData({\n          summary: summaryResponse.data,\n          recent_transactions: recentTransactionsResponse.data.transactions,\n          top_companies: topCompaniesResponse.data,\n          system_status: systemStatusResponse.data,\n        });\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err);\n        setError('Failed to load dashboard data. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n\n    // Set up auto-refresh every 5 minutes\n    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  if (!data) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"info\">No data available</Alert>\n      </Box>\n    );\n  }\n\n  const { summary, recent_transactions, top_companies, system_status } = data;\n\n  const formatCurrency = (value: number) => {\n    if (value >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (value >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  const netValue = summary.total_buy_value - summary.total_sell_value;\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Dashboard\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Overview of NSE insider trading activities\n        </Typography>\n      </Box>\n\n      {/* System Status */}\n      <Box mb={3}>\n        <LastFetchedStatus />\n      </Box>\n\n      {/* Key Metrics */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Total Transactions\"\n            value={summary.total_transactions.toLocaleString()}\n            icon={<AccountBalance />}\n            color=\"primary\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Buy Value\"\n            value={formatCurrency(summary.total_buy_value)}\n            icon={<TrendingUp />}\n            color=\"success\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Sell Value\"\n            value={formatCurrency(summary.total_sell_value)}\n            icon={<TrendingDown />}\n            color=\"error\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Net Value\"\n            value={formatCurrency(Math.abs(netValue))}\n            subtitle={netValue >= 0 ? 'Net Buying' : 'Net Selling'}\n            icon={netValue >= 0 ? <TrendingUp /> : <TrendingDown />}\n            color={netValue >= 0 ? 'success' : 'error'}\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Companies\"\n            value={summary.unique_companies.toLocaleString()}\n            icon={<Business />}\n            color=\"info\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Charts and Tables */}\n      <Grid container spacing={3}>\n        {/* Top Companies Chart */}\n        <Grid size={{ xs: 12, lg: 6 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top Companies by Transaction Value\n              </Typography>\n              <TopCompaniesChart data={top_companies} />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Transaction Trends */}\n        <Grid size={{ xs: 12, lg: 6 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Transaction Trends (Last 30 Days)\n              </Typography>\n              <TransactionTrendsChart />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Recent Transactions */}\n        <Grid size={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Recent Transactions\n              </Typography>\n              <RecentTransactions transactions={recent_transactions} />\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,QAEA,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,QAAQ,EAERC,cAAc,QACT,qBAAqB;AAE5B,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuChE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAuB,IAAI,CAAC;EAC5D,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAM4B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,MAAM,CAACE,eAAe,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,oBAAoB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClHtB,UAAU,CAACuB,mBAAmB,CAAC,CAAC,EAChCvB,UAAU,CAACwB,eAAe,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE,kBAAkB;UAAEC,UAAU,EAAE;QAAO,CAAC,CAAC,EAC1F3B,UAAU,CAAC4B,eAAe,CAAC;UAAEH,KAAK,EAAE;QAAG,CAAC,CAAC,EACzCzB,UAAU,CAAC6B,eAAe,CAAC,CAAC,CAC7B,CAAC;QAEFlB,OAAO,CAAC;UACNmB,OAAO,EAAEb,eAAe,CAACP,IAAI;UAC7BqB,mBAAmB,EAAEb,0BAA0B,CAACR,IAAI,CAACsB,YAAY;UACjEC,aAAa,EAAEd,oBAAoB,CAACT,IAAI;UACxCwB,aAAa,EAAEd,oBAAoB,CAACV;QACtC,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOyB,GAAG,EAAE;QACZC,OAAO,CAACtB,KAAK,CAAC,gCAAgC,EAAEqB,GAAG,CAAC;QACpDpB,QAAQ,CAAC,kDAAkD,CAAC;MAC9D,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,kBAAkB,CAAC,CAAC;;IAEpB;IACA,MAAMqB,QAAQ,GAAGC,WAAW,CAACtB,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAC/D,OAAO,MAAMuB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIzB,OAAO,EAAE;IACX,oBACEL,OAAA,CAAClB,GAAG;MAACmD,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/ErC,OAAA,CAACb,gBAAgB;QAACmD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAInC,KAAK,EAAE;IACT,oBACEP,OAAA,CAAClB,GAAG;MAAC6D,CAAC,EAAE,CAAE;MAAAN,QAAA,eACRrC,OAAA,CAACZ,KAAK;QAACwD,QAAQ,EAAC,OAAO;QAAAP,QAAA,EAAE9B;MAAK;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,IAAI,CAACvC,IAAI,EAAE;IACT,oBACEH,OAAA,CAAClB,GAAG;MAAC6D,CAAC,EAAE,CAAE;MAAAN,QAAA,eACRrC,OAAA,CAACZ,KAAK;QAACwD,QAAQ,EAAC,MAAM;QAAAP,QAAA,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;EAEA,MAAM;IAAEnB,OAAO;IAAEC,mBAAmB;IAAEE,aAAa;IAAEC;EAAc,CAAC,GAAGxB,IAAI;EAE3E,MAAM0C,cAAc,GAAIC,KAAa,IAAK;IACxC,IAAIA,KAAK,IAAI,QAAQ,EAAE;MACrB,OAAO,IAAI,CAACA,KAAK,GAAG,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;IAC9C,CAAC,MAAM,IAAID,KAAK,IAAI,MAAM,EAAE;MAC1B,OAAO,IAAI,CAACA,KAAK,GAAG,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3C,CAAC,MAAM;MACL,OAAO,IAAID,KAAK,CAACE,cAAc,CAAC,CAAC,EAAE;IACrC;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG1B,OAAO,CAAC2B,eAAe,GAAG3B,OAAO,CAAC4B,gBAAgB;EAEnE,oBACEnD,OAAA,CAAClB,GAAG;IAAAuD,QAAA,gBAEFrC,OAAA,CAAClB,GAAG;MAACsE,EAAE,EAAE,CAAE;MAAAf,QAAA,gBACTrC,OAAA,CAACd,UAAU;QAACmE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAjB,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1C,OAAA,CAACd,UAAU;QAACmE,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,gBAAgB;QAAAlB,QAAA,EAAC;MAEnD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN1C,OAAA,CAAClB,GAAG;MAACsE,EAAE,EAAE,CAAE;MAAAf,QAAA,eACTrC,OAAA,CAACF,iBAAiB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGN1C,OAAA,CAACjB,IAAI;MAACyE,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAf,QAAA,gBACxCrC,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEqB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAAxB,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPoE,KAAK,EAAC,oBAAoB;UAC1BhB,KAAK,EAAEvB,OAAO,CAACwC,kBAAkB,CAACf,cAAc,CAAC,CAAE;UACnDgB,IAAI,eAAEhE,OAAA,CAACR,cAAc;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBa,KAAK,EAAC;QAAS;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEqB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAAxB,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPoE,KAAK,EAAC,WAAW;UACjBhB,KAAK,EAAED,cAAc,CAACtB,OAAO,CAAC2B,eAAe,CAAE;UAC/Cc,IAAI,eAAEhE,OAAA,CAACX,UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBa,KAAK,EAAC;QAAS;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEqB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAAxB,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPoE,KAAK,EAAC,YAAY;UAClBhB,KAAK,EAAED,cAAc,CAACtB,OAAO,CAAC4B,gBAAgB,CAAE;UAChDa,IAAI,eAAEhE,OAAA,CAACV,YAAY;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,KAAK,EAAC;QAAO;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEqB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAAxB,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPoE,KAAK,EAAC,WAAW;UACjBhB,KAAK,EAAED,cAAc,CAACoB,IAAI,CAACC,GAAG,CAACjB,QAAQ,CAAC,CAAE;UAC1CkB,QAAQ,EAAElB,QAAQ,IAAI,CAAC,GAAG,YAAY,GAAG,aAAc;UACvDe,IAAI,EAAEf,QAAQ,IAAI,CAAC,gBAAGjD,OAAA,CAACX,UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG1C,OAAA,CAACV,YAAY;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxDa,KAAK,EAAEN,QAAQ,IAAI,CAAC,GAAG,SAAS,GAAG;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEqB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAAxB,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPoE,KAAK,EAAC,WAAW;UACjBhB,KAAK,EAAEvB,OAAO,CAAC6C,gBAAgB,CAACpB,cAAc,CAAC,CAAE;UACjDgB,IAAI,eAAEhE,OAAA,CAACT,QAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBa,KAAK,EAAC;QAAM;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1C,OAAA,CAACjB,IAAI;MAACyE,SAAS;MAACC,OAAO,EAAE,CAAE;MAAApB,QAAA,gBAEzBrC,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEqB,EAAE,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAhC,QAAA,eAC5BrC,OAAA,CAAChB,IAAI;UAAAqD,QAAA,eACHrC,OAAA,CAACf,WAAW;YAAAoD,QAAA,gBACVrC,OAAA,CAACd,UAAU;cAACmE,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAjB,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACJ,iBAAiB;cAACO,IAAI,EAAEuB;YAAc;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEqB,EAAE,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAhC,QAAA,eAC5BrC,OAAA,CAAChB,IAAI;UAAAqD,QAAA,eACHrC,OAAA,CAACf,WAAW;YAAAoD,QAAA,gBACVrC,OAAA,CAACd,UAAU;cAACmE,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAjB,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACH,sBAAsB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACbrC,OAAA,CAAChB,IAAI;UAAAqD,QAAA,eACHrC,OAAA,CAACf,WAAW;YAAAoD,QAAA,gBACVrC,OAAA,CAACd,UAAU;cAACmE,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAjB,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACL,kBAAkB;cAAC8B,YAAY,EAAED;YAAoB;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxC,EAAA,CApLID,SAAmB;AAAAqE,EAAA,GAAnBrE,SAAmB;AAsLzB,eAAeA,SAAS;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}