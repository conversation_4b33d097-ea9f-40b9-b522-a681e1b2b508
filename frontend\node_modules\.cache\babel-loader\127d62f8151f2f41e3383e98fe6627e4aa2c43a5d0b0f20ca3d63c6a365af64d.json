{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useClockReferenceDate = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _valueManagers = require(\"../utils/valueManagers\");\nvar _dateUtils = require(\"../utils/date-utils\");\nvar _getDefaultReferenceDate = require(\"../utils/getDefaultReferenceDate\");\nconst useClockReferenceDate = ({\n  value,\n  referenceDate: referenceDateProp,\n  utils,\n  props,\n  timezone\n}) => {\n  const referenceDate = React.useMemo(() => _valueManagers.singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    referenceDate: referenceDateProp,\n    granularity: _getDefaultReferenceDate.SECTION_TYPE_GRANULARITY.day,\n    timezone,\n    getTodayDate: () => (0, _dateUtils.getTodayDate)(utils, timezone, 'date')\n  }),\n  // We only want to compute the reference date on mount.\n  [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  return value ?? referenceDate;\n};\nexports.useClockReferenceDate = useClockReferenceDate;", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "Object", "defineProperty", "exports", "value", "useClockReferenceDate", "React", "_valueManagers", "_dateUtils", "_getDefaultReferenceDate", "referenceDate", "referenceDateProp", "utils", "props", "timezone", "useMemo", "singleItemValueManager", "getInitialReferenceValue", "granularity", "SECTION_TYPE_GRANULARITY", "day", "getTodayDate"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useClockReferenceDate.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useClockReferenceDate = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _valueManagers = require(\"../utils/valueManagers\");\nvar _dateUtils = require(\"../utils/date-utils\");\nvar _getDefaultReferenceDate = require(\"../utils/getDefaultReferenceDate\");\nconst useClockReferenceDate = ({\n  value,\n  referenceDate: referenceDateProp,\n  utils,\n  props,\n  timezone\n}) => {\n  const referenceDate = React.useMemo(() => _valueManagers.singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    referenceDate: referenceDateProp,\n    granularity: _getDefaultReferenceDate.SECTION_TYPE_GRANULARITY.day,\n    timezone,\n    getTodayDate: () => (0, _dateUtils.getTodayDate)(utils, timezone, 'date')\n  }),\n  // We only want to compute the reference date on mount.\n  [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  return value ?? referenceDate;\n};\nexports.useClockReferenceDate = useClockReferenceDate;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,qBAAqB,GAAG,KAAK,CAAC;AACtC,IAAIC,KAAK,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,cAAc,GAAGR,OAAO,CAAC,wBAAwB,CAAC;AACtD,IAAIS,UAAU,GAAGT,OAAO,CAAC,qBAAqB,CAAC;AAC/C,IAAIU,wBAAwB,GAAGV,OAAO,CAAC,kCAAkC,CAAC;AAC1E,MAAMM,qBAAqB,GAAGA,CAAC;EAC7BD,KAAK;EACLM,aAAa,EAAEC,iBAAiB;EAChCC,KAAK;EACLC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMJ,aAAa,GAAGJ,KAAK,CAACS,OAAO,CAAC,MAAMR,cAAc,CAACS,sBAAsB,CAACC,wBAAwB,CAAC;IACvGb,KAAK;IACLQ,KAAK;IACLC,KAAK;IACLH,aAAa,EAAEC,iBAAiB;IAChCO,WAAW,EAAET,wBAAwB,CAACU,wBAAwB,CAACC,GAAG;IAClEN,QAAQ;IACRO,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC,EAAEb,UAAU,CAACa,YAAY,EAAET,KAAK,EAAEE,QAAQ,EAAE,MAAM;EAC1E,CAAC,CAAC;EACF;EACA,EAAE,CAAC;EACH,CAAC;EACD,OAAOV,KAAK,IAAIM,aAAa;AAC/B,CAAC;AACDP,OAAO,CAACE,qBAAqB,GAAGA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}