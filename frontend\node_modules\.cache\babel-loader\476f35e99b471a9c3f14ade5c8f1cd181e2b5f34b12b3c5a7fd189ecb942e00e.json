{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\pages\\\\Companies.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, TextField, List, ListItem, ListItemText, ListItemSecondaryAction, Chip, Alert, CircularProgress, InputAdornment } from '@mui/material';\nimport { Search, Business } from '@mui/icons-material';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Companies = () => {\n  _s();\n  const [companies, setCompanies] = useState([]);\n  const [filteredCompanies, setFilteredCompanies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  useEffect(() => {\n    const fetchCompanies = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const response = await apiService.getTopCompanies({\n          limit: 100\n        });\n        setCompanies(response.data);\n        setFilteredCompanies(response.data);\n      } catch (err) {\n        console.error('Error fetching companies:', err);\n        setError('Failed to load companies data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCompanies();\n  }, []);\n  useEffect(() => {\n    if (!searchQuery) {\n      setFilteredCompanies(companies);\n    } else {\n      const filtered = companies.filter(company => company.symbol.toLowerCase().includes(searchQuery.toLowerCase()) || company.company_name.toLowerCase().includes(searchQuery.toLowerCase()));\n      setFilteredCompanies(filtered);\n    }\n  }, [searchQuery, companies]);\n  const formatCurrency = value => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n  const formatDate = dateString => {\n    try {\n      return new Date(dateString).toLocaleDateString();\n    } catch {\n      return dateString;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Companies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Browse companies with insider trading activity\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search companies by symbol or name...\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Business, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"primary.main\",\n                  children: companies.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Companies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Business, {\n                color: \"success\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"success.main\",\n                  children: companies.reduce((sum, c) => sum + c.transaction_count, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Transactions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Business, {\n                color: \"info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"info.main\",\n                  children: formatCurrency(companies.reduce((sum, c) => sum + c.total_value, 0))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Value\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [\"Companies (\", filteredCompanies.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), filteredCompanies.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          minHeight: \"200px\",\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: searchQuery ? 'No companies found matching your search' : 'No companies data available'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          children: filteredCompanies.map((company, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            divider: index < filteredCompanies.length - 1,\n            sx: {\n              '&:hover': {\n                backgroundColor: 'action.hover'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: 600,\n                  children: company.symbol\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${company.transaction_count} transactions`,\n                  size: \"small\",\n                  variant: \"outlined\",\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 23\n              }, this),\n              secondary: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.primary\",\n                  children: company.company_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: [company.unique_insiders, \" unique insiders \\u2022 Last activity: \", formatDate(company.latest_transaction_date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                textAlign: \"right\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: 600,\n                  color: \"primary.main\",\n                  children: formatCurrency(company.total_value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"success.main\",\n                  children: [\"Buy: \", formatCurrency(company.total_buy_value)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error.main\",\n                  children: [\"Sell: \", formatCurrency(company.total_sell_value)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this)]\n          }, company.symbol, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(Companies, \"DY3Lxjs5YkmYh5Kbt+vTO+5i+y0=\");\n_c = Companies;\nexport default Companies;\nvar _c;\n$RefreshReg$(_c, \"Companies\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "TextField", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Chip", "<PERSON><PERSON>", "CircularProgress", "InputAdornment", "Search", "Business", "apiService", "jsxDEV", "_jsxDEV", "Companies", "_s", "companies", "setCompanies", "filteredCompanies", "setFilteredCompanies", "loading", "setLoading", "error", "setError", "searchQuery", "setSearch<PERSON>uery", "fetchCompanies", "response", "getTopCompanies", "limit", "data", "err", "console", "filtered", "filter", "company", "symbol", "toLowerCase", "includes", "company_name", "formatCurrency", "value", "undefined", "isNaN", "absValue", "Math", "abs", "toFixed", "toLocaleString", "formatDate", "dateString", "Date", "toLocaleDateString", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "severity", "mb", "variant", "gutterBottom", "color", "sx", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "container", "spacing", "xs", "sm", "gap", "length", "reduce", "sum", "c", "transaction_count", "total_value", "map", "index", "divider", "backgroundColor", "primary", "fontWeight", "label", "secondary", "unique_insiders", "latest_transaction_date", "textAlign", "total_buy_value", "total_sell_value", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Companies.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  TextField,\n  Button,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Chip,\n  Alert,\n  CircularProgress,\n  InputAdornment,\n} from '@mui/material';\nimport { Search, Business } from '@mui/icons-material';\nimport { apiService, CompanyActivity } from '../services/apiService';\n\nconst Companies: React.FC = () => {\n  const [companies, setCompanies] = useState<CompanyActivity[]>([]);\n  const [filteredCompanies, setFilteredCompanies] = useState<CompanyActivity[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  useEffect(() => {\n    const fetchCompanies = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const response = await apiService.getTopCompanies({ limit: 100 });\n        setCompanies(response.data);\n        setFilteredCompanies(response.data);\n      } catch (err) {\n        console.error('Error fetching companies:', err);\n        setError('Failed to load companies data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCompanies();\n  }, []);\n\n  useEffect(() => {\n    if (!searchQuery) {\n      setFilteredCompanies(companies);\n    } else {\n      const filtered = companies.filter(\n        (company) =>\n          company.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||\n          company.company_name.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredCompanies(filtered);\n    }\n  }, [searchQuery, companies]);\n\n  const formatCurrency = (value: number | null | undefined) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    try {\n      return new Date(dateString).toLocaleDateString();\n    } catch {\n      return dateString;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Companies\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Browse companies with insider trading activity\n        </Typography>\n      </Box>\n\n      {/* Search */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <TextField\n            fullWidth\n            placeholder=\"Search companies by symbol or name...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <Search />\n                </InputAdornment>\n              ),\n            }}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Summary Stats */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid size={{ xs: 12, sm: 4 }}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"primary\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"primary.main\">\n                    {companies.length}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Companies\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid size={{ xs: 12, sm: 4 }}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"success\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"success.main\">\n                    {companies.reduce((sum, c) => sum + c.transaction_count, 0).toLocaleString()}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Transactions\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid size={{ xs: 12, sm: 4 }}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"info\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"info.main\">\n                    {formatCurrency(companies.reduce((sum, c) => sum + c.total_value, 0))}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Value\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Companies List */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Companies ({filteredCompanies.length})\n          </Typography>\n          \n          {filteredCompanies.length === 0 ? (\n            <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                {searchQuery ? 'No companies found matching your search' : 'No companies data available'}\n              </Typography>\n            </Box>\n          ) : (\n            <List>\n              {filteredCompanies.map((company, index) => (\n                <ListItem\n                  key={company.symbol}\n                  divider={index < filteredCompanies.length - 1}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'action.hover',\n                    },\n                  }}\n                >\n                  <ListItemText\n                    primary={\n                      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                        <Typography variant=\"subtitle1\" fontWeight={600}>\n                          {company.symbol}\n                        </Typography>\n                        <Chip\n                          label={`${company.transaction_count} transactions`}\n                          size=\"small\"\n                          variant=\"outlined\"\n                          color=\"primary\"\n                        />\n                      </Box>\n                    }\n                    secondary={\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.primary\">\n                          {company.company_name}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {company.unique_insiders} unique insiders • \n                          Last activity: {formatDate(company.latest_transaction_date)}\n                        </Typography>\n                      </Box>\n                    }\n                  />\n                  <ListItemSecondaryAction>\n                    <Box textAlign=\"right\">\n                      <Typography variant=\"subtitle1\" fontWeight={600} color=\"primary.main\">\n                        {formatCurrency(company.total_value)}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"success.main\">\n                        Buy: {formatCurrency(company.total_buy_value)}\n                      </Typography>\n                      <br />\n                      <Typography variant=\"caption\" color=\"error.main\">\n                        Sell: {formatCurrency(company.total_sell_value)}\n                      </Typography>\n                    </Box>\n                  </ListItemSecondaryAction>\n                </ListItem>\n              ))}\n            </List>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default Companies;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,SAAS,EAETC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,QACT,eAAe;AACtB,SAASC,MAAM,EAAEC,QAAQ,QAAQ,qBAAqB;AACtD,SAASC,UAAU,QAAyB,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAoB,EAAE,CAAC;EACjE,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAoB,EAAE,CAAC;EACjF,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd,MAAMgC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QAEd,MAAMI,QAAQ,GAAG,MAAMhB,UAAU,CAACiB,eAAe,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC,CAAC;QACjEZ,YAAY,CAACU,QAAQ,CAACG,IAAI,CAAC;QAC3BX,oBAAoB,CAACQ,QAAQ,CAACG,IAAI,CAAC;MACrC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACV,KAAK,CAAC,2BAA2B,EAAES,GAAG,CAAC;QAC/CR,QAAQ,CAAC,+BAA+B,CAAC;MAC3C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENhC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8B,WAAW,EAAE;MAChBL,oBAAoB,CAACH,SAAS,CAAC;IACjC,CAAC,MAAM;MACL,MAAMiB,QAAQ,GAAGjB,SAAS,CAACkB,MAAM,CAC9BC,OAAO,IACNA,OAAO,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,WAAW,CAACa,WAAW,CAAC,CAAC,CAAC,IAChEF,OAAO,CAACI,YAAY,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,WAAW,CAACa,WAAW,CAAC,CAAC,CACzE,CAAC;MACDlB,oBAAoB,CAACc,QAAQ,CAAC;IAChC;EACF,CAAC,EAAE,CAACT,WAAW,EAAER,SAAS,CAAC,CAAC;EAE5B,MAAMwB,cAAc,GAAIC,KAAgC,IAAK;IAC3D,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIC,KAAK,CAACF,KAAK,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC;IAChC,IAAIG,QAAQ,IAAI,QAAQ,EAAE;MACxB,OAAO,IAAI,CAACH,KAAK,GAAG,QAAQ,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI;IAC9C,CAAC,MAAM,IAAIH,QAAQ,IAAI,MAAM,EAAE;MAC7B,OAAO,IAAI,CAACH,KAAK,GAAG,MAAM,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3C,CAAC,MAAM;MACL,OAAO,IAAIN,KAAK,CAACO,cAAc,CAAC,CAAC,EAAE;IACrC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,IAAI;MACF,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;IAClD,CAAC,CAAC,MAAM;MACN,OAAOF,UAAU;IACnB;EACF,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACEP,OAAA,CAAClB,GAAG;MAAC0D,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E5C,OAAA,CAACN,gBAAgB;QAACmD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAIxC,KAAK,EAAE;IACT,oBACET,OAAA,CAAClB,GAAG;MAACoE,CAAC,EAAE,CAAE;MAAAN,QAAA,eACR5C,OAAA,CAACP,KAAK;QAAC0D,QAAQ,EAAC,OAAO;QAAAP,QAAA,EAAEnC;MAAK;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,oBACEjD,OAAA,CAAClB,GAAG;IAAA8D,QAAA,gBAEF5C,OAAA,CAAClB,GAAG;MAACsE,EAAE,EAAE,CAAE;MAAAR,QAAA,gBACT5C,OAAA,CAACjB,UAAU;QAACsE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAV,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjD,OAAA,CAACjB,UAAU;QAACsE,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,gBAAgB;QAAAX,QAAA,EAAC;MAEnD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNjD,OAAA,CAAChB,IAAI;MAACwE,EAAE,EAAE;QAAEJ,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAClB5C,OAAA,CAACf,WAAW;QAAA2D,QAAA,eACV5C,OAAA,CAACb,SAAS;UACRsE,SAAS;UACTC,WAAW,EAAC,uCAAuC;UACnD9B,KAAK,EAAEjB,WAAY;UACnBgD,QAAQ,EAAGC,CAAC,IAAKhD,cAAc,CAACgD,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAE;UAChDkC,UAAU,EAAE;YACVC,cAAc,eACZ/D,OAAA,CAACL,cAAc;cAACqE,QAAQ,EAAC,OAAO;cAAApB,QAAA,eAC9B5C,OAAA,CAACJ,MAAM;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjD,OAAA,CAACd,IAAI;MAAC+E,SAAS;MAACC,OAAO,EAAE,CAAE;MAACV,EAAE,EAAE;QAAEJ,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACxC5C,OAAA,CAACd,IAAI;QAAC2D,IAAI,EAAE;UAAEsB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,eAC5B5C,OAAA,CAAChB,IAAI;UAAA4D,QAAA,eACH5C,OAAA,CAACf,WAAW;YAAA2D,QAAA,eACV5C,OAAA,CAAClB,GAAG;cAAC0D,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAC2B,GAAG,EAAE,CAAE;cAAAzB,QAAA,gBAC7C5C,OAAA,CAACH,QAAQ;gBAAC0D,KAAK,EAAC;cAAS;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BjD,OAAA,CAAClB,GAAG;gBAAA8D,QAAA,gBACF5C,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,IAAI;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAC1CzC,SAAS,CAACmE;gBAAM;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACbjD,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,EAAC;gBAEnD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPjD,OAAA,CAACd,IAAI;QAAC2D,IAAI,EAAE;UAAEsB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,eAC5B5C,OAAA,CAAChB,IAAI;UAAA4D,QAAA,eACH5C,OAAA,CAACf,WAAW;YAAA2D,QAAA,eACV5C,OAAA,CAAClB,GAAG;cAAC0D,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAC2B,GAAG,EAAE,CAAE;cAAAzB,QAAA,gBAC7C5C,OAAA,CAACH,QAAQ;gBAAC0D,KAAK,EAAC;cAAS;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BjD,OAAA,CAAClB,GAAG;gBAAA8D,QAAA,gBACF5C,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,IAAI;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAC1CzC,SAAS,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACC,iBAAiB,EAAE,CAAC,CAAC,CAACvC,cAAc,CAAC;gBAAC;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACbjD,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,EAAC;gBAEnD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPjD,OAAA,CAACd,IAAI;QAAC2D,IAAI,EAAE;UAAEsB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,eAC5B5C,OAAA,CAAChB,IAAI;UAAA4D,QAAA,eACH5C,OAAA,CAACf,WAAW;YAAA2D,QAAA,eACV5C,OAAA,CAAClB,GAAG;cAAC0D,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAC2B,GAAG,EAAE,CAAE;cAAAzB,QAAA,gBAC7C5C,OAAA,CAACH,QAAQ;gBAAC0D,KAAK,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBjD,OAAA,CAAClB,GAAG;gBAAA8D,QAAA,gBACF5C,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,IAAI;kBAACE,KAAK,EAAC,WAAW;kBAAAX,QAAA,EACvCjB,cAAc,CAACxB,SAAS,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACE,WAAW,EAAE,CAAC,CAAC;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACbjD,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,EAAC;gBAEnD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPjD,OAAA,CAAChB,IAAI;MAAA4D,QAAA,eACH5C,OAAA,CAACf,WAAW;QAAA2D,QAAA,gBACV5C,OAAA,CAACjB,UAAU;UAACsE,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,GAAC,aACzB,EAACvC,iBAAiB,CAACiE,MAAM,EAAC,GACvC;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ5C,iBAAiB,CAACiE,MAAM,KAAK,CAAC,gBAC7BtE,OAAA,CAAClB,GAAG;UAAC0D,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACC,UAAU,EAAC,QAAQ;UAACC,SAAS,EAAC,OAAO;UAAAC,QAAA,eAC/E5C,OAAA,CAACjB,UAAU;YAACsE,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAX,QAAA,EAC/CjC,WAAW,GAAG,yCAAyC,GAAG;UAA6B;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAENjD,OAAA,CAACZ,IAAI;UAAAwD,QAAA,EACFvC,iBAAiB,CAACuE,GAAG,CAAC,CAACtD,OAAO,EAAEuD,KAAK,kBACpC7E,OAAA,CAACX,QAAQ;YAEPyF,OAAO,EAAED,KAAK,GAAGxE,iBAAiB,CAACiE,MAAM,GAAG,CAAE;YAC9Cd,EAAE,EAAE;cACF,SAAS,EAAE;gBACTuB,eAAe,EAAE;cACnB;YACF,CAAE;YAAAnC,QAAA,gBAEF5C,OAAA,CAACV,YAAY;cACX0F,OAAO,eACLhF,OAAA,CAAClB,GAAG;gBAAC0D,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAAC2B,GAAG,EAAE,CAAE;gBAAAzB,QAAA,gBAC7C5C,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,WAAW;kBAAC4B,UAAU,EAAE,GAAI;kBAAArC,QAAA,EAC7CtB,OAAO,CAACC;gBAAM;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACbjD,OAAA,CAACR,IAAI;kBACH0F,KAAK,EAAE,GAAG5D,OAAO,CAACoD,iBAAiB,eAAgB;kBACnD7B,IAAI,EAAC,OAAO;kBACZQ,OAAO,EAAC,UAAU;kBAClBE,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;cACDkC,SAAS,eACPnF,OAAA,CAAClB,GAAG;gBAAA8D,QAAA,gBACF5C,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAC7CtB,OAAO,CAACI;gBAAY;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACbjD,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,SAAS;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,GACjDtB,OAAO,CAAC8D,eAAe,EAAC,yCACV,EAAChD,UAAU,CAACd,OAAO,CAAC+D,uBAAuB,CAAC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFjD,OAAA,CAACT,uBAAuB;cAAAqD,QAAA,eACtB5C,OAAA,CAAClB,GAAG;gBAACwG,SAAS,EAAC,OAAO;gBAAA1C,QAAA,gBACpB5C,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,WAAW;kBAAC4B,UAAU,EAAE,GAAI;kBAAC1B,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAClEjB,cAAc,CAACL,OAAO,CAACqD,WAAW;gBAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACbjD,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,SAAS;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,GAAC,OAC5C,EAACjB,cAAc,CAACL,OAAO,CAACiE,eAAe,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACbjD,OAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNjD,OAAA,CAACjB,UAAU;kBAACsE,OAAO,EAAC,SAAS;kBAACE,KAAK,EAAC,YAAY;kBAAAX,QAAA,GAAC,QACzC,EAACjB,cAAc,CAACL,OAAO,CAACkE,gBAAgB,CAAC;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACiB,CAAC;UAAA,GA/CrB3B,OAAO,CAACC,MAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDX,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC/C,EAAA,CA9OID,SAAmB;AAAAwF,EAAA,GAAnBxF,SAAmB;AAgPzB,eAAeA,SAAS;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}