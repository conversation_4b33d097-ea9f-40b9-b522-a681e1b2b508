{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useField = void 0;\nvar _useFieldV7TextField = require(\"./useFieldV7TextField\");\nvar _useFieldV6TextField = require(\"./useFieldV6TextField\");\nvar _useNullableFieldPrivateContext = require(\"../useNullableFieldPrivateContext\");\nconst useField = parameters => {\n  const fieldPrivateContext = (0, _useNullableFieldPrivateContext.useNullableFieldPrivateContext)();\n  const enableAccessibleFieldDOMStructure = parameters.props.enableAccessibleFieldDOMStructure ?? fieldPrivateContext?.enableAccessibleFieldDOMStructure ?? true;\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? _useFieldV7TextField.useFieldV7TextField : _useFieldV6TextField.useFieldV6TextField;\n  return useFieldTextField(parameters);\n};\nexports.useField = useField;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "useField", "_useFieldV7TextField", "require", "_useFieldV6TextField", "_useNullableFieldPrivateContext", "parameters", "fieldPrivateContext", "useNullableFieldPrivateContext", "enableAccessibleFieldDOMStructure", "props", "useFieldTextField", "useFieldV7TextField", "useFieldV6TextField"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useField.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useField = void 0;\nvar _useFieldV7TextField = require(\"./useFieldV7TextField\");\nvar _useFieldV6TextField = require(\"./useFieldV6TextField\");\nvar _useNullableFieldPrivateContext = require(\"../useNullableFieldPrivateContext\");\nconst useField = parameters => {\n  const fieldPrivateContext = (0, _useNullableFieldPrivateContext.useNullableFieldPrivateContext)();\n  const enableAccessibleFieldDOMStructure = parameters.props.enableAccessibleFieldDOMStructure ?? fieldPrivateContext?.enableAccessibleFieldDOMStructure ?? true;\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? _useFieldV7TextField.useFieldV7TextField : _useFieldV6TextField.useFieldV6TextField;\n  return useFieldTextField(parameters);\n};\nexports.useField = useField;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAIC,oBAAoB,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAC3D,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC3D,IAAIE,+BAA+B,GAAGF,OAAO,CAAC,mCAAmC,CAAC;AAClF,MAAMF,QAAQ,GAAGK,UAAU,IAAI;EAC7B,MAAMC,mBAAmB,GAAG,CAAC,CAAC,EAAEF,+BAA+B,CAACG,8BAA8B,EAAE,CAAC;EACjG,MAAMC,iCAAiC,GAAGH,UAAU,CAACI,KAAK,CAACD,iCAAiC,IAAIF,mBAAmB,EAAEE,iCAAiC,IAAI,IAAI;EAC9J,MAAME,iBAAiB,GAAGF,iCAAiC,GAAGP,oBAAoB,CAACU,mBAAmB,GAAGR,oBAAoB,CAACS,mBAAmB;EACjJ,OAAOF,iBAAiB,CAACL,UAAU,CAAC;AACtC,CAAC;AACDP,OAAO,CAACE,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}