{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldV6TextField = exports.addPositionPropertiesToSections = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _useEnhancedEffect = _interopRequireDefault(require(\"@mui/utils/useEnhancedEffect\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useTimeout = _interopRequireDefault(require(\"@mui/utils/useTimeout\"));\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _hooks = require(\"../../../hooks\");\nvar _utils = require(\"../../utils/utils\");\nvar _useField = require(\"./useField.utils\");\nvar _useFieldCharacterEditing = require(\"./useFieldCharacterEditing\");\nvar _useFieldRootHandleKeyDown = require(\"./useFieldRootHandleKeyDown\");\nvar _useFieldState = require(\"./useFieldState\");\nvar _useFieldInternalPropsWithDefaults = require(\"./useFieldInternalPropsWithDefaults\");\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nconst addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = (0, _useField.getSectionVisibleValue)(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push((0, _extends2.default)({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexports.addPositionPropertiesToSections = addPositionPropertiesToSections;\nconst useFieldV6TextField = parameters => {\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const focusTimeout = (0, _useTimeout.default)();\n  const selectionSyncTimeout = (0, _useTimeout.default)();\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = (0, _hooks.useSplitFieldProps)(props, valueType);\n  const internalPropsWithDefaults = (0, _useFieldInternalPropsWithDefaults.useFieldInternalPropsWithDefaults)({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    onFocus,\n    onClick,\n    onPaste,\n    onBlur,\n    onKeyDown,\n    onClear,\n    clearable,\n    inputRef: inputRefProp,\n    placeholder: inPlaceholder\n  } = forwardedProps;\n  const {\n    readOnly = false,\n    disabled = false,\n    autoFocus = false,\n    focused,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const inputRef = React.useRef(null);\n  const handleRef = (0, _useForkRef.default)(inputRefProp, inputRef);\n  const stateResponse = (0, _useFieldState.useFieldState)({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  } = stateResponse;\n  const applyCharacterEditing = (0, _useFieldCharacterEditing.useFieldCharacterEditing)({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  function syncSelectionFromDOM() {\n    const browserStartIndex = inputRef.current.selectionStart ?? 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  }\n  function focusField(newSelectedSection = 0) {\n    if ((0, _utils.getActiveElement)(document) === inputRef.current) {\n      return;\n    }\n    inputRef.current?.focus();\n    setSelectedSections(newSelectedSection);\n  }\n  const handleInputFocus = (0, _useEventCallback.default)(event => {\n    onFocus?.(event);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    focusTimeout.start(0, () => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = (0, _useEventCallback.default)((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = (0, _useEventCallback.default)(event => {\n    onPaste?.(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        setCharacterQuery(null);\n        updateSectionValue({\n          section: activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = (0, _useEventCallback.default)(event => {\n    onBlur?.(event);\n    setSelectedSections(null);\n  });\n  const handleInputChange = (0, _useEventCallback.default)(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n    if (parsedSelectedSections === 'all') {\n      setSelectedSections(activeSectionIndex);\n    }\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if ((0, _useField.isAndroid)()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const handleClear = (0, _useEventCallback.default)((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!isFieldFocused(inputRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerKeyDown = (0, _useFieldRootHandleKeyDown.useFieldRootHandleKeyDown)({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const wrappedHandleContainerKeyDown = (0, _useEventCallback.default)(event => {\n    onKeyDown?.(event);\n    handleContainerKeyDown(event);\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => state.tempValueStrAndroid ?? fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl), [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === (0, _utils.getActiveElement)(document)) {\n      setSelectedSections('all');\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  (0, _useEnhancedEffect.default)(() => {\n    function syncSelectionToDOM() {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== (0, _utils.getActiveElement)(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === (0, _utils.getActiveElement)(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        selectionSyncTimeout.start(0, () => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === (0, _utils.getActiveElement)(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n    syncSelectionToDOM();\n  });\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === (0, _utils.getActiveElement)(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      const browserStartIndex = inputRef.current.selectionStart ?? 0;\n      const browserEndIndex = inputRef.current.selectionEnd ?? 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    focusField,\n    isFieldFocused: () => isFieldFocused(inputRef)\n  }));\n  return (0, _extends2.default)({}, forwardedProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    onBlur: handleContainerBlur,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onPaste: handleInputPaste,\n    onKeyDown: wrappedHandleContainerKeyDown,\n    onClear: handleClear,\n    inputRef: handleRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: false,\n    placeholder,\n    inputMode,\n    autoComplete: 'off',\n    value: shouldShowPlaceholder ? '' : valueStr,\n    onChange: handleInputChange,\n    focused,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nexports.useFieldV6TextField = useFieldV6TextField;\nfunction isFieldFocused(inputRef) {\n  return inputRef.current === (0, _utils.getActiveElement)(document);\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "useFieldV6TextField", "addPositionPropertiesToSections", "_extends2", "React", "_RtlProvider", "_useEnhancedEffect", "_useEventCallback", "_useTimeout", "_useForkRef", "_hooks", "_utils", "_useField", "_useFieldCharacterEditing", "_useFieldRootHandleKeyDown", "_useFieldState", "_useFieldInternalPropsWithDefaults", "cleanString", "dirtyString", "replace", "sections", "localizedDigits", "isRtl", "position", "positionInInput", "newSections", "i", "length", "section", "renderedValue", "getSectionVisibleValue", "sectionStr", "startSeparator", "endSeparator", "sectionLength", "sectionLengthInInput", "cleanedValue", "startInInput", "indexOf", "endInInput", "push", "start", "end", "parameters", "useRtl", "focusTimeout", "selectionSyncTimeout", "props", "manager", "skipContextFieldRefAssignment", "valueType", "internal_valueManager", "valueManager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "internal_useOpenPickerButtonAriaLabel", "useOpenPickerButtonAriaLabel", "internalProps", "forwardedProps", "useSplitFieldProps", "internalPropsWithDefaults", "useFieldInternalPropsWithDefaults", "onFocus", "onClick", "onPaste", "onBlur", "onKeyDown", "onClear", "clearable", "inputRef", "inputRefProp", "placeholder", "inPlaceholder", "readOnly", "disabled", "autoFocus", "focused", "unstableFieldRef", "useRef", "handleRef", "stateResponse", "useFieldState", "activeSectionIndex", "areAllSectionsEmpty", "error", "parsedSelectedSections", "sectionOrder", "state", "clearValue", "clearActiveSection", "setCharacterQuery", "setSelectedSections", "setTempAndroidValueStr", "updateSectionValue", "updateValueFromValueStr", "getSectionsFromValue", "applyCharacterEditing", "useFieldCharacterEditing", "openPickerAriaLabel", "useMemo", "syncSelectionFromDOM", "browserStartIndex", "current", "selectionStart", "nextSectionIndex", "findIndex", "sectionIndex", "focusField", "newSelectedSection", "getActiveElement", "document", "focus", "handleInputFocus", "event", "input", "Number", "selectionEnd", "handleInputClick", "args", "isDefaultPrevented", "handleInputPaste", "preventDefault", "pastedValue", "clipboardData", "getData", "activeSection", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleContainerBlur", "handleInputChange", "targetValue", "target", "eventData", "nativeEvent", "data", "shouldUseEventData", "valueStr", "cleanValueStr", "keyPressed", "prevValueStr", "getV6InputValueFromSections", "startOfDiffIndex", "endOfDiffIndex", "hasDiffOutsideOfActiveSection", "activeSectionEndRelativeToNewValue", "slice", "isAndroid", "handleClear", "isFieldFocused", "startIndex", "handleContainerKeyDown", "useFieldRootHandleKeyDown", "wrappedHandleContainerKeyDown", "undefined", "emptyValue", "tempValueStrAndroid", "useEffect", "syncSelectionToDOM", "scrollLeft", "currentScrollTop", "scrollTop", "select", "selectedSection", "type", "setSelectionRange", "inputMode", "inputHasFocus", "shouldShowPlaceholder", "useImperativeHandle", "getSections", "getActiveSectionIndex", "browserEndIndex", "newSelectedSections", "Boolean", "enableAccessibleFieldDOMStructure", "autoComplete", "onChange"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldV6TextField.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldV6TextField = exports.addPositionPropertiesToSections = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _useEnhancedEffect = _interopRequireDefault(require(\"@mui/utils/useEnhancedEffect\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useTimeout = _interopRequireDefault(require(\"@mui/utils/useTimeout\"));\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _hooks = require(\"../../../hooks\");\nvar _utils = require(\"../../utils/utils\");\nvar _useField = require(\"./useField.utils\");\nvar _useFieldCharacterEditing = require(\"./useFieldCharacterEditing\");\nvar _useFieldRootHandleKeyDown = require(\"./useFieldRootHandleKeyDown\");\nvar _useFieldState = require(\"./useFieldState\");\nvar _useFieldInternalPropsWithDefaults = require(\"./useFieldInternalPropsWithDefaults\");\nconst cleanString = dirtyString => dirtyString.replace(/[\\u2066\\u2067\\u2068\\u2069]/g, '');\nconst addPositionPropertiesToSections = (sections, localizedDigits, isRtl) => {\n  let position = 0;\n  let positionInInput = isRtl ? 1 : 0;\n  const newSections = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const renderedValue = (0, _useField.getSectionVisibleValue)(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    const sectionStr = `${section.startSeparator}${renderedValue}${section.endSeparator}`;\n    const sectionLength = cleanString(sectionStr).length;\n    const sectionLengthInInput = sectionStr.length;\n\n    // The ...InInput values consider the unicode characters but do include them in their indexes\n    const cleanedValue = cleanString(renderedValue);\n    const startInInput = positionInInput + (cleanedValue === '' ? 0 : renderedValue.indexOf(cleanedValue[0])) + section.startSeparator.length;\n    const endInInput = startInInput + cleanedValue.length;\n    newSections.push((0, _extends2.default)({}, section, {\n      start: position,\n      end: position + sectionLength,\n      startInInput,\n      endInInput\n    }));\n    position += sectionLength;\n    // Move position to the end of string associated to the current section\n    positionInInput += sectionLengthInInput;\n  }\n  return newSections;\n};\nexports.addPositionPropertiesToSections = addPositionPropertiesToSections;\nconst useFieldV6TextField = parameters => {\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const focusTimeout = (0, _useTimeout.default)();\n  const selectionSyncTimeout = (0, _useTimeout.default)();\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_valueManager: valueManager,\n      internal_fieldValueManager: fieldValueManager,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = (0, _hooks.useSplitFieldProps)(props, valueType);\n  const internalPropsWithDefaults = (0, _useFieldInternalPropsWithDefaults.useFieldInternalPropsWithDefaults)({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    onFocus,\n    onClick,\n    onPaste,\n    onBlur,\n    onKeyDown,\n    onClear,\n    clearable,\n    inputRef: inputRefProp,\n    placeholder: inPlaceholder\n  } = forwardedProps;\n  const {\n    readOnly = false,\n    disabled = false,\n    autoFocus = false,\n    focused,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const inputRef = React.useRef(null);\n  const handleRef = (0, _useForkRef.default)(inputRefProp, inputRef);\n  const stateResponse = (0, _useFieldState.useFieldState)({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    activeSectionIndex,\n    areAllSectionsEmpty,\n    error,\n    localizedDigits,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    clearActiveSection,\n    setCharacterQuery,\n    setSelectedSections,\n    setTempAndroidValueStr,\n    updateSectionValue,\n    updateValueFromValueStr,\n    // Utilities methods\n    getSectionsFromValue\n  } = stateResponse;\n  const applyCharacterEditing = (0, _useFieldCharacterEditing.useFieldCharacterEditing)({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const sections = React.useMemo(() => addPositionPropertiesToSections(state.sections, localizedDigits, isRtl), [state.sections, localizedDigits, isRtl]);\n  function syncSelectionFromDOM() {\n    const browserStartIndex = inputRef.current.selectionStart ?? 0;\n    let nextSectionIndex;\n    if (browserStartIndex <= sections[0].startInInput) {\n      // Special case if browser index is in invisible characters at the beginning\n      nextSectionIndex = 1;\n    } else if (browserStartIndex >= sections[sections.length - 1].endInInput) {\n      // If the click is after the last character of the input, then we want to select the 1st section.\n      nextSectionIndex = 1;\n    } else {\n      nextSectionIndex = sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n    }\n    const sectionIndex = nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    setSelectedSections(sectionIndex);\n  }\n  function focusField(newSelectedSection = 0) {\n    if ((0, _utils.getActiveElement)(document) === inputRef.current) {\n      return;\n    }\n    inputRef.current?.focus();\n    setSelectedSections(newSelectedSection);\n  }\n  const handleInputFocus = (0, _useEventCallback.default)(event => {\n    onFocus?.(event);\n    // The ref is guaranteed to be resolved at this point.\n    const input = inputRef.current;\n    focusTimeout.start(0, () => {\n      // The ref changed, the component got remounted, the focus event is no longer relevant.\n      if (!input || input !== inputRef.current) {\n        return;\n      }\n      if (activeSectionIndex != null) {\n        return;\n      }\n      if (\n      // avoid selecting all sections when focusing empty field without value\n      input.value.length && Number(input.selectionEnd) - Number(input.selectionStart) === input.value.length) {\n        setSelectedSections('all');\n      } else {\n        syncSelectionFromDOM();\n      }\n    });\n  });\n  const handleInputClick = (0, _useEventCallback.default)((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event, ...args);\n    syncSelectionFromDOM();\n  });\n  const handleInputPaste = (0, _useEventCallback.default)(event => {\n    onPaste?.(event);\n\n    // prevent default to avoid the input `onChange` handler being called\n    event.preventDefault();\n    if (readOnly || disabled) {\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    if (typeof parsedSelectedSections === 'number') {\n      const activeSection = state.sections[parsedSelectedSections];\n      const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n      const digitsOnly = /^[0-9]+$/.test(pastedValue);\n      const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n      const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n      if (isValidPastedValue) {\n        setCharacterQuery(null);\n        updateSectionValue({\n          section: activeSection,\n          newSectionValue: pastedValue,\n          shouldGoToNextSection: true\n        });\n        return;\n      }\n      if (lettersOnly || digitsOnly) {\n        // The pasted value corresponds to a single section, but not the expected type,\n        // skip the modification\n        return;\n      }\n    }\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerBlur = (0, _useEventCallback.default)(event => {\n    onBlur?.(event);\n    setSelectedSections(null);\n  });\n  const handleInputChange = (0, _useEventCallback.default)(event => {\n    if (readOnly) {\n      return;\n    }\n    const targetValue = event.target.value;\n    if (targetValue === '') {\n      clearValue();\n      return;\n    }\n    const eventData = event.nativeEvent.data;\n    // Calling `.fill(04/11/2022)` in playwright will trigger a change event with the requested content to insert in `event.nativeEvent.data`\n    // usual changes have only the currently typed character in the `event.nativeEvent.data`\n    const shouldUseEventData = eventData && eventData.length > 1;\n    const valueStr = shouldUseEventData ? eventData : targetValue;\n    const cleanValueStr = cleanString(valueStr);\n    if (parsedSelectedSections === 'all') {\n      setSelectedSections(activeSectionIndex);\n    }\n\n    // If no section is selected or eventData should be used, we just try to parse the new value\n    // This line is mostly triggered by imperative code / application tests.\n    if (activeSectionIndex == null || shouldUseEventData) {\n      updateValueFromValueStr(shouldUseEventData ? eventData : cleanValueStr);\n      return;\n    }\n    let keyPressed;\n    if (parsedSelectedSections === 'all' && cleanValueStr.length === 1) {\n      keyPressed = cleanValueStr;\n    } else {\n      const prevValueStr = cleanString(fieldValueManager.getV6InputValueFromSections(sections, localizedDigits, isRtl));\n      let startOfDiffIndex = -1;\n      let endOfDiffIndex = -1;\n      for (let i = 0; i < prevValueStr.length; i += 1) {\n        if (startOfDiffIndex === -1 && prevValueStr[i] !== cleanValueStr[i]) {\n          startOfDiffIndex = i;\n        }\n        if (endOfDiffIndex === -1 && prevValueStr[prevValueStr.length - i - 1] !== cleanValueStr[cleanValueStr.length - i - 1]) {\n          endOfDiffIndex = i;\n        }\n      }\n      const activeSection = sections[activeSectionIndex];\n      const hasDiffOutsideOfActiveSection = startOfDiffIndex < activeSection.start || prevValueStr.length - endOfDiffIndex - 1 > activeSection.end;\n      if (hasDiffOutsideOfActiveSection) {\n        // TODO: Support if the new date is valid\n        return;\n      }\n\n      // The active section being selected, the browser has replaced its value with the key pressed by the user.\n      const activeSectionEndRelativeToNewValue = cleanValueStr.length - prevValueStr.length + activeSection.end - cleanString(activeSection.endSeparator || '').length;\n      keyPressed = cleanValueStr.slice(activeSection.start + cleanString(activeSection.startSeparator || '').length, activeSectionEndRelativeToNewValue);\n    }\n    if (keyPressed.length === 0) {\n      if ((0, _useField.isAndroid)()) {\n        setTempAndroidValueStr(valueStr);\n      }\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex: activeSectionIndex\n    });\n  });\n  const handleClear = (0, _useEventCallback.default)((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!isFieldFocused(inputRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerKeyDown = (0, _useFieldRootHandleKeyDown.useFieldRootHandleKeyDown)({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const wrappedHandleContainerKeyDown = (0, _useEventCallback.default)(event => {\n    onKeyDown?.(event);\n    handleContainerKeyDown(event);\n  });\n  const placeholder = React.useMemo(() => {\n    if (inPlaceholder !== undefined) {\n      return inPlaceholder;\n    }\n    return fieldValueManager.getV6InputValueFromSections(getSectionsFromValue(valueManager.emptyValue), localizedDigits, isRtl);\n  }, [inPlaceholder, fieldValueManager, getSectionsFromValue, valueManager.emptyValue, localizedDigits, isRtl]);\n  const valueStr = React.useMemo(() => state.tempValueStrAndroid ?? fieldValueManager.getV6InputValueFromSections(state.sections, localizedDigits, isRtl), [state.sections, fieldValueManager, state.tempValueStrAndroid, localizedDigits, isRtl]);\n  React.useEffect(() => {\n    // Select all the sections when focused on mount (`autoFocus = true` on the input)\n    if (inputRef.current && inputRef.current === (0, _utils.getActiveElement)(document)) {\n      setSelectedSections('all');\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  (0, _useEnhancedEffect.default)(() => {\n    function syncSelectionToDOM() {\n      if (!inputRef.current) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        if (inputRef.current.scrollLeft) {\n          // Ensure that input content is not marked as selected.\n          // setting selection range to 0 causes issues in Safari.\n          // https://bugs.webkit.org/show_bug.cgi?id=224425\n          inputRef.current.scrollLeft = 0;\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      // This helps to avoid the focus jumping on Safari https://github.com/mui/mui-x/issues/9003\n      // because WebKit implements the `setSelectionRange` based on the spec: https://bugs.webkit.org/show_bug.cgi?id=224425\n      if (inputRef.current !== (0, _utils.getActiveElement)(document)) {\n        return;\n      }\n\n      // Fix scroll jumping on iOS browser: https://github.com/mui/mui-x/issues/8321\n      const currentScrollTop = inputRef.current.scrollTop;\n      if (parsedSelectedSections === 'all') {\n        inputRef.current.select();\n      } else {\n        const selectedSection = sections[parsedSelectedSections];\n        const selectionStart = selectedSection.type === 'empty' ? selectedSection.startInInput - selectedSection.startSeparator.length : selectedSection.startInInput;\n        const selectionEnd = selectedSection.type === 'empty' ? selectedSection.endInInput + selectedSection.endSeparator.length : selectedSection.endInInput;\n        if (selectionStart !== inputRef.current.selectionStart || selectionEnd !== inputRef.current.selectionEnd) {\n          if (inputRef.current === (0, _utils.getActiveElement)(document)) {\n            inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n          }\n        }\n        selectionSyncTimeout.start(0, () => {\n          // handle case when the selection is not updated correctly\n          // could happen on Android\n          if (inputRef.current && inputRef.current === (0, _utils.getActiveElement)(document) &&\n          // The section might loose all selection, where `selectionStart === selectionEnd`\n          // https://github.com/mui/mui-x/pull/13652\n          inputRef.current.selectionStart === inputRef.current.selectionEnd && (inputRef.current.selectionStart !== selectionStart || inputRef.current.selectionEnd !== selectionEnd)) {\n            syncSelectionToDOM();\n          }\n        });\n      }\n\n      // Even reading this variable seems to do the trick, but also setting it just to make use of it\n      inputRef.current.scrollTop = currentScrollTop;\n    }\n    syncSelectionToDOM();\n  });\n  const inputMode = React.useMemo(() => {\n    if (activeSectionIndex == null) {\n      return 'text';\n    }\n    if (state.sections[activeSectionIndex].contentType === 'letter') {\n      return 'text';\n    }\n    return 'numeric';\n  }, [activeSectionIndex, state.sections]);\n  const inputHasFocus = inputRef.current && inputRef.current === (0, _utils.getActiveElement)(document);\n  const shouldShowPlaceholder = !inputHasFocus && areAllSectionsEmpty;\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => {\n      const browserStartIndex = inputRef.current.selectionStart ?? 0;\n      const browserEndIndex = inputRef.current.selectionEnd ?? 0;\n      if (browserStartIndex === 0 && browserEndIndex === 0) {\n        return null;\n      }\n      const nextSectionIndex = browserStartIndex <= sections[0].startInInput ? 1 // Special case if browser index is in invisible characters at the beginning.\n      : sections.findIndex(section => section.startInInput - section.startSeparator.length > browserStartIndex);\n      return nextSectionIndex === -1 ? sections.length - 1 : nextSectionIndex - 1;\n    },\n    setSelectedSections: newSelectedSections => setSelectedSections(newSelectedSections),\n    focusField,\n    isFieldFocused: () => isFieldFocused(inputRef)\n  }));\n  return (0, _extends2.default)({}, forwardedProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    onBlur: handleContainerBlur,\n    onClick: handleInputClick,\n    onFocus: handleInputFocus,\n    onPaste: handleInputPaste,\n    onKeyDown: wrappedHandleContainerKeyDown,\n    onClear: handleClear,\n    inputRef: handleRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: false,\n    placeholder,\n    inputMode,\n    autoComplete: 'off',\n    value: shouldShowPlaceholder ? '' : valueStr,\n    onChange: handleInputChange,\n    focused,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nexports.useFieldV6TextField = useFieldV6TextField;\nfunction isFieldFocused(inputRef) {\n  return inputRef.current === (0, _utils.getActiveElement)(document);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,mBAAmB,GAAGF,OAAO,CAACG,+BAA+B,GAAG,KAAK,CAAC;AAC9E,IAAIC,SAAS,GAAGP,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIU,KAAK,GAAGX,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIW,YAAY,GAAGX,OAAO,CAAC,yBAAyB,CAAC;AACrD,IAAIY,kBAAkB,GAAGV,sBAAsB,CAACF,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACxF,IAAIa,iBAAiB,GAAGX,sBAAsB,CAACF,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIc,WAAW,GAAGZ,sBAAsB,CAACF,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIe,WAAW,GAAGb,sBAAsB,CAACF,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIgB,MAAM,GAAGhB,OAAO,CAAC,gBAAgB,CAAC;AACtC,IAAIiB,MAAM,GAAGjB,OAAO,CAAC,mBAAmB,CAAC;AACzC,IAAIkB,SAAS,GAAGlB,OAAO,CAAC,kBAAkB,CAAC;AAC3C,IAAImB,yBAAyB,GAAGnB,OAAO,CAAC,4BAA4B,CAAC;AACrE,IAAIoB,0BAA0B,GAAGpB,OAAO,CAAC,6BAA6B,CAAC;AACvE,IAAIqB,cAAc,GAAGrB,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIsB,kCAAkC,GAAGtB,OAAO,CAAC,qCAAqC,CAAC;AACvF,MAAMuB,WAAW,GAAGC,WAAW,IAAIA,WAAW,CAACC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;AACzF,MAAMjB,+BAA+B,GAAGA,CAACkB,QAAQ,EAAEC,eAAe,EAAEC,KAAK,KAAK;EAC5E,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIC,eAAe,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC;EACnC,MAAMG,WAAW,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAME,OAAO,GAAGR,QAAQ,CAACM,CAAC,CAAC;IAC3B,MAAMG,aAAa,GAAG,CAAC,CAAC,EAAEjB,SAAS,CAACkB,sBAAsB,EAAEF,OAAO,EAAEN,KAAK,GAAG,WAAW,GAAG,WAAW,EAAED,eAAe,CAAC;IACxH,MAAMU,UAAU,GAAG,GAAGH,OAAO,CAACI,cAAc,GAAGH,aAAa,GAAGD,OAAO,CAACK,YAAY,EAAE;IACrF,MAAMC,aAAa,GAAGjB,WAAW,CAACc,UAAU,CAAC,CAACJ,MAAM;IACpD,MAAMQ,oBAAoB,GAAGJ,UAAU,CAACJ,MAAM;;IAE9C;IACA,MAAMS,YAAY,GAAGnB,WAAW,CAACY,aAAa,CAAC;IAC/C,MAAMQ,YAAY,GAAGb,eAAe,IAAIY,YAAY,KAAK,EAAE,GAAG,CAAC,GAAGP,aAAa,CAACS,OAAO,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGR,OAAO,CAACI,cAAc,CAACL,MAAM;IACzI,MAAMY,UAAU,GAAGF,YAAY,GAAGD,YAAY,CAACT,MAAM;IACrDF,WAAW,CAACe,IAAI,CAAC,CAAC,CAAC,EAAErC,SAAS,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEiC,OAAO,EAAE;MACnDa,KAAK,EAAElB,QAAQ;MACfmB,GAAG,EAAEnB,QAAQ,GAAGW,aAAa;MAC7BG,YAAY;MACZE;IACF,CAAC,CAAC,CAAC;IACHhB,QAAQ,IAAIW,aAAa;IACzB;IACAV,eAAe,IAAIW,oBAAoB;EACzC;EACA,OAAOV,WAAW;AACpB,CAAC;AACD1B,OAAO,CAACG,+BAA+B,GAAGA,+BAA+B;AACzE,MAAMD,mBAAmB,GAAG0C,UAAU,IAAI;EACxC,MAAMrB,KAAK,GAAG,CAAC,CAAC,EAAEjB,YAAY,CAACuC,MAAM,EAAE,CAAC;EACxC,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAErC,WAAW,CAACb,OAAO,EAAE,CAAC;EAC/C,MAAMmD,oBAAoB,GAAG,CAAC,CAAC,EAAEtC,WAAW,CAACb,OAAO,EAAE,CAAC;EACvD,MAAM;IACJoD,KAAK;IACLC,OAAO;IACPC,6BAA6B;IAC7BD,OAAO,EAAE;MACPE,SAAS;MACTC,qBAAqB,EAAEC,YAAY;MACnCC,0BAA0B,EAAEC,iBAAiB;MAC7CC,qCAAqC,EAAEC;IACzC;EACF,CAAC,GAAGb,UAAU;EACd,MAAM;IACJc,aAAa;IACbC;EACF,CAAC,GAAG,CAAC,CAAC,EAAEhD,MAAM,CAACiD,kBAAkB,EAAEZ,KAAK,EAAEG,SAAS,CAAC;EACpD,MAAMU,yBAAyB,GAAG,CAAC,CAAC,EAAE5C,kCAAkC,CAAC6C,iCAAiC,EAAE;IAC1Gb,OAAO;IACPS,aAAa;IACbR;EACF,CAAC,CAAC;EACF,MAAM;IACJa,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC,SAAS;IACTC,QAAQ,EAAEC,YAAY;IACtBC,WAAW,EAAEC;EACf,CAAC,GAAGd,cAAc;EAClB,MAAM;IACJe,QAAQ,GAAG,KAAK;IAChBC,QAAQ,GAAG,KAAK;IAChBC,SAAS,GAAG,KAAK;IACjBC,OAAO;IACPC;EACF,CAAC,GAAGjB,yBAAyB;EAC7B,MAAMS,QAAQ,GAAGjE,KAAK,CAAC0E,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAEtE,WAAW,CAACd,OAAO,EAAE2E,YAAY,EAAED,QAAQ,CAAC;EAClE,MAAMW,aAAa,GAAG,CAAC,CAAC,EAAEjE,cAAc,CAACkE,aAAa,EAAE;IACtDjC,OAAO;IACPY,yBAAyB;IACzBF;EACF,CAAC,CAAC;EACF,MAAM;IACJ;IACAwB,kBAAkB;IAClBC,mBAAmB;IACnBC,KAAK;IACL/D,eAAe;IACfgE,sBAAsB;IACtBC,YAAY;IACZC,KAAK;IACLvF,KAAK;IACL;IACAwF,UAAU;IACVC,kBAAkB;IAClBC,iBAAiB;IACjBC,mBAAmB;IACnBC,sBAAsB;IACtBC,kBAAkB;IAClBC,uBAAuB;IACvB;IACAC;EACF,CAAC,GAAGf,aAAa;EACjB,MAAMgB,qBAAqB,GAAG,CAAC,CAAC,EAAEnF,yBAAyB,CAACoF,wBAAwB,EAAE;IACpFjB;EACF,CAAC,CAAC;EACF,MAAMkB,mBAAmB,GAAG1C,4BAA4B,CAACxD,KAAK,CAAC;EAC/D,MAAMoB,QAAQ,GAAGhB,KAAK,CAAC+F,OAAO,CAAC,MAAMjG,+BAA+B,CAACqF,KAAK,CAACnE,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,EAAE,CAACiE,KAAK,CAACnE,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;EACvJ,SAAS8E,oBAAoBA,CAAA,EAAG;IAC9B,MAAMC,iBAAiB,GAAGhC,QAAQ,CAACiC,OAAO,CAACC,cAAc,IAAI,CAAC;IAC9D,IAAIC,gBAAgB;IACpB,IAAIH,iBAAiB,IAAIjF,QAAQ,CAAC,CAAC,CAAC,CAACiB,YAAY,EAAE;MACjD;MACAmE,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM,IAAIH,iBAAiB,IAAIjF,QAAQ,CAACA,QAAQ,CAACO,MAAM,GAAG,CAAC,CAAC,CAACY,UAAU,EAAE;MACxE;MACAiE,gBAAgB,GAAG,CAAC;IACtB,CAAC,MAAM;MACLA,gBAAgB,GAAGpF,QAAQ,CAACqF,SAAS,CAAC7E,OAAO,IAAIA,OAAO,CAACS,YAAY,GAAGT,OAAO,CAACI,cAAc,CAACL,MAAM,GAAG0E,iBAAiB,CAAC;IAC5H;IACA,MAAMK,YAAY,GAAGF,gBAAgB,KAAK,CAAC,CAAC,GAAGpF,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAG6E,gBAAgB,GAAG,CAAC;IACzFb,mBAAmB,CAACe,YAAY,CAAC;EACnC;EACA,SAASC,UAAUA,CAACC,kBAAkB,GAAG,CAAC,EAAE;IAC1C,IAAI,CAAC,CAAC,EAAEjG,MAAM,CAACkG,gBAAgB,EAAEC,QAAQ,CAAC,KAAKzC,QAAQ,CAACiC,OAAO,EAAE;MAC/D;IACF;IACAjC,QAAQ,CAACiC,OAAO,EAAES,KAAK,CAAC,CAAC;IACzBpB,mBAAmB,CAACiB,kBAAkB,CAAC;EACzC;EACA,MAAMI,gBAAgB,GAAG,CAAC,CAAC,EAAEzG,iBAAiB,CAACZ,OAAO,EAAEsH,KAAK,IAAI;IAC/DnD,OAAO,GAAGmD,KAAK,CAAC;IAChB;IACA,MAAMC,KAAK,GAAG7C,QAAQ,CAACiC,OAAO;IAC9BzD,YAAY,CAACJ,KAAK,CAAC,CAAC,EAAE,MAAM;MAC1B;MACA,IAAI,CAACyE,KAAK,IAAIA,KAAK,KAAK7C,QAAQ,CAACiC,OAAO,EAAE;QACxC;MACF;MACA,IAAIpB,kBAAkB,IAAI,IAAI,EAAE;QAC9B;MACF;MACA;MACA;MACAgC,KAAK,CAAClH,KAAK,CAAC2B,MAAM,IAAIwF,MAAM,CAACD,KAAK,CAACE,YAAY,CAAC,GAAGD,MAAM,CAACD,KAAK,CAACX,cAAc,CAAC,KAAKW,KAAK,CAAClH,KAAK,CAAC2B,MAAM,EAAE;QACtGgE,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLS,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMiB,gBAAgB,GAAG,CAAC,CAAC,EAAE9G,iBAAiB,CAACZ,OAAO,EAAE,CAACsH,KAAK,EAAE,GAAGK,IAAI,KAAK;IAC1E;IACA;IACA,IAAIL,KAAK,CAACM,kBAAkB,CAAC,CAAC,EAAE;MAC9B;IACF;IACAxD,OAAO,GAAGkD,KAAK,EAAE,GAAGK,IAAI,CAAC;IACzBlB,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMoB,gBAAgB,GAAG,CAAC,CAAC,EAAEjH,iBAAiB,CAACZ,OAAO,EAAEsH,KAAK,IAAI;IAC/DjD,OAAO,GAAGiD,KAAK,CAAC;;IAEhB;IACAA,KAAK,CAACQ,cAAc,CAAC,CAAC;IACtB,IAAIhD,QAAQ,IAAIC,QAAQ,EAAE;MACxB;IACF;IACA,MAAMgD,WAAW,GAAGT,KAAK,CAACU,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,IAAI,OAAOvC,sBAAsB,KAAK,QAAQ,EAAE;MAC9C,MAAMwC,aAAa,GAAGtC,KAAK,CAACnE,QAAQ,CAACiE,sBAAsB,CAAC;MAC5D,MAAMyC,WAAW,GAAG,aAAa,CAACC,IAAI,CAACL,WAAW,CAAC;MACnD,MAAMM,UAAU,GAAG,UAAU,CAACD,IAAI,CAACL,WAAW,CAAC;MAC/C,MAAMO,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACL,WAAW,CAAC;MACtF,MAAMQ,kBAAkB,GAAGL,aAAa,CAACM,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAID,aAAa,CAACM,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIH,aAAa,CAACM,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;MACnN,IAAIC,kBAAkB,EAAE;QACtBxC,iBAAiB,CAAC,IAAI,CAAC;QACvBG,kBAAkB,CAAC;UACjBjE,OAAO,EAAEiG,aAAa;UACtBO,eAAe,EAAEV,WAAW;UAC5BW,qBAAqB,EAAE;QACzB,CAAC,CAAC;QACF;MACF;MACA,IAAIP,WAAW,IAAIE,UAAU,EAAE;QAC7B;QACA;QACA;MACF;IACF;IACAtC,iBAAiB,CAAC,IAAI,CAAC;IACvBI,uBAAuB,CAAC4B,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMY,mBAAmB,GAAG,CAAC,CAAC,EAAE/H,iBAAiB,CAACZ,OAAO,EAAEsH,KAAK,IAAI;IAClEhD,MAAM,GAAGgD,KAAK,CAAC;IACftB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC,CAAC;EACF,MAAM4C,iBAAiB,GAAG,CAAC,CAAC,EAAEhI,iBAAiB,CAACZ,OAAO,EAAEsH,KAAK,IAAI;IAChE,IAAIxC,QAAQ,EAAE;MACZ;IACF;IACA,MAAM+D,WAAW,GAAGvB,KAAK,CAACwB,MAAM,CAACzI,KAAK;IACtC,IAAIwI,WAAW,KAAK,EAAE,EAAE;MACtBhD,UAAU,CAAC,CAAC;MACZ;IACF;IACA,MAAMkD,SAAS,GAAGzB,KAAK,CAAC0B,WAAW,CAACC,IAAI;IACxC;IACA;IACA,MAAMC,kBAAkB,GAAGH,SAAS,IAAIA,SAAS,CAAC/G,MAAM,GAAG,CAAC;IAC5D,MAAMmH,QAAQ,GAAGD,kBAAkB,GAAGH,SAAS,GAAGF,WAAW;IAC7D,MAAMO,aAAa,GAAG9H,WAAW,CAAC6H,QAAQ,CAAC;IAC3C,IAAIzD,sBAAsB,KAAK,KAAK,EAAE;MACpCM,mBAAmB,CAACT,kBAAkB,CAAC;IACzC;;IAEA;IACA;IACA,IAAIA,kBAAkB,IAAI,IAAI,IAAI2D,kBAAkB,EAAE;MACpD/C,uBAAuB,CAAC+C,kBAAkB,GAAGH,SAAS,GAAGK,aAAa,CAAC;MACvE;IACF;IACA,IAAIC,UAAU;IACd,IAAI3D,sBAAsB,KAAK,KAAK,IAAI0D,aAAa,CAACpH,MAAM,KAAK,CAAC,EAAE;MAClEqH,UAAU,GAAGD,aAAa;IAC5B,CAAC,MAAM;MACL,MAAME,YAAY,GAAGhI,WAAW,CAACqC,iBAAiB,CAAC4F,2BAA2B,CAAC9H,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,CAAC;MACjH,IAAI6H,gBAAgB,GAAG,CAAC,CAAC;MACzB,IAAIC,cAAc,GAAG,CAAC,CAAC;MACvB,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuH,YAAY,CAACtH,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QAC/C,IAAIyH,gBAAgB,KAAK,CAAC,CAAC,IAAIF,YAAY,CAACvH,CAAC,CAAC,KAAKqH,aAAa,CAACrH,CAAC,CAAC,EAAE;UACnEyH,gBAAgB,GAAGzH,CAAC;QACtB;QACA,IAAI0H,cAAc,KAAK,CAAC,CAAC,IAAIH,YAAY,CAACA,YAAY,CAACtH,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,KAAKqH,aAAa,CAACA,aAAa,CAACpH,MAAM,GAAGD,CAAC,GAAG,CAAC,CAAC,EAAE;UACtH0H,cAAc,GAAG1H,CAAC;QACpB;MACF;MACA,MAAMmG,aAAa,GAAGzG,QAAQ,CAAC8D,kBAAkB,CAAC;MAClD,MAAMmE,6BAA6B,GAAGF,gBAAgB,GAAGtB,aAAa,CAACpF,KAAK,IAAIwG,YAAY,CAACtH,MAAM,GAAGyH,cAAc,GAAG,CAAC,GAAGvB,aAAa,CAACnF,GAAG;MAC5I,IAAI2G,6BAA6B,EAAE;QACjC;QACA;MACF;;MAEA;MACA,MAAMC,kCAAkC,GAAGP,aAAa,CAACpH,MAAM,GAAGsH,YAAY,CAACtH,MAAM,GAAGkG,aAAa,CAACnF,GAAG,GAAGzB,WAAW,CAAC4G,aAAa,CAAC5F,YAAY,IAAI,EAAE,CAAC,CAACN,MAAM;MAChKqH,UAAU,GAAGD,aAAa,CAACQ,KAAK,CAAC1B,aAAa,CAACpF,KAAK,GAAGxB,WAAW,CAAC4G,aAAa,CAAC7F,cAAc,IAAI,EAAE,CAAC,CAACL,MAAM,EAAE2H,kCAAkC,CAAC;IACpJ;IACA,IAAIN,UAAU,CAACrH,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI,CAAC,CAAC,EAAEf,SAAS,CAAC4I,SAAS,EAAE,CAAC,EAAE;QAC9B5D,sBAAsB,CAACkD,QAAQ,CAAC;MAClC;MACArD,kBAAkB,CAAC,CAAC;MACpB;IACF;IACAO,qBAAqB,CAAC;MACpBgD,UAAU;MACVtC,YAAY,EAAExB;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMuE,WAAW,GAAG,CAAC,CAAC,EAAElJ,iBAAiB,CAACZ,OAAO,EAAE,CAACsH,KAAK,EAAE,GAAGK,IAAI,KAAK;IACrEL,KAAK,CAACQ,cAAc,CAAC,CAAC;IACtBtD,OAAO,GAAG8C,KAAK,EAAE,GAAGK,IAAI,CAAC;IACzB9B,UAAU,CAAC,CAAC;IACZ,IAAI,CAACkE,cAAc,CAACrF,QAAQ,CAAC,EAAE;MAC7B;MACAsC,UAAU,CAAC,CAAC,CAAC;IACf,CAAC,MAAM;MACLhB,mBAAmB,CAACL,YAAY,CAACqE,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAMC,sBAAsB,GAAG,CAAC,CAAC,EAAE9I,0BAA0B,CAAC+I,yBAAyB,EAAE;IACvF7G,OAAO;IACPY,yBAAyB;IACzBoB;EACF,CAAC,CAAC;EACF,MAAM8E,6BAA6B,GAAG,CAAC,CAAC,EAAEvJ,iBAAiB,CAACZ,OAAO,EAAEsH,KAAK,IAAI;IAC5E/C,SAAS,GAAG+C,KAAK,CAAC;IAClB2C,sBAAsB,CAAC3C,KAAK,CAAC;EAC/B,CAAC,CAAC;EACF,MAAM1C,WAAW,GAAGnE,KAAK,CAAC+F,OAAO,CAAC,MAAM;IACtC,IAAI3B,aAAa,KAAKuF,SAAS,EAAE;MAC/B,OAAOvF,aAAa;IACtB;IACA,OAAOlB,iBAAiB,CAAC4F,2BAA2B,CAACnD,oBAAoB,CAAC3C,YAAY,CAAC4G,UAAU,CAAC,EAAE3I,eAAe,EAAEC,KAAK,CAAC;EAC7H,CAAC,EAAE,CAACkD,aAAa,EAAElB,iBAAiB,EAAEyC,oBAAoB,EAAE3C,YAAY,CAAC4G,UAAU,EAAE3I,eAAe,EAAEC,KAAK,CAAC,CAAC;EAC7G,MAAMwH,QAAQ,GAAG1I,KAAK,CAAC+F,OAAO,CAAC,MAAMZ,KAAK,CAAC0E,mBAAmB,IAAI3G,iBAAiB,CAAC4F,2BAA2B,CAAC3D,KAAK,CAACnE,QAAQ,EAAEC,eAAe,EAAEC,KAAK,CAAC,EAAE,CAACiE,KAAK,CAACnE,QAAQ,EAAEkC,iBAAiB,EAAEiC,KAAK,CAAC0E,mBAAmB,EAAE5I,eAAe,EAAEC,KAAK,CAAC,CAAC;EAChPlB,KAAK,CAAC8J,SAAS,CAAC,MAAM;IACpB;IACA,IAAI7F,QAAQ,CAACiC,OAAO,IAAIjC,QAAQ,CAACiC,OAAO,KAAK,CAAC,CAAC,EAAE3F,MAAM,CAACkG,gBAAgB,EAAEC,QAAQ,CAAC,EAAE;MACnFnB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,CAAC,CAAC,EAAErF,kBAAkB,CAACX,OAAO,EAAE,MAAM;IACpC,SAASwK,kBAAkBA,CAAA,EAAG;MAC5B,IAAI,CAAC9F,QAAQ,CAACiC,OAAO,EAAE;QACrB;MACF;MACA,IAAIjB,sBAAsB,IAAI,IAAI,EAAE;QAClC,IAAIhB,QAAQ,CAACiC,OAAO,CAAC8D,UAAU,EAAE;UAC/B;UACA;UACA;UACA/F,QAAQ,CAACiC,OAAO,CAAC8D,UAAU,GAAG,CAAC;QACjC;QACA;MACF;;MAEA;MACA;MACA;MACA,IAAI/F,QAAQ,CAACiC,OAAO,KAAK,CAAC,CAAC,EAAE3F,MAAM,CAACkG,gBAAgB,EAAEC,QAAQ,CAAC,EAAE;QAC/D;MACF;;MAEA;MACA,MAAMuD,gBAAgB,GAAGhG,QAAQ,CAACiC,OAAO,CAACgE,SAAS;MACnD,IAAIjF,sBAAsB,KAAK,KAAK,EAAE;QACpChB,QAAQ,CAACiC,OAAO,CAACiE,MAAM,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,MAAMC,eAAe,GAAGpJ,QAAQ,CAACiE,sBAAsB,CAAC;QACxD,MAAMkB,cAAc,GAAGiE,eAAe,CAACC,IAAI,KAAK,OAAO,GAAGD,eAAe,CAACnI,YAAY,GAAGmI,eAAe,CAACxI,cAAc,CAACL,MAAM,GAAG6I,eAAe,CAACnI,YAAY;QAC7J,MAAM+E,YAAY,GAAGoD,eAAe,CAACC,IAAI,KAAK,OAAO,GAAGD,eAAe,CAACjI,UAAU,GAAGiI,eAAe,CAACvI,YAAY,CAACN,MAAM,GAAG6I,eAAe,CAACjI,UAAU;QACrJ,IAAIgE,cAAc,KAAKlC,QAAQ,CAACiC,OAAO,CAACC,cAAc,IAAIa,YAAY,KAAK/C,QAAQ,CAACiC,OAAO,CAACc,YAAY,EAAE;UACxG,IAAI/C,QAAQ,CAACiC,OAAO,KAAK,CAAC,CAAC,EAAE3F,MAAM,CAACkG,gBAAgB,EAAEC,QAAQ,CAAC,EAAE;YAC/DzC,QAAQ,CAACiC,OAAO,CAACoE,iBAAiB,CAACnE,cAAc,EAAEa,YAAY,CAAC;UAClE;QACF;QACAtE,oBAAoB,CAACL,KAAK,CAAC,CAAC,EAAE,MAAM;UAClC;UACA;UACA,IAAI4B,QAAQ,CAACiC,OAAO,IAAIjC,QAAQ,CAACiC,OAAO,KAAK,CAAC,CAAC,EAAE3F,MAAM,CAACkG,gBAAgB,EAAEC,QAAQ,CAAC;UACnF;UACA;UACAzC,QAAQ,CAACiC,OAAO,CAACC,cAAc,KAAKlC,QAAQ,CAACiC,OAAO,CAACc,YAAY,KAAK/C,QAAQ,CAACiC,OAAO,CAACC,cAAc,KAAKA,cAAc,IAAIlC,QAAQ,CAACiC,OAAO,CAACc,YAAY,KAAKA,YAAY,CAAC,EAAE;YAC3K+C,kBAAkB,CAAC,CAAC;UACtB;QACF,CAAC,CAAC;MACJ;;MAEA;MACA9F,QAAQ,CAACiC,OAAO,CAACgE,SAAS,GAAGD,gBAAgB;IAC/C;IACAF,kBAAkB,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,MAAMQ,SAAS,GAAGvK,KAAK,CAAC+F,OAAO,CAAC,MAAM;IACpC,IAAIjB,kBAAkB,IAAI,IAAI,EAAE;MAC9B,OAAO,MAAM;IACf;IACA,IAAIK,KAAK,CAACnE,QAAQ,CAAC8D,kBAAkB,CAAC,CAACiD,WAAW,KAAK,QAAQ,EAAE;MAC/D,OAAO,MAAM;IACf;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAACjD,kBAAkB,EAAEK,KAAK,CAACnE,QAAQ,CAAC,CAAC;EACxC,MAAMwJ,aAAa,GAAGvG,QAAQ,CAACiC,OAAO,IAAIjC,QAAQ,CAACiC,OAAO,KAAK,CAAC,CAAC,EAAE3F,MAAM,CAACkG,gBAAgB,EAAEC,QAAQ,CAAC;EACrG,MAAM+D,qBAAqB,GAAG,CAACD,aAAa,IAAIzF,mBAAmB;EACnE/E,KAAK,CAAC0K,mBAAmB,CAACjG,gBAAgB,EAAE,OAAO;IACjDkG,WAAW,EAAEA,CAAA,KAAMxF,KAAK,CAACnE,QAAQ;IACjC4J,qBAAqB,EAAEA,CAAA,KAAM;MAC3B,MAAM3E,iBAAiB,GAAGhC,QAAQ,CAACiC,OAAO,CAACC,cAAc,IAAI,CAAC;MAC9D,MAAM0E,eAAe,GAAG5G,QAAQ,CAACiC,OAAO,CAACc,YAAY,IAAI,CAAC;MAC1D,IAAIf,iBAAiB,KAAK,CAAC,IAAI4E,eAAe,KAAK,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MACA,MAAMzE,gBAAgB,GAAGH,iBAAiB,IAAIjF,QAAQ,CAAC,CAAC,CAAC,CAACiB,YAAY,GAAG,CAAC,CAAC;MAAA,EACzEjB,QAAQ,CAACqF,SAAS,CAAC7E,OAAO,IAAIA,OAAO,CAACS,YAAY,GAAGT,OAAO,CAACI,cAAc,CAACL,MAAM,GAAG0E,iBAAiB,CAAC;MACzG,OAAOG,gBAAgB,KAAK,CAAC,CAAC,GAAGpF,QAAQ,CAACO,MAAM,GAAG,CAAC,GAAG6E,gBAAgB,GAAG,CAAC;IAC7E,CAAC;IACDb,mBAAmB,EAAEuF,mBAAmB,IAAIvF,mBAAmB,CAACuF,mBAAmB,CAAC;IACpFvE,UAAU;IACV+C,cAAc,EAAEA,CAAA,KAAMA,cAAc,CAACrF,QAAQ;EAC/C,CAAC,CAAC,CAAC;EACH,OAAO,CAAC,CAAC,EAAElE,SAAS,CAACR,OAAO,EAAE,CAAC,CAAC,EAAE+D,cAAc,EAAE;IAChD0B,KAAK;IACLhB,SAAS,EAAE+G,OAAO,CAAC/G,SAAS,IAAI,CAACe,mBAAmB,IAAI,CAACV,QAAQ,IAAI,CAACC,QAAQ,CAAC;IAC/ET,MAAM,EAAEqE,mBAAmB;IAC3BvE,OAAO,EAAEsD,gBAAgB;IACzBvD,OAAO,EAAEkD,gBAAgB;IACzBhD,OAAO,EAAEwD,gBAAgB;IACzBtD,SAAS,EAAE4F,6BAA6B;IACxC3F,OAAO,EAAEsF,WAAW;IACpBpF,QAAQ,EAAEU,SAAS;IACnB;IACAqG,iCAAiC,EAAE,KAAK;IACxC7G,WAAW;IACXoG,SAAS;IACTU,YAAY,EAAE,KAAK;IACnBrL,KAAK,EAAE6K,qBAAqB,GAAG,EAAE,GAAG/B,QAAQ;IAC5CwC,QAAQ,EAAE/C,iBAAiB;IAC3B3D,OAAO;IACPF,QAAQ;IACRD,QAAQ;IACRE,SAAS;IACTuB;EACF,CAAC,CAAC;AACJ,CAAC;AACDnG,OAAO,CAACE,mBAAmB,GAAGA,mBAAmB;AACjD,SAASyJ,cAAcA,CAACrF,QAAQ,EAAE;EAChC,OAAOA,QAAQ,CAACiC,OAAO,KAAK,CAAC,CAAC,EAAE3F,MAAM,CAACkG,gBAAgB,EAAEC,QAAQ,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}