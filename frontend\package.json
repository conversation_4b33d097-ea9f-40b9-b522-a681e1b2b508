{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "^3.2.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-data-grid": "^8.5.2", "@mui/x-date-pickers": "^8.5.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/plotly.js": "^3.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.10.0", "date-fns": "^4.1.0", "plotly.js": "^3.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "recharts": "^2.15.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"typescript": "^4.9.5"}}