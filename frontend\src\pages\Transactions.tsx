import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel } from '@mui/x-data-grid';
// import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Search, Clear, Download } from '@mui/icons-material';
import { format } from 'date-fns';
import { apiService, Transaction, TransactionFilters } from '../services/apiService';

const Transactions: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: 50,
  });

  const handlePaginationModelChange = (newModel: GridPaginationModel) => {
    setPaginationModel(newModel);
  };

  // Filter states
  const [filters, setFilters] = useState<TransactionFilters>({
    symbol: '',
    person_name: '',
    person_category: '',
    transaction_type: '',
    from_date: '',
    to_date: '',
    min_value: undefined,
    max_value: undefined,
    sort_by: 'transaction_date',
    sort_order: 'desc',
  });

  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);

  const fetchTransactions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const requestFilters: TransactionFilters = {
        ...filters,
        page: paginationModel.page + 1, // API uses 1-based pagination
        limit: paginationModel.pageSize,
        from_date: fromDate ? format(fromDate, 'yyyy-MM-dd') : undefined,
        to_date: toDate ? format(toDate, 'yyyy-MM-dd') : undefined,
      };

      // Remove empty filters
      Object.keys(requestFilters).forEach(key => {
        const value = requestFilters[key as keyof TransactionFilters];
        if (value === '' || value === undefined || value === null) {
          delete requestFilters[key as keyof TransactionFilters];
        }
      });

      const response = await apiService.getTransactions(requestFilters);
      setTransactions(response.data.transactions);
      setTotalCount(response.data.total_count);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError(apiService.formatError(err));
    } finally {
      setLoading(false);
    }
  }, [filters, paginationModel, fromDate, toDate]);

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  const handleFilterChange = (field: keyof TransactionFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
    }));
    setPaginationModel(prev => ({ ...prev, page: 0 })); // Reset to first page
  };

  const handleClearFilters = () => {
    setFilters({
      symbol: '',
      person_name: '',
      person_category: '',
      transaction_type: '',
      min_value: undefined,
      max_value: undefined,
      sort_by: 'transaction_date',
      sort_order: 'desc',
    });
    setFromDate(null);
    setToDate(null);
  };

  const formatCurrency = (value: number | undefined | null) => {
    if (!value || value === 0) return '-';
    if (value >= 10000000) {
      return `₹${(value / 10000000).toFixed(2)}Cr`;
    } else if (value >= 100000) {
      return `₹${(value / 100000).toFixed(2)}L`;
    } else {
      return `₹${value.toLocaleString()}`;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'symbol',
      headerName: 'Symbol',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2" fontWeight={600}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'company_name',
      headerName: 'Company',
      width: 200,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
          title={params.value}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'person_name',
      headerName: 'Person',
      width: 180,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
          title={params.value}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'person_category',
      headerName: 'Category',
      width: 130,
      renderCell: (params) => (
        params.value ? (
          <Chip
            label={params.value}
            size="small"
            variant="outlined"
            color="primary"
          />
        ) : null
      ),
    },
    {
      field: 'transaction_type',
      headerName: 'Type',
      width: 120,
      renderCell: (params) => {
        if (!params.value) return null;
        const color = params.value.toLowerCase().includes('buy') ? 'success' : 
                     params.value.toLowerCase().includes('sell') ? 'error' : 'info';
        return (
          <Chip
            label={params.value}
            size="small"
            color={color as any}
          />
        );
      },
    },
    {
      field: 'security_value',
      headerName: 'Value',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" fontWeight={600}>
          {formatCurrency(params.value)}
        </Typography>
      ),
    },
    {
      field: 'securities_acquired',
      headerName: 'Shares',
      width: 100,
      align: 'right',
      renderCell: (params) => (
        params.value ? (
          <Typography variant="body2">
            {params.value.toLocaleString()}
          </Typography>
        ) : null
      ),
    },
    {
      field: 'percentage_after_transaction',
      headerName: 'Holding %',
      width: 100,
      align: 'right',
      renderCell: (params) => (
        params.value ? (
          <Typography variant="body2">
            {params.value.toFixed(2)}%
          </Typography>
        ) : null
      ),
    },
    {
      field: 'transaction_date',
      headerName: 'Date',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {formatDate(params.value)}
        </Typography>
      ),
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          Transactions
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Browse and filter insider trading transactions
        </Typography>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <TextField
                fullWidth
                size="small"
                label="Symbol"
                value={filters.symbol}
                onChange={(e) => handleFilterChange('symbol', e.target.value)}
                placeholder="e.g., RELIANCE"
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <TextField
                fullWidth
                size="small"
                label="Person Name"
                value={filters.person_name}
                onChange={(e) => handleFilterChange('person_name', e.target.value)}
                placeholder="Search person"
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <TextField
                fullWidth
                size="small"
                label="Category"
                value={filters.person_category}
                onChange={(e) => handleFilterChange('person_category', e.target.value)}
                placeholder="e.g., Promoter"
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <TextField
                fullWidth
                size="small"
                label="From Date"
                type="date"
                value={fromDate ? fromDate.toISOString().split('T')[0] : ''}
                onChange={(e) => setFromDate(e.target.value ? new Date(e.target.value) : null)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <TextField
                fullWidth
                size="small"
                label="To Date"
                type="date"
                value={toDate ? toDate.toISOString().split('T')[0] : ''}
                onChange={(e) => setToDate(e.target.value ? new Date(e.target.value) : null)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6, md: 2 }}>
              <Box display="flex" gap={1}>
                <Button
                  variant="contained"
                  startIcon={<Search />}
                  onClick={fetchTransactions}
                  disabled={loading}
                >
                  Search
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Clear />}
                  onClick={handleClearFilters}
                >
                  Clear
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Data Grid */}
      <Card>
        <CardContent sx={{ p: 0 }}>
          <DataGrid
            rows={transactions}
            columns={columns}
            paginationModel={paginationModel}
            onPaginationModelChange={handlePaginationModelChange}
            pageSizeOptions={[25, 50, 100]}
            rowCount={totalCount}
            paginationMode="server"
            loading={loading}
            disableRowSelectionOnClick
            sx={{
              border: 0,
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid',
                borderBottomColor: 'divider',
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: 'grey.50',
                borderBottom: '2px solid',
                borderBottomColor: 'divider',
              },
            }}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 50 },
              },
            }}
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default Transactions;
