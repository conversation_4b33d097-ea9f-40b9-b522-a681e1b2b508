import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Chip,
  Tooltip,
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  Refresh,
  CheckCircle,
  Warning,
  Error,
  Schedule,
} from '@mui/icons-material';
import { formatDistanceToNow, format } from 'date-fns';
import { apiService } from '../services/apiService';

interface LastFetchedStatusProps {
  onRefresh?: () => void;
  compact?: boolean;
}

interface SystemStatus {
  api_status: string;
  database_status: {
    total_transactions: number;
    total_companies: number;
    date_range?: {
      earliest: string;
      latest: string;
    };
    last_updated?: string;
  };
  scraper_status: {
    last_execution?: string;
    status: string;
    records_fetched: number;
    records_inserted: number;
    records_skipped: number;
    error_message?: string;
  };
  timestamp: string;
}

const LastFetchedStatus: React.FC<LastFetchedStatusProps> = ({ 
  onRefresh, 
  compact = false 
}) => {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getSystemStatus();
      setStatus(response.data);
    } catch (err) {
      console.error('Error fetching system status:', err);
      setError('Failed to fetch status');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    // Refresh status every 30 seconds
    const interval = setInterval(fetchStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchStatus();
    if (onRefresh) {
      onRefresh();
    }
  };

  const getStatusColor = (lastUpdated: string) => {
    const now = new Date();
    const updated = new Date(lastUpdated);
    const diffMinutes = (now.getTime() - updated.getTime()) / (1000 * 60);
    
    if (diffMinutes < 30) return 'success';
    if (diffMinutes < 60) return 'warning';
    return 'error';
  };

  const getStatusIcon = (lastUpdated: string) => {
    const color = getStatusColor(lastUpdated);
    switch (color) {
      case 'success':
        return <CheckCircle color="success" fontSize="small" />;
      case 'warning':
        return <Warning color="warning" fontSize="small" />;
      case 'error':
        return <Error color="error" fontSize="small" />;
      default:
        return <Schedule color="disabled" fontSize="small" />;
    }
  };

  const formatLastUpdated = (lastUpdated: string) => {
    try {
      const date = new Date(lastUpdated);
      const timeAgo = formatDistanceToNow(date, { addSuffix: true });
      const fullDate = format(date, 'MMM dd, yyyy \'at\' h:mm a');
      return { timeAgo, fullDate };
    } catch {
      return { timeAgo: 'Unknown', fullDate: 'Unknown' };
    }
  };

  if (loading && !status) {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <CircularProgress size={16} />
        <Typography variant="body2" color="text.secondary">
          Loading status...
        </Typography>
      </Box>
    );
  }

  if (error || !status) {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Error color="error" fontSize="small" />
        <Typography variant="body2" color="error">
          Status unavailable
        </Typography>
        <IconButton size="small" onClick={handleRefresh}>
          <Refresh fontSize="small" />
        </IconButton>
      </Box>
    );
  }

  const lastUpdated = status.database_status.last_updated || status.timestamp;
  const { timeAgo, fullDate } = formatLastUpdated(lastUpdated);
  const statusColor = getStatusColor(lastUpdated);

  if (compact) {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        {getStatusIcon(lastUpdated)}
        <Tooltip title={`Last updated: ${fullDate}`}>
          <Typography variant="body2" color="text.secondary">
            Updated {timeAgo}
          </Typography>
        </Tooltip>
        <IconButton size="small" onClick={handleRefresh} disabled={loading}>
          <Refresh fontSize="small" />
        </IconButton>
      </Box>
    );
  }

  return (
    <Box 
      sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 2,
        p: 2,
        backgroundColor: 'background.paper',
        borderRadius: 1,
        border: 1,
        borderColor: 'divider',
      }}
    >
      <Box display="flex" alignItems="center" gap={1}>
        {getStatusIcon(lastUpdated)}
        <Box>
          <Typography variant="body2" fontWeight={600}>
            Data Status
          </Typography>
          <Tooltip title={fullDate}>
            <Typography variant="caption" color="text.secondary">
              Last updated {timeAgo}
            </Typography>
          </Tooltip>
        </Box>
      </Box>

      <Box display="flex" alignItems="center" gap={1}>
        <Chip
          label={`${status.database_status.total_transactions} transactions`}
          size="small"
          variant="outlined"
          color="primary"
        />
        <Chip
          label={`${status.database_status.total_companies} companies`}
          size="small"
          variant="outlined"
          color="secondary"
        />
      </Box>

      <Box display="flex" alignItems="center" gap={1}>
        <Chip
          label={status.api_status === 'healthy' ? 'API Online' : 'API Issues'}
          size="small"
          color={status.api_status === 'healthy' ? 'success' : 'error'}
        />
        <Tooltip title="Refresh data status">
          <IconButton size="small" onClick={handleRefresh} disabled={loading}>
            <Refresh fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default LastFetchedStatus;
