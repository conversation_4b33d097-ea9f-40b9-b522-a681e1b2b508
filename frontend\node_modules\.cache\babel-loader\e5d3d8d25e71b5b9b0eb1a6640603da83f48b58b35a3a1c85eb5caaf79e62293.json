{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _reactIs = require(\"react-is\");\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _refType = _interopRequireDefault(require(\"@mui/utils/refType\"));\nvar _composeClasses = _interopRequireDefault(require(\"@mui/utils/composeClasses\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _useSlotProps = _interopRequireDefault(require(\"@mui/utils/useSlotProps\"));\nvar _zeroStyled = require(\"../zero-styled\");\nvar _memoTheme = _interopRequireDefault(require(\"../utils/memoTheme\"));\nvar _DefaultPropsProvider = require(\"../DefaultPropsProvider\");\nvar _debounce = _interopRequireDefault(require(\"../utils/debounce\"));\nvar _animate = _interopRequireDefault(require(\"../internal/animate\"));\nvar _ScrollbarSize = _interopRequireDefault(require(\"./ScrollbarSize\"));\nvar _TabScrollButton = _interopRequireDefault(require(\"../TabScrollButton\"));\nvar _useEventCallback = _interopRequireDefault(require(\"../utils/useEventCallback\"));\nvar _tabsClasses = _interopRequireWildcard(require(\"./tabsClasses\"));\nvar _ownerDocument = _interopRequireDefault(require(\"../utils/ownerDocument\"));\nvar _ownerWindow = _interopRequireDefault(require(\"../utils/ownerWindow\"));\nvar _useSlot = _interopRequireDefault(require(\"../utils/useSlot\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst nextItem = (list, item) => {\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    }\n\n    // Same logic as useAutocomplete.js\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    vertical,\n    fixed,\n    hideScrollbar,\n    scrollableX,\n    scrollableY,\n    centered,\n    scrollButtonsHideMobile,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', vertical && 'vertical'],\n    scroller: ['scroller', fixed && 'fixed', hideScrollbar && 'hideScrollbar', scrollableX && 'scrollableX', scrollableY && 'scrollableY'],\n    list: ['list', 'flexContainer', vertical && 'flexContainerVertical', vertical && 'vertical', centered && 'centered'],\n    indicator: ['indicator'],\n    scrollButtons: ['scrollButtons', scrollButtonsHideMobile && 'scrollButtonsHideMobile'],\n    scrollableX: [scrollableX && 'scrollableX'],\n    hideScrollbar: [hideScrollbar && 'hideScrollbar']\n  };\n  return (0, _composeClasses.default)(slots, _tabsClasses.getTabsUtilityClass, classes);\n};\nconst TabsRoot = (0, _zeroStyled.styled)('div', {\n  name: 'MuiTabs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${_tabsClasses.default.scrollButtons}`]: styles.scrollButtons\n    }, {\n      [`& .${_tabsClasses.default.scrollButtons}`]: ownerState.scrollButtonsHideMobile && styles.scrollButtonsHideMobile\n    }, styles.root, ownerState.vertical && styles.vertical];\n  }\n})((0, _memoTheme.default)(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minHeight: 48,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollButtonsHideMobile,\n    style: {\n      [`& .${_tabsClasses.default.scrollButtons}`]: {\n        [theme.breakpoints.down('sm')]: {\n          display: 'none'\n        }\n      }\n    }\n  }]\n})));\nconst TabsScroller = (0, _zeroStyled.styled)('div', {\n  name: 'MuiTabs',\n  slot: 'Scroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.scroller, ownerState.fixed && styles.fixed, ownerState.hideScrollbar && styles.hideScrollbar, ownerState.scrollableX && styles.scrollableX, ownerState.scrollableY && styles.scrollableY];\n  }\n})({\n  position: 'relative',\n  display: 'inline-block',\n  flex: '1 1 auto',\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.fixed,\n    style: {\n      overflowX: 'hidden',\n      width: '100%'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hideScrollbar,\n    style: {\n      // Hide dimensionless scrollbar on macOS\n      scrollbarWidth: 'none',\n      // Firefox\n      '&::-webkit-scrollbar': {\n        display: 'none' // Safari + Chrome\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableX,\n    style: {\n      overflowX: 'auto',\n      overflowY: 'hidden'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableY,\n    style: {\n      overflowY: 'auto',\n      overflowX: 'hidden'\n    }\n  }]\n});\nconst List = (0, _zeroStyled.styled)('div', {\n  name: 'MuiTabs',\n  slot: 'List',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.list, styles.flexContainer, ownerState.vertical && styles.flexContainerVertical, ownerState.centered && styles.centered];\n  }\n})({\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.centered,\n    style: {\n      justifyContent: 'center'\n    }\n  }]\n});\nconst TabsIndicator = (0, _zeroStyled.styled)('span', {\n  name: 'MuiTabs',\n  slot: 'Indicator'\n})((0, _memoTheme.default)(({\n  theme\n}) => ({\n  position: 'absolute',\n  height: 2,\n  bottom: 0,\n  width: '100%',\n  transition: theme.transitions.create(),\n  variants: [{\n    props: {\n      indicatorColor: 'primary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.primary.main\n    }\n  }, {\n    props: {\n      indicatorColor: 'secondary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.secondary.main\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      height: '100%',\n      width: 2,\n      right: 0\n    }\n  }]\n})));\nconst TabsScrollbarSize = (0, _zeroStyled.styled)(_ScrollbarSize.default)({\n  overflowX: 'auto',\n  overflowY: 'hidden',\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n});\nconst defaultIndicatorStyle = {};\nlet warnedOnceTabPresent = false;\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(inProps, ref) {\n  const props = (0, _DefaultPropsProvider.useDefaultProps)({\n    props: inProps,\n    name: 'MuiTabs'\n  });\n  const theme = (0, _zeroStyled.useTheme)();\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    action,\n    centered = false,\n    children: childrenProp,\n    className,\n    component = 'div',\n    allowScrollButtonsMobile = false,\n    indicatorColor = 'primary',\n    onChange,\n    orientation = 'horizontal',\n    ScrollButtonComponent,\n    // TODO: remove in v7 (deprecated in v6)\n    scrollButtons = 'auto',\n    selectionFollowsFocus,\n    slots = {},\n    slotProps = {},\n    TabIndicatorProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    TabScrollButtonProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    textColor = 'primary',\n    value,\n    variant = 'standard',\n    visibleScrollbar = false,\n    ...other\n  } = props;\n  const scrollable = variant === 'scrollable';\n  const vertical = orientation === 'vertical';\n  const scrollStart = vertical ? 'scrollTop' : 'scrollLeft';\n  const start = vertical ? 'top' : 'left';\n  const end = vertical ? 'bottom' : 'right';\n  const clientSize = vertical ? 'clientHeight' : 'clientWidth';\n  const size = vertical ? 'height' : 'width';\n  const ownerState = {\n    ...props,\n    component,\n    allowScrollButtonsMobile,\n    indicatorColor,\n    orientation,\n    vertical,\n    scrollButtons,\n    textColor,\n    variant,\n    visibleScrollbar,\n    fixed: !scrollable,\n    hideScrollbar: scrollable && !visibleScrollbar,\n    scrollableX: scrollable && !vertical,\n    scrollableY: scrollable && vertical,\n    centered: centered && !scrollable,\n    scrollButtonsHideMobile: !allowScrollButtonsMobile\n  };\n  const classes = useUtilityClasses(ownerState);\n  const startScrollButtonIconProps = (0, _useSlotProps.default)({\n    elementType: slots.StartScrollButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    ownerState\n  });\n  const endScrollButtonIconProps = (0, _useSlotProps.default)({\n    elementType: slots.EndScrollButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    ownerState\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (centered && scrollable) {\n      console.error('MUI: You can not use the `centered={true}` and `variant=\"scrollable\"` properties ' + 'at the same time on a `Tabs` component.');\n    }\n  }\n  const [mounted, setMounted] = React.useState(false);\n  const [indicatorStyle, setIndicatorStyle] = React.useState(defaultIndicatorStyle);\n  const [displayStartScroll, setDisplayStartScroll] = React.useState(false);\n  const [displayEndScroll, setDisplayEndScroll] = React.useState(false);\n  const [updateScrollObserver, setUpdateScrollObserver] = React.useState(false);\n  const [scrollerStyle, setScrollerStyle] = React.useState({\n    overflow: 'hidden',\n    scrollbarWidth: 0\n  });\n  const valueToIndex = new Map();\n  const tabsRef = React.useRef(null);\n  const tabListRef = React.useRef(null);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      indicator: TabIndicatorProps,\n      scrollButton: TabScrollButtonProps,\n      ...slotProps\n    }\n  };\n  const getTabsMeta = () => {\n    const tabsNode = tabsRef.current;\n    let tabsMeta;\n    if (tabsNode) {\n      const rect = tabsNode.getBoundingClientRect();\n      // create a new object with ClientRect class props + scrollLeft\n      tabsMeta = {\n        clientWidth: tabsNode.clientWidth,\n        scrollLeft: tabsNode.scrollLeft,\n        scrollTop: tabsNode.scrollTop,\n        scrollWidth: tabsNode.scrollWidth,\n        top: rect.top,\n        bottom: rect.bottom,\n        left: rect.left,\n        right: rect.right\n      };\n    }\n    let tabMeta;\n    if (tabsNode && value !== false) {\n      const children = tabListRef.current.children;\n      if (children.length > 0) {\n        const tab = children[valueToIndex.get(value)];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!tab) {\n            console.error([`MUI: The \\`value\\` provided to the Tabs component is invalid.`, `None of the Tabs' children match with \"${value}\".`, valueToIndex.keys ? `You can provide one of the following values: ${Array.from(valueToIndex.keys()).join(', ')}.` : null].join('\\n'));\n          }\n        }\n        tabMeta = tab ? tab.getBoundingClientRect() : null;\n        if (process.env.NODE_ENV !== 'production') {\n          if (process.env.NODE_ENV !== 'test' && !warnedOnceTabPresent && tabMeta && tabMeta.width === 0 && tabMeta.height === 0 &&\n          // if the whole Tabs component is hidden, don't warn\n          tabsMeta.clientWidth !== 0) {\n            tabsMeta = null;\n            console.error(['MUI: The `value` provided to the Tabs component is invalid.', `The Tab with this \\`value\\` (\"${value}\") is not part of the document layout.`, \"Make sure the tab item is present in the document or that it's not `display: none`.\"].join('\\n'));\n            warnedOnceTabPresent = true;\n          }\n        }\n      }\n    }\n    return {\n      tabsMeta,\n      tabMeta\n    };\n  };\n  const updateIndicatorState = (0, _useEventCallback.default)(() => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    let startValue = 0;\n    let startIndicator;\n    if (vertical) {\n      startIndicator = 'top';\n      if (tabMeta && tabsMeta) {\n        startValue = tabMeta.top - tabsMeta.top + tabsMeta.scrollTop;\n      }\n    } else {\n      startIndicator = isRtl ? 'right' : 'left';\n      if (tabMeta && tabsMeta) {\n        startValue = (isRtl ? -1 : 1) * (tabMeta[startIndicator] - tabsMeta[startIndicator] + tabsMeta.scrollLeft);\n      }\n    }\n    const newIndicatorStyle = {\n      [startIndicator]: startValue,\n      // May be wrong until the font is loaded.\n      [size]: tabMeta ? tabMeta[size] : 0\n    };\n    if (typeof indicatorStyle[startIndicator] !== 'number' || typeof indicatorStyle[size] !== 'number') {\n      setIndicatorStyle(newIndicatorStyle);\n    } else {\n      const dStart = Math.abs(indicatorStyle[startIndicator] - newIndicatorStyle[startIndicator]);\n      const dSize = Math.abs(indicatorStyle[size] - newIndicatorStyle[size]);\n      if (dStart >= 1 || dSize >= 1) {\n        setIndicatorStyle(newIndicatorStyle);\n      }\n    }\n  });\n  const scroll = (scrollValue, {\n    animation = true\n  } = {}) => {\n    if (animation) {\n      (0, _animate.default)(scrollStart, tabsRef.current, scrollValue, {\n        duration: theme.transitions.duration.standard\n      });\n    } else {\n      tabsRef.current[scrollStart] = scrollValue;\n    }\n  };\n  const moveTabsScroll = delta => {\n    let scrollValue = tabsRef.current[scrollStart];\n    if (vertical) {\n      scrollValue += delta;\n    } else {\n      scrollValue += delta * (isRtl ? -1 : 1);\n    }\n    scroll(scrollValue);\n  };\n  const getScrollSize = () => {\n    const containerSize = tabsRef.current[clientSize];\n    let totalSize = 0;\n    const children = Array.from(tabListRef.current.children);\n    for (let i = 0; i < children.length; i += 1) {\n      const tab = children[i];\n      if (totalSize + tab[clientSize] > containerSize) {\n        // If the first item is longer than the container size, then only scroll\n        // by the container size.\n        if (i === 0) {\n          totalSize = containerSize;\n        }\n        break;\n      }\n      totalSize += tab[clientSize];\n    }\n    return totalSize;\n  };\n  const handleStartScrollClick = () => {\n    moveTabsScroll(-1 * getScrollSize());\n  };\n  const handleEndScrollClick = () => {\n    moveTabsScroll(getScrollSize());\n  };\n  const [ScrollbarSlot, {\n    onChange: scrollbarOnChange,\n    ...scrollbarSlotProps\n  }] = (0, _useSlot.default)('scrollbar', {\n    className: (0, _clsx.default)(classes.scrollableX, classes.hideScrollbar),\n    elementType: TabsScrollbarSize,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // TODO Remove <ScrollbarSize /> as browser support for hiding the scrollbar\n  // with CSS improves.\n  const handleScrollbarSizeChange = React.useCallback(scrollbarWidth => {\n    scrollbarOnChange?.(scrollbarWidth);\n    setScrollerStyle({\n      overflow: null,\n      scrollbarWidth\n    });\n  }, [scrollbarOnChange]);\n  const [ScrollButtonsSlot, scrollButtonSlotProps] = (0, _useSlot.default)('scrollButtons', {\n    className: (0, _clsx.default)(classes.scrollButtons, TabScrollButtonProps.className),\n    elementType: _TabScrollButton.default,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      orientation,\n      slots: {\n        StartScrollButtonIcon: slots.startScrollButtonIcon || slots.StartScrollButtonIcon,\n        EndScrollButtonIcon: slots.endScrollButtonIcon || slots.EndScrollButtonIcon\n      },\n      slotProps: {\n        startScrollButtonIcon: startScrollButtonIconProps,\n        endScrollButtonIcon: endScrollButtonIconProps\n      }\n    }\n  });\n  const getConditionalElements = () => {\n    const conditionalElements = {};\n    conditionalElements.scrollbarSizeListener = scrollable ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScrollbarSlot, {\n      ...scrollbarSlotProps,\n      onChange: handleScrollbarSizeChange\n    }) : null;\n    const scrollButtonsActive = displayStartScroll || displayEndScroll;\n    const showScrollButtons = scrollable && (scrollButtons === 'auto' && scrollButtonsActive || scrollButtons === true);\n    conditionalElements.scrollButtonStart = showScrollButtons ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScrollButtonsSlot, {\n      direction: isRtl ? 'right' : 'left',\n      onClick: handleStartScrollClick,\n      disabled: !displayStartScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    conditionalElements.scrollButtonEnd = showScrollButtons ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScrollButtonsSlot, {\n      direction: isRtl ? 'left' : 'right',\n      onClick: handleEndScrollClick,\n      disabled: !displayEndScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    return conditionalElements;\n  };\n  const scrollSelectedIntoView = (0, _useEventCallback.default)(animation => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    if (!tabMeta || !tabsMeta) {\n      return;\n    }\n    if (tabMeta[start] < tabsMeta[start]) {\n      // left side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[start] - tabsMeta[start]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    } else if (tabMeta[end] > tabsMeta[end]) {\n      // right side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[end] - tabsMeta[end]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    }\n  });\n  const updateScrollButtonState = (0, _useEventCallback.default)(() => {\n    if (scrollable && scrollButtons !== false) {\n      setUpdateScrollObserver(!updateScrollObserver);\n    }\n  });\n  React.useEffect(() => {\n    const handleResize = (0, _debounce.default)(() => {\n      // If the Tabs component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/33276\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n      if (tabsRef.current) {\n        updateIndicatorState();\n      }\n    });\n    let resizeObserver;\n\n    /**\n     * @type {MutationCallback}\n     */\n    const handleMutation = records => {\n      records.forEach(record => {\n        record.removedNodes.forEach(item => {\n          resizeObserver?.unobserve(item);\n        });\n        record.addedNodes.forEach(item => {\n          resizeObserver?.observe(item);\n        });\n      });\n      handleResize();\n      updateScrollButtonState();\n    };\n    const win = (0, _ownerWindow.default)(tabsRef.current);\n    win.addEventListener('resize', handleResize);\n    let mutationObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      Array.from(tabListRef.current.children).forEach(child => {\n        resizeObserver.observe(child);\n      });\n    }\n    if (typeof MutationObserver !== 'undefined') {\n      mutationObserver = new MutationObserver(handleMutation);\n      mutationObserver.observe(tabListRef.current, {\n        childList: true\n      });\n    }\n    return () => {\n      handleResize.clear();\n      win.removeEventListener('resize', handleResize);\n      mutationObserver?.disconnect();\n      resizeObserver?.disconnect();\n    };\n  }, [updateIndicatorState, updateScrollButtonState]);\n\n  /**\n   * Toggle visibility of start and end scroll buttons\n   * Using IntersectionObserver on first and last Tabs.\n   */\n  React.useEffect(() => {\n    const tabListChildren = Array.from(tabListRef.current.children);\n    const length = tabListChildren.length;\n    if (typeof IntersectionObserver !== 'undefined' && length > 0 && scrollable && scrollButtons !== false) {\n      const firstTab = tabListChildren[0];\n      const lastTab = tabListChildren[length - 1];\n      const observerOptions = {\n        root: tabsRef.current,\n        threshold: 0.99\n      };\n      const handleScrollButtonStart = entries => {\n        setDisplayStartScroll(!entries[0].isIntersecting);\n      };\n      const firstObserver = new IntersectionObserver(handleScrollButtonStart, observerOptions);\n      firstObserver.observe(firstTab);\n      const handleScrollButtonEnd = entries => {\n        setDisplayEndScroll(!entries[0].isIntersecting);\n      };\n      const lastObserver = new IntersectionObserver(handleScrollButtonEnd, observerOptions);\n      lastObserver.observe(lastTab);\n      return () => {\n        firstObserver.disconnect();\n        lastObserver.disconnect();\n      };\n    }\n    return undefined;\n  }, [scrollable, scrollButtons, updateScrollObserver, childrenProp?.length]);\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    updateIndicatorState();\n  });\n  React.useEffect(() => {\n    // Don't animate on the first render.\n    scrollSelectedIntoView(defaultIndicatorStyle !== indicatorStyle);\n  }, [scrollSelectedIntoView, indicatorStyle]);\n  React.useImperativeHandle(action, () => ({\n    updateIndicator: updateIndicatorState,\n    updateScrollButtons: updateScrollButtonState\n  }), [updateIndicatorState, updateScrollButtonState]);\n  const [IndicatorSlot, indicatorSlotProps] = (0, _useSlot.default)('indicator', {\n    className: (0, _clsx.default)(classes.indicator, TabIndicatorProps.className),\n    elementType: TabsIndicator,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: indicatorStyle\n    }\n  });\n  const indicator = /*#__PURE__*/(0, _jsxRuntime.jsx)(IndicatorSlot, {\n    ...indicatorSlotProps\n  });\n  let childIndex = 0;\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if ((0, _reactIs.isFragment)(child)) {\n        console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    const childValue = child.props.value === undefined ? childIndex : child.props.value;\n    valueToIndex.set(childValue, childIndex);\n    const selected = childValue === value;\n    childIndex += 1;\n    return /*#__PURE__*/React.cloneElement(child, {\n      fullWidth: variant === 'fullWidth',\n      indicator: selected && !mounted && indicator,\n      selected,\n      selectionFollowsFocus,\n      onChange,\n      textColor,\n      value: childValue,\n      ...(childIndex === 1 && value === false && !child.props.tabIndex ? {\n        tabIndex: 0\n      } : {})\n    });\n  });\n  const handleKeyDown = event => {\n    // Check if a modifier key (Alt, Shift, Ctrl, Meta) is pressed\n    if (event.altKey || event.shiftKey || event.ctrlKey || event.metaKey) {\n      return;\n    }\n    const list = tabListRef.current;\n    const currentFocus = (0, _ownerDocument.default)(list).activeElement;\n    // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n    const role = currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const conditionalElements = getConditionalElements();\n  const [RootSlot, rootSlotProps] = (0, _useSlot.default)('root', {\n    ref,\n    className: (0, _clsx.default)(classes.root, className),\n    elementType: TabsRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    ownerState\n  });\n  const [ScrollerSlot, scrollerSlotProps] = (0, _useSlot.default)('scroller', {\n    ref: tabsRef,\n    className: classes.scroller,\n    elementType: TabsScroller,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: {\n        overflow: scrollerStyle.overflow,\n        [vertical ? `margin${isRtl ? 'Left' : 'Right'}` : 'marginBottom']: visibleScrollbar ? undefined : -scrollerStyle.scrollbarWidth\n      }\n    }\n  });\n  const [ListSlot, listSlotProps] = (0, _useSlot.default)('list', {\n    ref: tabListRef,\n    className: (0, _clsx.default)(classes.list, classes.flexContainer),\n    elementType: List,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onKeyDown: event => {\n        handleKeyDown(event);\n        handlers.onKeyDown?.(event);\n      }\n    })\n  });\n  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(RootSlot, {\n    ...rootSlotProps,\n    children: [conditionalElements.scrollButtonStart, conditionalElements.scrollbarSizeListener, /*#__PURE__*/(0, _jsxRuntime.jsxs)(ScrollerSlot, {\n      ...scrollerSlotProps,\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(ListSlot, {\n        \"aria-label\": ariaLabel,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-orientation\": orientation === 'vertical' ? 'vertical' : null,\n        role: \"tablist\",\n        ...listSlotProps,\n        children: children\n      }), mounted && indicator]\n    }), conditionalElements.scrollButtonEnd]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Callback fired when the component mounts.\n   * This is useful when you want to trigger an action programmatically.\n   * It supports two actions: `updateIndicator()` and `updateScrollButtons()`\n   *\n   * @param {object} actions This object contains all possible actions\n   * that can be triggered programmatically.\n   */\n  action: _refType.default,\n  /**\n   * If `true`, the scroll buttons aren't forced hidden on mobile.\n   * By default the scroll buttons are hidden on mobile and takes precedence over `scrollButtons`.\n   * @default false\n   */\n  allowScrollButtonsMobile: _propTypes.default.bool,\n  /**\n   * The label for the Tabs as a string.\n   */\n  'aria-label': _propTypes.default.string,\n  /**\n   * An id or list of ids separated by a space that label the Tabs.\n   */\n  'aria-labelledby': _propTypes.default.string,\n  /**\n   * If `true`, the tabs are centered.\n   * This prop is intended for large views.\n   * @default false\n   */\n  centered: _propTypes.default.bool,\n  /**\n   * The content of the component.\n   */\n  children: _propTypes.default.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: _propTypes.default.object,\n  /**\n   * @ignore\n   */\n  className: _propTypes.default.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: _propTypes.default.elementType,\n  /**\n   * Determines the color of the indicator.\n   * @default 'primary'\n   */\n  indicatorColor: _propTypes.default /* @typescript-to-proptypes-ignore */.oneOfType([_propTypes.default.oneOf(['primary', 'secondary']), _propTypes.default.string]),\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child (number)\n   */\n  onChange: _propTypes.default.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: _propTypes.default.oneOf(['horizontal', 'vertical']),\n  /**\n   * The component used to render the scroll buttons.\n   * @deprecated use the `slots.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default TabScrollButton\n   */\n  ScrollButtonComponent: _propTypes.default.elementType,\n  /**\n   * Determine behavior of scroll buttons when tabs are set to scroll:\n   *\n   * - `auto` will only present them when not all the items are visible.\n   * - `true` will always present them.\n   * - `false` will never present them.\n   *\n   * By default the scroll buttons are hidden on mobile.\n   * This behavior can be disabled with `allowScrollButtonsMobile`.\n   * @default 'auto'\n   */\n  scrollButtons: _propTypes.default /* @typescript-to-proptypes-ignore */.oneOf(['auto', false, true]),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: _propTypes.default.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: _propTypes.default.shape({\n    endScrollButtonIcon: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    indicator: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    list: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    root: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    scrollbar: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    scrollButtons: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    scroller: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    startScrollButtonIcon: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: _propTypes.default.shape({\n    endScrollButtonIcon: _propTypes.default.elementType,\n    EndScrollButtonIcon: _propTypes.default.elementType,\n    indicator: _propTypes.default.elementType,\n    list: _propTypes.default.elementType,\n    root: _propTypes.default.elementType,\n    scrollbar: _propTypes.default.elementType,\n    scrollButtons: _propTypes.default.elementType,\n    scroller: _propTypes.default.elementType,\n    startScrollButtonIcon: _propTypes.default.elementType,\n    StartScrollButtonIcon: _propTypes.default.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),\n  /**\n   * Props applied to the tab indicator element.\n   * @deprecated use the `slotProps.indicator` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default  {}\n   */\n  TabIndicatorProps: _propTypes.default.object,\n  /**\n   * Props applied to the [`TabScrollButton`](https://mui.com/material-ui/api/tab-scroll-button/) element.\n   * @deprecated use the `slotProps.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TabScrollButtonProps: _propTypes.default.object,\n  /**\n   * Determines the color of the `Tab`.\n   * @default 'primary'\n   */\n  textColor: _propTypes.default.oneOf(['inherit', 'primary', 'secondary']),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: _propTypes.default.any,\n  /**\n   * Determines additional display behavior of the tabs:\n   *\n   *  - `scrollable` will invoke scrolling properties and allow for horizontally\n   *  scrolling (or swiping) of the tab bar.\n   *  - `fullWidth` will make the tabs grow to use all the available space,\n   *  which should be used for small views, like on mobile.\n   *  - `standard` will render the default state.\n   * @default 'standard'\n   */\n  variant: _propTypes.default.oneOf(['fullWidth', 'scrollable', 'standard']),\n  /**\n   * If `true`, the scrollbar is visible. It can be useful when displaying\n   * a long vertical list of tabs.\n   * @default false\n   */\n  visibleScrollbar: _propTypes.default.bool\n} : void 0;\nvar _default = exports.default = Tabs;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "React", "_reactIs", "_propTypes", "_clsx", "_refType", "_composeClasses", "_RtlProvider", "_useSlotProps", "_zeroStyled", "_memoTheme", "_DefaultPropsProvider", "_debounce", "_animate", "_ScrollbarSize", "_TabScrollButton", "_useEventCallback", "_tabsClasses", "_ownerDocument", "_ownerW<PERSON>ow", "_useSlot", "_jsxRuntime", "nextItem", "list", "item", "<PERSON><PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "previousItem", "<PERSON><PERSON><PERSON><PERSON>", "previousElementSibling", "moveFocus", "currentFocus", "traversalFunction", "wrappedOnce", "nextFocus", "nextFocusDisabled", "disabled", "getAttribute", "hasAttribute", "focus", "useUtilityClasses", "ownerState", "vertical", "fixed", "hideScrollbar", "scrollableX", "scrollableY", "centered", "scrollButtonsHideMobile", "classes", "slots", "root", "scroller", "indicator", "scrollButtons", "getTabsUtilityClass", "TabsRoot", "styled", "name", "slot", "overridesResolver", "props", "styles", "theme", "overflow", "minHeight", "WebkitOverflowScrolling", "display", "variants", "style", "flexDirection", "breakpoints", "down", "TabsScroller", "position", "flex", "whiteSpace", "overflowX", "width", "scrollbarWidth", "overflowY", "List", "flexContainer", "flexContainerVertical", "justifyContent", "TabsIndicator", "height", "bottom", "transition", "transitions", "create", "indicatorColor", "backgroundColor", "vars", "palette", "primary", "main", "secondary", "right", "TabsScrollbarSize", "defaultIndicatorStyle", "warnedOnceTabPresent", "Tabs", "forwardRef", "inProps", "ref", "useDefaultProps", "useTheme", "isRtl", "useRtl", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "action", "children", "childrenProp", "className", "component", "allowScrollButtonsMobile", "onChange", "orientation", "ScrollButtonComponent", "selectionFollowsFocus", "slotProps", "TabIndicatorProps", "TabScrollButtonProps", "textColor", "variant", "visibleScrollbar", "other", "scrollable", "scrollStart", "start", "end", "clientSize", "size", "startScrollButtonIconProps", "elementType", "StartScrollButtonIcon", "externalSlotProps", "startScrollButtonIcon", "endScrollButtonIconProps", "EndScrollButtonIcon", "endScrollButtonIcon", "process", "env", "NODE_ENV", "console", "error", "mounted", "setMounted", "useState", "indicatorStyle", "setIndicatorStyle", "displayStartScroll", "setDisplayStartScroll", "displayEndScroll", "setDisplayEndScroll", "updateScrollObserver", "setUpdateScrollObserver", "scrollerStyle", "setScrollerStyle", "valueToIndex", "Map", "tabsRef", "useRef", "tabListRef", "externalForwardedProps", "scrollButton", "getTabsMeta", "tabsNode", "current", "tabsMeta", "rect", "getBoundingClientRect", "clientWidth", "scrollLeft", "scrollTop", "scrollWidth", "top", "left", "tabMeta", "length", "tab", "get", "keys", "Array", "from", "join", "updateIndicatorState", "startValue", "startIndicator", "newIndicatorStyle", "dStart", "Math", "abs", "dSize", "scroll", "scrollValue", "animation", "duration", "standard", "moveTabsScroll", "delta", "getScrollSize", "containerSize", "totalSize", "i", "handleStartScrollClick", "handleEndScrollClick", "ScrollbarSlot", "scrollbarOnChange", "scrollbarSlotProps", "shouldForwardComponentProp", "handleScrollbarSizeChange", "useCallback", "ScrollButtonsSlot", "scrollButtonSlotProps", "additionalProps", "getConditionalElements", "conditionalElements", "scrollbarSizeListener", "jsx", "scrollButtonsActive", "showScrollButtons", "scrollButtonStart", "direction", "onClick", "scrollButtonEnd", "scrollSelectedIntoView", "nextScrollStart", "updateScrollButtonState", "useEffect", "handleResize", "resizeObserver", "handleMutation", "records", "for<PERSON>ach", "record", "removedNodes", "unobserve", "addedNodes", "observe", "win", "addEventListener", "mutationObserver", "ResizeObserver", "child", "MutationObserver", "childList", "clear", "removeEventListener", "disconnect", "tabList<PERSON><PERSON><PERSON>n", "IntersectionObserver", "firstTab", "lastTab", "observerOptions", "threshold", "handleScrollButtonStart", "entries", "isIntersecting", "firstObserver", "handleScrollButtonEnd", "lastObserver", "undefined", "useImperativeHandle", "updateIndicator", "updateScrollButtons", "IndicatorSlot", "indicatorSlotProps", "childIndex", "Children", "map", "isValidElement", "isFragment", "childValue", "set", "selected", "cloneElement", "fullWidth", "tabIndex", "handleKeyDown", "event", "altKey", "shift<PERSON>ey", "ctrl<PERSON>ey", "metaKey", "activeElement", "role", "previousItemKey", "nextItemKey", "key", "preventDefault", "RootSlot", "rootSlotProps", "ScrollerSlot", "scrollerSlotProps", "ListSlot", "listSlotProps", "getSlotProps", "handlers", "onKeyDown", "jsxs", "propTypes", "bool", "string", "node", "object", "oneOfType", "oneOf", "func", "shape", "scrollbar", "sx", "arrayOf", "any", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/Tabs/Tabs.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _reactIs = require(\"react-is\");\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _refType = _interopRequireDefault(require(\"@mui/utils/refType\"));\nvar _composeClasses = _interopRequireDefault(require(\"@mui/utils/composeClasses\"));\nvar _RtlProvider = require(\"@mui/system/RtlProvider\");\nvar _useSlotProps = _interopRequireDefault(require(\"@mui/utils/useSlotProps\"));\nvar _zeroStyled = require(\"../zero-styled\");\nvar _memoTheme = _interopRequireDefault(require(\"../utils/memoTheme\"));\nvar _DefaultPropsProvider = require(\"../DefaultPropsProvider\");\nvar _debounce = _interopRequireDefault(require(\"../utils/debounce\"));\nvar _animate = _interopRequireDefault(require(\"../internal/animate\"));\nvar _ScrollbarSize = _interopRequireDefault(require(\"./ScrollbarSize\"));\nvar _TabScrollButton = _interopRequireDefault(require(\"../TabScrollButton\"));\nvar _useEventCallback = _interopRequireDefault(require(\"../utils/useEventCallback\"));\nvar _tabsClasses = _interopRequireWildcard(require(\"./tabsClasses\"));\nvar _ownerDocument = _interopRequireDefault(require(\"../utils/ownerDocument\"));\nvar _ownerWindow = _interopRequireDefault(require(\"../utils/ownerWindow\"));\nvar _useSlot = _interopRequireDefault(require(\"../utils/useSlot\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst nextItem = (list, item) => {\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    }\n\n    // Same logic as useAutocomplete.js\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    vertical,\n    fixed,\n    hideScrollbar,\n    scrollableX,\n    scrollableY,\n    centered,\n    scrollButtonsHideMobile,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', vertical && 'vertical'],\n    scroller: ['scroller', fixed && 'fixed', hideScrollbar && 'hideScrollbar', scrollableX && 'scrollableX', scrollableY && 'scrollableY'],\n    list: ['list', 'flexContainer', vertical && 'flexContainerVertical', vertical && 'vertical', centered && 'centered'],\n    indicator: ['indicator'],\n    scrollButtons: ['scrollButtons', scrollButtonsHideMobile && 'scrollButtonsHideMobile'],\n    scrollableX: [scrollableX && 'scrollableX'],\n    hideScrollbar: [hideScrollbar && 'hideScrollbar']\n  };\n  return (0, _composeClasses.default)(slots, _tabsClasses.getTabsUtilityClass, classes);\n};\nconst TabsRoot = (0, _zeroStyled.styled)('div', {\n  name: 'MuiTabs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${_tabsClasses.default.scrollButtons}`]: styles.scrollButtons\n    }, {\n      [`& .${_tabsClasses.default.scrollButtons}`]: ownerState.scrollButtonsHideMobile && styles.scrollButtonsHideMobile\n    }, styles.root, ownerState.vertical && styles.vertical];\n  }\n})((0, _memoTheme.default)(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minHeight: 48,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollButtonsHideMobile,\n    style: {\n      [`& .${_tabsClasses.default.scrollButtons}`]: {\n        [theme.breakpoints.down('sm')]: {\n          display: 'none'\n        }\n      }\n    }\n  }]\n})));\nconst TabsScroller = (0, _zeroStyled.styled)('div', {\n  name: 'MuiTabs',\n  slot: 'Scroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.scroller, ownerState.fixed && styles.fixed, ownerState.hideScrollbar && styles.hideScrollbar, ownerState.scrollableX && styles.scrollableX, ownerState.scrollableY && styles.scrollableY];\n  }\n})({\n  position: 'relative',\n  display: 'inline-block',\n  flex: '1 1 auto',\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.fixed,\n    style: {\n      overflowX: 'hidden',\n      width: '100%'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hideScrollbar,\n    style: {\n      // Hide dimensionless scrollbar on macOS\n      scrollbarWidth: 'none',\n      // Firefox\n      '&::-webkit-scrollbar': {\n        display: 'none' // Safari + Chrome\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableX,\n    style: {\n      overflowX: 'auto',\n      overflowY: 'hidden'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableY,\n    style: {\n      overflowY: 'auto',\n      overflowX: 'hidden'\n    }\n  }]\n});\nconst List = (0, _zeroStyled.styled)('div', {\n  name: 'MuiTabs',\n  slot: 'List',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.list, styles.flexContainer, ownerState.vertical && styles.flexContainerVertical, ownerState.centered && styles.centered];\n  }\n})({\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.centered,\n    style: {\n      justifyContent: 'center'\n    }\n  }]\n});\nconst TabsIndicator = (0, _zeroStyled.styled)('span', {\n  name: 'MuiTabs',\n  slot: 'Indicator'\n})((0, _memoTheme.default)(({\n  theme\n}) => ({\n  position: 'absolute',\n  height: 2,\n  bottom: 0,\n  width: '100%',\n  transition: theme.transitions.create(),\n  variants: [{\n    props: {\n      indicatorColor: 'primary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.primary.main\n    }\n  }, {\n    props: {\n      indicatorColor: 'secondary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.secondary.main\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      height: '100%',\n      width: 2,\n      right: 0\n    }\n  }]\n})));\nconst TabsScrollbarSize = (0, _zeroStyled.styled)(_ScrollbarSize.default)({\n  overflowX: 'auto',\n  overflowY: 'hidden',\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n});\nconst defaultIndicatorStyle = {};\nlet warnedOnceTabPresent = false;\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(inProps, ref) {\n  const props = (0, _DefaultPropsProvider.useDefaultProps)({\n    props: inProps,\n    name: 'MuiTabs'\n  });\n  const theme = (0, _zeroStyled.useTheme)();\n  const isRtl = (0, _RtlProvider.useRtl)();\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    action,\n    centered = false,\n    children: childrenProp,\n    className,\n    component = 'div',\n    allowScrollButtonsMobile = false,\n    indicatorColor = 'primary',\n    onChange,\n    orientation = 'horizontal',\n    ScrollButtonComponent,\n    // TODO: remove in v7 (deprecated in v6)\n    scrollButtons = 'auto',\n    selectionFollowsFocus,\n    slots = {},\n    slotProps = {},\n    TabIndicatorProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    TabScrollButtonProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    textColor = 'primary',\n    value,\n    variant = 'standard',\n    visibleScrollbar = false,\n    ...other\n  } = props;\n  const scrollable = variant === 'scrollable';\n  const vertical = orientation === 'vertical';\n  const scrollStart = vertical ? 'scrollTop' : 'scrollLeft';\n  const start = vertical ? 'top' : 'left';\n  const end = vertical ? 'bottom' : 'right';\n  const clientSize = vertical ? 'clientHeight' : 'clientWidth';\n  const size = vertical ? 'height' : 'width';\n  const ownerState = {\n    ...props,\n    component,\n    allowScrollButtonsMobile,\n    indicatorColor,\n    orientation,\n    vertical,\n    scrollButtons,\n    textColor,\n    variant,\n    visibleScrollbar,\n    fixed: !scrollable,\n    hideScrollbar: scrollable && !visibleScrollbar,\n    scrollableX: scrollable && !vertical,\n    scrollableY: scrollable && vertical,\n    centered: centered && !scrollable,\n    scrollButtonsHideMobile: !allowScrollButtonsMobile\n  };\n  const classes = useUtilityClasses(ownerState);\n  const startScrollButtonIconProps = (0, _useSlotProps.default)({\n    elementType: slots.StartScrollButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    ownerState\n  });\n  const endScrollButtonIconProps = (0, _useSlotProps.default)({\n    elementType: slots.EndScrollButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    ownerState\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (centered && scrollable) {\n      console.error('MUI: You can not use the `centered={true}` and `variant=\"scrollable\"` properties ' + 'at the same time on a `Tabs` component.');\n    }\n  }\n  const [mounted, setMounted] = React.useState(false);\n  const [indicatorStyle, setIndicatorStyle] = React.useState(defaultIndicatorStyle);\n  const [displayStartScroll, setDisplayStartScroll] = React.useState(false);\n  const [displayEndScroll, setDisplayEndScroll] = React.useState(false);\n  const [updateScrollObserver, setUpdateScrollObserver] = React.useState(false);\n  const [scrollerStyle, setScrollerStyle] = React.useState({\n    overflow: 'hidden',\n    scrollbarWidth: 0\n  });\n  const valueToIndex = new Map();\n  const tabsRef = React.useRef(null);\n  const tabListRef = React.useRef(null);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      indicator: TabIndicatorProps,\n      scrollButton: TabScrollButtonProps,\n      ...slotProps\n    }\n  };\n  const getTabsMeta = () => {\n    const tabsNode = tabsRef.current;\n    let tabsMeta;\n    if (tabsNode) {\n      const rect = tabsNode.getBoundingClientRect();\n      // create a new object with ClientRect class props + scrollLeft\n      tabsMeta = {\n        clientWidth: tabsNode.clientWidth,\n        scrollLeft: tabsNode.scrollLeft,\n        scrollTop: tabsNode.scrollTop,\n        scrollWidth: tabsNode.scrollWidth,\n        top: rect.top,\n        bottom: rect.bottom,\n        left: rect.left,\n        right: rect.right\n      };\n    }\n    let tabMeta;\n    if (tabsNode && value !== false) {\n      const children = tabListRef.current.children;\n      if (children.length > 0) {\n        const tab = children[valueToIndex.get(value)];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!tab) {\n            console.error([`MUI: The \\`value\\` provided to the Tabs component is invalid.`, `None of the Tabs' children match with \"${value}\".`, valueToIndex.keys ? `You can provide one of the following values: ${Array.from(valueToIndex.keys()).join(', ')}.` : null].join('\\n'));\n          }\n        }\n        tabMeta = tab ? tab.getBoundingClientRect() : null;\n        if (process.env.NODE_ENV !== 'production') {\n          if (process.env.NODE_ENV !== 'test' && !warnedOnceTabPresent && tabMeta && tabMeta.width === 0 && tabMeta.height === 0 &&\n          // if the whole Tabs component is hidden, don't warn\n          tabsMeta.clientWidth !== 0) {\n            tabsMeta = null;\n            console.error(['MUI: The `value` provided to the Tabs component is invalid.', `The Tab with this \\`value\\` (\"${value}\") is not part of the document layout.`, \"Make sure the tab item is present in the document or that it's not `display: none`.\"].join('\\n'));\n            warnedOnceTabPresent = true;\n          }\n        }\n      }\n    }\n    return {\n      tabsMeta,\n      tabMeta\n    };\n  };\n  const updateIndicatorState = (0, _useEventCallback.default)(() => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    let startValue = 0;\n    let startIndicator;\n    if (vertical) {\n      startIndicator = 'top';\n      if (tabMeta && tabsMeta) {\n        startValue = tabMeta.top - tabsMeta.top + tabsMeta.scrollTop;\n      }\n    } else {\n      startIndicator = isRtl ? 'right' : 'left';\n      if (tabMeta && tabsMeta) {\n        startValue = (isRtl ? -1 : 1) * (tabMeta[startIndicator] - tabsMeta[startIndicator] + tabsMeta.scrollLeft);\n      }\n    }\n    const newIndicatorStyle = {\n      [startIndicator]: startValue,\n      // May be wrong until the font is loaded.\n      [size]: tabMeta ? tabMeta[size] : 0\n    };\n    if (typeof indicatorStyle[startIndicator] !== 'number' || typeof indicatorStyle[size] !== 'number') {\n      setIndicatorStyle(newIndicatorStyle);\n    } else {\n      const dStart = Math.abs(indicatorStyle[startIndicator] - newIndicatorStyle[startIndicator]);\n      const dSize = Math.abs(indicatorStyle[size] - newIndicatorStyle[size]);\n      if (dStart >= 1 || dSize >= 1) {\n        setIndicatorStyle(newIndicatorStyle);\n      }\n    }\n  });\n  const scroll = (scrollValue, {\n    animation = true\n  } = {}) => {\n    if (animation) {\n      (0, _animate.default)(scrollStart, tabsRef.current, scrollValue, {\n        duration: theme.transitions.duration.standard\n      });\n    } else {\n      tabsRef.current[scrollStart] = scrollValue;\n    }\n  };\n  const moveTabsScroll = delta => {\n    let scrollValue = tabsRef.current[scrollStart];\n    if (vertical) {\n      scrollValue += delta;\n    } else {\n      scrollValue += delta * (isRtl ? -1 : 1);\n    }\n    scroll(scrollValue);\n  };\n  const getScrollSize = () => {\n    const containerSize = tabsRef.current[clientSize];\n    let totalSize = 0;\n    const children = Array.from(tabListRef.current.children);\n    for (let i = 0; i < children.length; i += 1) {\n      const tab = children[i];\n      if (totalSize + tab[clientSize] > containerSize) {\n        // If the first item is longer than the container size, then only scroll\n        // by the container size.\n        if (i === 0) {\n          totalSize = containerSize;\n        }\n        break;\n      }\n      totalSize += tab[clientSize];\n    }\n    return totalSize;\n  };\n  const handleStartScrollClick = () => {\n    moveTabsScroll(-1 * getScrollSize());\n  };\n  const handleEndScrollClick = () => {\n    moveTabsScroll(getScrollSize());\n  };\n  const [ScrollbarSlot, {\n    onChange: scrollbarOnChange,\n    ...scrollbarSlotProps\n  }] = (0, _useSlot.default)('scrollbar', {\n    className: (0, _clsx.default)(classes.scrollableX, classes.hideScrollbar),\n    elementType: TabsScrollbarSize,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // TODO Remove <ScrollbarSize /> as browser support for hiding the scrollbar\n  // with CSS improves.\n  const handleScrollbarSizeChange = React.useCallback(scrollbarWidth => {\n    scrollbarOnChange?.(scrollbarWidth);\n    setScrollerStyle({\n      overflow: null,\n      scrollbarWidth\n    });\n  }, [scrollbarOnChange]);\n  const [ScrollButtonsSlot, scrollButtonSlotProps] = (0, _useSlot.default)('scrollButtons', {\n    className: (0, _clsx.default)(classes.scrollButtons, TabScrollButtonProps.className),\n    elementType: _TabScrollButton.default,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      orientation,\n      slots: {\n        StartScrollButtonIcon: slots.startScrollButtonIcon || slots.StartScrollButtonIcon,\n        EndScrollButtonIcon: slots.endScrollButtonIcon || slots.EndScrollButtonIcon\n      },\n      slotProps: {\n        startScrollButtonIcon: startScrollButtonIconProps,\n        endScrollButtonIcon: endScrollButtonIconProps\n      }\n    }\n  });\n  const getConditionalElements = () => {\n    const conditionalElements = {};\n    conditionalElements.scrollbarSizeListener = scrollable ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScrollbarSlot, {\n      ...scrollbarSlotProps,\n      onChange: handleScrollbarSizeChange\n    }) : null;\n    const scrollButtonsActive = displayStartScroll || displayEndScroll;\n    const showScrollButtons = scrollable && (scrollButtons === 'auto' && scrollButtonsActive || scrollButtons === true);\n    conditionalElements.scrollButtonStart = showScrollButtons ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScrollButtonsSlot, {\n      direction: isRtl ? 'right' : 'left',\n      onClick: handleStartScrollClick,\n      disabled: !displayStartScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    conditionalElements.scrollButtonEnd = showScrollButtons ? /*#__PURE__*/(0, _jsxRuntime.jsx)(ScrollButtonsSlot, {\n      direction: isRtl ? 'left' : 'right',\n      onClick: handleEndScrollClick,\n      disabled: !displayEndScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    return conditionalElements;\n  };\n  const scrollSelectedIntoView = (0, _useEventCallback.default)(animation => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    if (!tabMeta || !tabsMeta) {\n      return;\n    }\n    if (tabMeta[start] < tabsMeta[start]) {\n      // left side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[start] - tabsMeta[start]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    } else if (tabMeta[end] > tabsMeta[end]) {\n      // right side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[end] - tabsMeta[end]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    }\n  });\n  const updateScrollButtonState = (0, _useEventCallback.default)(() => {\n    if (scrollable && scrollButtons !== false) {\n      setUpdateScrollObserver(!updateScrollObserver);\n    }\n  });\n  React.useEffect(() => {\n    const handleResize = (0, _debounce.default)(() => {\n      // If the Tabs component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/33276\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n      if (tabsRef.current) {\n        updateIndicatorState();\n      }\n    });\n    let resizeObserver;\n\n    /**\n     * @type {MutationCallback}\n     */\n    const handleMutation = records => {\n      records.forEach(record => {\n        record.removedNodes.forEach(item => {\n          resizeObserver?.unobserve(item);\n        });\n        record.addedNodes.forEach(item => {\n          resizeObserver?.observe(item);\n        });\n      });\n      handleResize();\n      updateScrollButtonState();\n    };\n    const win = (0, _ownerWindow.default)(tabsRef.current);\n    win.addEventListener('resize', handleResize);\n    let mutationObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      Array.from(tabListRef.current.children).forEach(child => {\n        resizeObserver.observe(child);\n      });\n    }\n    if (typeof MutationObserver !== 'undefined') {\n      mutationObserver = new MutationObserver(handleMutation);\n      mutationObserver.observe(tabListRef.current, {\n        childList: true\n      });\n    }\n    return () => {\n      handleResize.clear();\n      win.removeEventListener('resize', handleResize);\n      mutationObserver?.disconnect();\n      resizeObserver?.disconnect();\n    };\n  }, [updateIndicatorState, updateScrollButtonState]);\n\n  /**\n   * Toggle visibility of start and end scroll buttons\n   * Using IntersectionObserver on first and last Tabs.\n   */\n  React.useEffect(() => {\n    const tabListChildren = Array.from(tabListRef.current.children);\n    const length = tabListChildren.length;\n    if (typeof IntersectionObserver !== 'undefined' && length > 0 && scrollable && scrollButtons !== false) {\n      const firstTab = tabListChildren[0];\n      const lastTab = tabListChildren[length - 1];\n      const observerOptions = {\n        root: tabsRef.current,\n        threshold: 0.99\n      };\n      const handleScrollButtonStart = entries => {\n        setDisplayStartScroll(!entries[0].isIntersecting);\n      };\n      const firstObserver = new IntersectionObserver(handleScrollButtonStart, observerOptions);\n      firstObserver.observe(firstTab);\n      const handleScrollButtonEnd = entries => {\n        setDisplayEndScroll(!entries[0].isIntersecting);\n      };\n      const lastObserver = new IntersectionObserver(handleScrollButtonEnd, observerOptions);\n      lastObserver.observe(lastTab);\n      return () => {\n        firstObserver.disconnect();\n        lastObserver.disconnect();\n      };\n    }\n    return undefined;\n  }, [scrollable, scrollButtons, updateScrollObserver, childrenProp?.length]);\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    updateIndicatorState();\n  });\n  React.useEffect(() => {\n    // Don't animate on the first render.\n    scrollSelectedIntoView(defaultIndicatorStyle !== indicatorStyle);\n  }, [scrollSelectedIntoView, indicatorStyle]);\n  React.useImperativeHandle(action, () => ({\n    updateIndicator: updateIndicatorState,\n    updateScrollButtons: updateScrollButtonState\n  }), [updateIndicatorState, updateScrollButtonState]);\n  const [IndicatorSlot, indicatorSlotProps] = (0, _useSlot.default)('indicator', {\n    className: (0, _clsx.default)(classes.indicator, TabIndicatorProps.className),\n    elementType: TabsIndicator,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: indicatorStyle\n    }\n  });\n  const indicator = /*#__PURE__*/(0, _jsxRuntime.jsx)(IndicatorSlot, {\n    ...indicatorSlotProps\n  });\n  let childIndex = 0;\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if ((0, _reactIs.isFragment)(child)) {\n        console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    const childValue = child.props.value === undefined ? childIndex : child.props.value;\n    valueToIndex.set(childValue, childIndex);\n    const selected = childValue === value;\n    childIndex += 1;\n    return /*#__PURE__*/React.cloneElement(child, {\n      fullWidth: variant === 'fullWidth',\n      indicator: selected && !mounted && indicator,\n      selected,\n      selectionFollowsFocus,\n      onChange,\n      textColor,\n      value: childValue,\n      ...(childIndex === 1 && value === false && !child.props.tabIndex ? {\n        tabIndex: 0\n      } : {})\n    });\n  });\n  const handleKeyDown = event => {\n    // Check if a modifier key (Alt, Shift, Ctrl, Meta) is pressed\n    if (event.altKey || event.shiftKey || event.ctrlKey || event.metaKey) {\n      return;\n    }\n    const list = tabListRef.current;\n    const currentFocus = (0, _ownerDocument.default)(list).activeElement;\n    // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n    const role = currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const conditionalElements = getConditionalElements();\n  const [RootSlot, rootSlotProps] = (0, _useSlot.default)('root', {\n    ref,\n    className: (0, _clsx.default)(classes.root, className),\n    elementType: TabsRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    ownerState\n  });\n  const [ScrollerSlot, scrollerSlotProps] = (0, _useSlot.default)('scroller', {\n    ref: tabsRef,\n    className: classes.scroller,\n    elementType: TabsScroller,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: {\n        overflow: scrollerStyle.overflow,\n        [vertical ? `margin${isRtl ? 'Left' : 'Right'}` : 'marginBottom']: visibleScrollbar ? undefined : -scrollerStyle.scrollbarWidth\n      }\n    }\n  });\n  const [ListSlot, listSlotProps] = (0, _useSlot.default)('list', {\n    ref: tabListRef,\n    className: (0, _clsx.default)(classes.list, classes.flexContainer),\n    elementType: List,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onKeyDown: event => {\n        handleKeyDown(event);\n        handlers.onKeyDown?.(event);\n      }\n    })\n  });\n  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(RootSlot, {\n    ...rootSlotProps,\n    children: [conditionalElements.scrollButtonStart, conditionalElements.scrollbarSizeListener, /*#__PURE__*/(0, _jsxRuntime.jsxs)(ScrollerSlot, {\n      ...scrollerSlotProps,\n      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(ListSlot, {\n        \"aria-label\": ariaLabel,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-orientation\": orientation === 'vertical' ? 'vertical' : null,\n        role: \"tablist\",\n        ...listSlotProps,\n        children: children\n      }), mounted && indicator]\n    }), conditionalElements.scrollButtonEnd]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Callback fired when the component mounts.\n   * This is useful when you want to trigger an action programmatically.\n   * It supports two actions: `updateIndicator()` and `updateScrollButtons()`\n   *\n   * @param {object} actions This object contains all possible actions\n   * that can be triggered programmatically.\n   */\n  action: _refType.default,\n  /**\n   * If `true`, the scroll buttons aren't forced hidden on mobile.\n   * By default the scroll buttons are hidden on mobile and takes precedence over `scrollButtons`.\n   * @default false\n   */\n  allowScrollButtonsMobile: _propTypes.default.bool,\n  /**\n   * The label for the Tabs as a string.\n   */\n  'aria-label': _propTypes.default.string,\n  /**\n   * An id or list of ids separated by a space that label the Tabs.\n   */\n  'aria-labelledby': _propTypes.default.string,\n  /**\n   * If `true`, the tabs are centered.\n   * This prop is intended for large views.\n   * @default false\n   */\n  centered: _propTypes.default.bool,\n  /**\n   * The content of the component.\n   */\n  children: _propTypes.default.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: _propTypes.default.object,\n  /**\n   * @ignore\n   */\n  className: _propTypes.default.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: _propTypes.default.elementType,\n  /**\n   * Determines the color of the indicator.\n   * @default 'primary'\n   */\n  indicatorColor: _propTypes.default /* @typescript-to-proptypes-ignore */.oneOfType([_propTypes.default.oneOf(['primary', 'secondary']), _propTypes.default.string]),\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child (number)\n   */\n  onChange: _propTypes.default.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: _propTypes.default.oneOf(['horizontal', 'vertical']),\n  /**\n   * The component used to render the scroll buttons.\n   * @deprecated use the `slots.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default TabScrollButton\n   */\n  ScrollButtonComponent: _propTypes.default.elementType,\n  /**\n   * Determine behavior of scroll buttons when tabs are set to scroll:\n   *\n   * - `auto` will only present them when not all the items are visible.\n   * - `true` will always present them.\n   * - `false` will never present them.\n   *\n   * By default the scroll buttons are hidden on mobile.\n   * This behavior can be disabled with `allowScrollButtonsMobile`.\n   * @default 'auto'\n   */\n  scrollButtons: _propTypes.default /* @typescript-to-proptypes-ignore */.oneOf(['auto', false, true]),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: _propTypes.default.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: _propTypes.default.shape({\n    endScrollButtonIcon: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    indicator: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    list: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    root: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    scrollbar: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    scrollButtons: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    scroller: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),\n    startScrollButtonIcon: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: _propTypes.default.shape({\n    endScrollButtonIcon: _propTypes.default.elementType,\n    EndScrollButtonIcon: _propTypes.default.elementType,\n    indicator: _propTypes.default.elementType,\n    list: _propTypes.default.elementType,\n    root: _propTypes.default.elementType,\n    scrollbar: _propTypes.default.elementType,\n    scrollButtons: _propTypes.default.elementType,\n    scroller: _propTypes.default.elementType,\n    startScrollButtonIcon: _propTypes.default.elementType,\n    StartScrollButtonIcon: _propTypes.default.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),\n  /**\n   * Props applied to the tab indicator element.\n   * @deprecated use the `slotProps.indicator` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default  {}\n   */\n  TabIndicatorProps: _propTypes.default.object,\n  /**\n   * Props applied to the [`TabScrollButton`](https://mui.com/material-ui/api/tab-scroll-button/) element.\n   * @deprecated use the `slotProps.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TabScrollButtonProps: _propTypes.default.object,\n  /**\n   * Determines the color of the `Tab`.\n   * @default 'primary'\n   */\n  textColor: _propTypes.default.oneOf(['inherit', 'primary', 'secondary']),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: _propTypes.default.any,\n  /**\n   * Determines additional display behavior of the tabs:\n   *\n   *  - `scrollable` will invoke scrolling properties and allow for horizontally\n   *  scrolling (or swiping) of the tab bar.\n   *  - `fullWidth` will make the tabs grow to use all the available space,\n   *  which should be used for small views, like on mobile.\n   *  - `standard` will render the default state.\n   * @default 'standard'\n   */\n  variant: _propTypes.default.oneOf(['fullWidth', 'scrollable', 'standard']),\n  /**\n   * If `true`, the scrollbar is visible. It can be useful when displaying\n   * a long vertical list of tabs.\n   * @default false\n   */\n  visibleScrollbar: _propTypes.default.bool\n} : void 0;\nvar _default = exports.default = Tabs;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACJ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIM,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,QAAQ,GAAGR,OAAO,CAAC,UAAU,CAAC;AAClC,IAAIS,UAAU,GAAGV,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIU,KAAK,GAAGX,sBAAsB,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,IAAIW,QAAQ,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACpE,IAAIY,eAAe,GAAGb,sBAAsB,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAClF,IAAIa,YAAY,GAAGb,OAAO,CAAC,yBAAyB,CAAC;AACrD,IAAIc,aAAa,GAAGf,sBAAsB,CAACC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAC9E,IAAIe,WAAW,GAAGf,OAAO,CAAC,gBAAgB,CAAC;AAC3C,IAAIgB,UAAU,GAAGjB,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIiB,qBAAqB,GAAGjB,OAAO,CAAC,yBAAyB,CAAC;AAC9D,IAAIkB,SAAS,GAAGnB,sBAAsB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACpE,IAAImB,QAAQ,GAAGpB,sBAAsB,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACrE,IAAIoB,cAAc,GAAGrB,sBAAsB,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AACvE,IAAIqB,gBAAgB,GAAGtB,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC5E,IAAIsB,iBAAiB,GAAGvB,sBAAsB,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACpF,IAAIuB,YAAY,GAAGrB,uBAAuB,CAACF,OAAO,CAAC,eAAe,CAAC,CAAC;AACpE,IAAIwB,cAAc,GAAGzB,sBAAsB,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAAC;AAC9E,IAAIyB,YAAY,GAAG1B,sBAAsB,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAC1E,IAAI0B,QAAQ,GAAG3B,sBAAsB,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAClE,IAAI2B,WAAW,GAAG3B,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAM4B,QAAQ,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC/B,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACE,UAAU;EACxB;EACA,IAAID,IAAI,IAAIA,IAAI,CAACE,kBAAkB,EAAE;IACnC,OAAOF,IAAI,CAACE,kBAAkB;EAChC;EACA,OAAOH,IAAI,CAACE,UAAU;AACxB,CAAC;AACD,MAAME,YAAY,GAAGA,CAACJ,IAAI,EAAEC,IAAI,KAAK;EACnC,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACK,SAAS;EACvB;EACA,IAAIJ,IAAI,IAAIA,IAAI,CAACK,sBAAsB,EAAE;IACvC,OAAOL,IAAI,CAACK,sBAAsB;EACpC;EACA,OAAON,IAAI,CAACK,SAAS;AACvB,CAAC;AACD,MAAME,SAAS,GAAGA,CAACP,IAAI,EAAEQ,YAAY,EAAEC,iBAAiB,KAAK;EAC3D,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEQ,YAAY,CAAC;EACrD,OAAOG,SAAS,EAAE;IAChB;IACA,IAAIA,SAAS,KAAKX,IAAI,CAACE,UAAU,EAAE;MACjC,IAAIQ,WAAW,EAAE;QACf;MACF;MACAA,WAAW,GAAG,IAAI;IACpB;;IAEA;IACA,MAAME,iBAAiB,GAAGD,SAAS,CAACE,QAAQ,IAAIF,SAAS,CAACG,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;IAClG,IAAI,CAACH,SAAS,CAACI,YAAY,CAAC,UAAU,CAAC,IAAIH,iBAAiB,EAAE;MAC5D;MACAD,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEW,SAAS,CAAC;IAChD,CAAC,MAAM;MACLA,SAAS,CAACK,KAAK,CAAC,CAAC;MACjB;IACF;EACF;AACF,CAAC;AACD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC,KAAK;IACLC,aAAa;IACbC,WAAW;IACXC,WAAW;IACXC,QAAQ;IACRC,uBAAuB;IACvBC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,QAAQ,IAAI,UAAU,CAAC;IACtCU,QAAQ,EAAE,CAAC,UAAU,EAAET,KAAK,IAAI,OAAO,EAAEC,aAAa,IAAI,eAAe,EAAEC,WAAW,IAAI,aAAa,EAAEC,WAAW,IAAI,aAAa,CAAC;IACtIvB,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAEmB,QAAQ,IAAI,uBAAuB,EAAEA,QAAQ,IAAI,UAAU,EAAEK,QAAQ,IAAI,UAAU,CAAC;IACpHM,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,aAAa,EAAE,CAAC,eAAe,EAAEN,uBAAuB,IAAI,yBAAyB,CAAC;IACtFH,WAAW,EAAE,CAACA,WAAW,IAAI,aAAa,CAAC;IAC3CD,aAAa,EAAE,CAACA,aAAa,IAAI,eAAe;EAClD,CAAC;EACD,OAAO,CAAC,CAAC,EAAEtC,eAAe,CAACX,OAAO,EAAEuD,KAAK,EAAEjC,YAAY,CAACsC,mBAAmB,EAAEN,OAAO,CAAC;AACvF,CAAC;AACD,MAAMO,QAAQ,GAAG,CAAC,CAAC,EAAE/C,WAAW,CAACgD,MAAM,EAAE,KAAK,EAAE;EAC9CC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJrB;IACF,CAAC,GAAGoB,KAAK;IACT,OAAO,CAAC;MACN,CAAC,MAAM5C,YAAY,CAACtB,OAAO,CAAC2D,aAAa,EAAE,GAAGQ,MAAM,CAACR;IACvD,CAAC,EAAE;MACD,CAAC,MAAMrC,YAAY,CAACtB,OAAO,CAAC2D,aAAa,EAAE,GAAGb,UAAU,CAACO,uBAAuB,IAAIc,MAAM,CAACd;IAC7F,CAAC,EAAEc,MAAM,CAACX,IAAI,EAAEV,UAAU,CAACC,QAAQ,IAAIoB,MAAM,CAACpB,QAAQ,CAAC;EACzD;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEhC,UAAU,CAACf,OAAO,EAAE,CAAC;EAC1BoE;AACF,CAAC,MAAM;EACLC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,EAAE;EACb;EACAC,uBAAuB,EAAE,OAAO;EAChCC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACC,QAAQ;IACzB2B,KAAK,EAAE;MACLC,aAAa,EAAE;IACjB;EACF,CAAC,EAAE;IACDT,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACO,uBAAuB;IACxCqB,KAAK,EAAE;MACL,CAAC,MAAMpD,YAAY,CAACtB,OAAO,CAAC2D,aAAa,EAAE,GAAG;QAC5C,CAACS,KAAK,CAACQ,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;UAC9BL,OAAO,EAAE;QACX;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMM,YAAY,GAAG,CAAC,CAAC,EAAEhE,WAAW,CAACgD,MAAM,EAAE,KAAK,EAAE;EAClDC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJrB;IACF,CAAC,GAAGoB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,QAAQ,EAAEX,UAAU,CAACE,KAAK,IAAImB,MAAM,CAACnB,KAAK,EAAEF,UAAU,CAACG,aAAa,IAAIkB,MAAM,CAAClB,aAAa,EAAEH,UAAU,CAACI,WAAW,IAAIiB,MAAM,CAACjB,WAAW,EAAEJ,UAAU,CAACK,WAAW,IAAIgB,MAAM,CAAChB,WAAW,CAAC;EAC1M;AACF,CAAC,CAAC,CAAC;EACD4B,QAAQ,EAAE,UAAU;EACpBP,OAAO,EAAE,cAAc;EACvBQ,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE,QAAQ;EACpBR,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACE,KAAK;IACtB0B,KAAK,EAAE;MACLQ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT;EACF,CAAC,EAAE;IACDjB,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACG,aAAa;IAC9ByB,KAAK,EAAE;MACL;MACAU,cAAc,EAAE,MAAM;MACtB;MACA,sBAAsB,EAAE;QACtBZ,OAAO,EAAE,MAAM,CAAC;MAClB;IACF;EACF,CAAC,EAAE;IACDN,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACI,WAAW;IAC5BwB,KAAK,EAAE;MACLQ,SAAS,EAAE,MAAM;MACjBG,SAAS,EAAE;IACb;EACF,CAAC,EAAE;IACDnB,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACK,WAAW;IAC5BuB,KAAK,EAAE;MACLW,SAAS,EAAE,MAAM;MACjBH,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMI,IAAI,GAAG,CAAC,CAAC,EAAExE,WAAW,CAACgD,MAAM,EAAE,KAAK,EAAE;EAC1CC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJrB;IACF,CAAC,GAAGoB,KAAK;IACT,OAAO,CAACC,MAAM,CAACvC,IAAI,EAAEuC,MAAM,CAACoB,aAAa,EAAEzC,UAAU,CAACC,QAAQ,IAAIoB,MAAM,CAACqB,qBAAqB,EAAE1C,UAAU,CAACM,QAAQ,IAAIe,MAAM,CAACf,QAAQ,CAAC;EACzI;AACF,CAAC,CAAC,CAAC;EACDoB,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACC,QAAQ;IACzB2B,KAAK,EAAE;MACLC,aAAa,EAAE;IACjB;EACF,CAAC,EAAE;IACDT,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACM,QAAQ;IACzBsB,KAAK,EAAE;MACLe,cAAc,EAAE;IAClB;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,aAAa,GAAG,CAAC,CAAC,EAAE5E,WAAW,CAACgD,MAAM,EAAE,MAAM,EAAE;EACpDC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjD,UAAU,CAACf,OAAO,EAAE,CAAC;EAC1BoE;AACF,CAAC,MAAM;EACLW,QAAQ,EAAE,UAAU;EACpBY,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTT,KAAK,EAAE,MAAM;EACbU,UAAU,EAAEzB,KAAK,CAAC0B,WAAW,CAACC,MAAM,CAAC,CAAC;EACtCtB,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAE;MACL8B,cAAc,EAAE;IAClB,CAAC;IACDtB,KAAK,EAAE;MACLuB,eAAe,EAAE,CAAC7B,KAAK,CAAC8B,IAAI,IAAI9B,KAAK,EAAE+B,OAAO,CAACC,OAAO,CAACC;IACzD;EACF,CAAC,EAAE;IACDnC,KAAK,EAAE;MACL8B,cAAc,EAAE;IAClB,CAAC;IACDtB,KAAK,EAAE;MACLuB,eAAe,EAAE,CAAC7B,KAAK,CAAC8B,IAAI,IAAI9B,KAAK,EAAE+B,OAAO,CAACG,SAAS,CAACD;IAC3D;EACF,CAAC,EAAE;IACDnC,KAAK,EAAEA,CAAC;MACNpB;IACF,CAAC,KAAKA,UAAU,CAACC,QAAQ;IACzB2B,KAAK,EAAE;MACLiB,MAAM,EAAE,MAAM;MACdR,KAAK,EAAE,CAAC;MACRoB,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,iBAAiB,GAAG,CAAC,CAAC,EAAE1F,WAAW,CAACgD,MAAM,EAAE3C,cAAc,CAACnB,OAAO,CAAC,CAAC;EACxEkF,SAAS,EAAE,MAAM;EACjBG,SAAS,EAAE,QAAQ;EACnB;EACAD,cAAc,EAAE,MAAM;EACtB;EACA,sBAAsB,EAAE;IACtBZ,OAAO,EAAE,MAAM,CAAC;EAClB;AACF,CAAC,CAAC;AACF,MAAMiC,qBAAqB,GAAG,CAAC,CAAC;AAChC,IAAIC,oBAAoB,GAAG,KAAK;AAChC,MAAMC,IAAI,GAAG,aAAarG,KAAK,CAACsG,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAM5C,KAAK,GAAG,CAAC,CAAC,EAAElD,qBAAqB,CAAC+F,eAAe,EAAE;IACvD7C,KAAK,EAAE2C,OAAO;IACd9C,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMK,KAAK,GAAG,CAAC,CAAC,EAAEtD,WAAW,CAACkG,QAAQ,EAAE,CAAC;EACzC,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAErG,YAAY,CAACsG,MAAM,EAAE,CAAC;EACxC,MAAM;IACJ,YAAY,EAAEC,SAAS;IACvB,iBAAiB,EAAEC,cAAc;IACjCC,MAAM;IACNjE,QAAQ,GAAG,KAAK;IAChBkE,QAAQ,EAAEC,YAAY;IACtBC,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBC,wBAAwB,GAAG,KAAK;IAChC1B,cAAc,GAAG,SAAS;IAC1B2B,QAAQ;IACRC,WAAW,GAAG,YAAY;IAC1BC,qBAAqB;IACrB;IACAlE,aAAa,GAAG,MAAM;IACtBmE,qBAAqB;IACrBvE,KAAK,GAAG,CAAC,CAAC;IACVwE,SAAS,GAAG,CAAC,CAAC;IACdC,iBAAiB,GAAG,CAAC,CAAC;IACtB;IACAC,oBAAoB,GAAG,CAAC,CAAC;IACzB;IACAC,SAAS,GAAG,SAAS;IACrB7H,KAAK;IACL8H,OAAO,GAAG,UAAU;IACpBC,gBAAgB,GAAG,KAAK;IACxB,GAAGC;EACL,CAAC,GAAGnE,KAAK;EACT,MAAMoE,UAAU,GAAGH,OAAO,KAAK,YAAY;EAC3C,MAAMpF,QAAQ,GAAG6E,WAAW,KAAK,UAAU;EAC3C,MAAMW,WAAW,GAAGxF,QAAQ,GAAG,WAAW,GAAG,YAAY;EACzD,MAAMyF,KAAK,GAAGzF,QAAQ,GAAG,KAAK,GAAG,MAAM;EACvC,MAAM0F,GAAG,GAAG1F,QAAQ,GAAG,QAAQ,GAAG,OAAO;EACzC,MAAM2F,UAAU,GAAG3F,QAAQ,GAAG,cAAc,GAAG,aAAa;EAC5D,MAAM4F,IAAI,GAAG5F,QAAQ,GAAG,QAAQ,GAAG,OAAO;EAC1C,MAAMD,UAAU,GAAG;IACjB,GAAGoB,KAAK;IACRuD,SAAS;IACTC,wBAAwB;IACxB1B,cAAc;IACd4B,WAAW;IACX7E,QAAQ;IACRY,aAAa;IACbuE,SAAS;IACTC,OAAO;IACPC,gBAAgB;IAChBpF,KAAK,EAAE,CAACsF,UAAU;IAClBrF,aAAa,EAAEqF,UAAU,IAAI,CAACF,gBAAgB;IAC9ClF,WAAW,EAAEoF,UAAU,IAAI,CAACvF,QAAQ;IACpCI,WAAW,EAAEmF,UAAU,IAAIvF,QAAQ;IACnCK,QAAQ,EAAEA,QAAQ,IAAI,CAACkF,UAAU;IACjCjF,uBAAuB,EAAE,CAACqE;EAC5B,CAAC;EACD,MAAMpE,OAAO,GAAGT,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM8F,0BAA0B,GAAG,CAAC,CAAC,EAAE/H,aAAa,CAACb,OAAO,EAAE;IAC5D6I,WAAW,EAAEtF,KAAK,CAACuF,qBAAqB;IACxCC,iBAAiB,EAAEhB,SAAS,CAACiB,qBAAqB;IAClDlG;EACF,CAAC,CAAC;EACF,MAAMmG,wBAAwB,GAAG,CAAC,CAAC,EAAEpI,aAAa,CAACb,OAAO,EAAE;IAC1D6I,WAAW,EAAEtF,KAAK,CAAC2F,mBAAmB;IACtCH,iBAAiB,EAAEhB,SAAS,CAACoB,mBAAmB;IAChDrG;EACF,CAAC,CAAC;EACF,IAAIsG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIlG,QAAQ,IAAIkF,UAAU,EAAE;MAC1BiB,OAAO,CAACC,KAAK,CAAC,mFAAmF,GAAG,yCAAyC,CAAC;IAChJ;EACF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpJ,KAAK,CAACqJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvJ,KAAK,CAACqJ,QAAQ,CAAClD,qBAAqB,CAAC;EACjF,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzJ,KAAK,CAACqJ,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3J,KAAK,CAACqJ,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACO,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7J,KAAK,CAACqJ,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAG/J,KAAK,CAACqJ,QAAQ,CAAC;IACvDtF,QAAQ,EAAE,QAAQ;IAClBe,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAMkF,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B,MAAMC,OAAO,GAAGlK,KAAK,CAACmK,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,UAAU,GAAGpK,KAAK,CAACmK,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,sBAAsB,GAAG;IAC7BpH,KAAK;IACLwE,SAAS,EAAE;MACTrE,SAAS,EAAEsE,iBAAiB;MAC5B4C,YAAY,EAAE3C,oBAAoB;MAClC,GAAGF;IACL;EACF,CAAC;EACD,MAAM8C,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAGN,OAAO,CAACO,OAAO;IAChC,IAAIC,QAAQ;IACZ,IAAIF,QAAQ,EAAE;MACZ,MAAMG,IAAI,GAAGH,QAAQ,CAACI,qBAAqB,CAAC,CAAC;MAC7C;MACAF,QAAQ,GAAG;QACTG,WAAW,EAAEL,QAAQ,CAACK,WAAW;QACjCC,UAAU,EAAEN,QAAQ,CAACM,UAAU;QAC/BC,SAAS,EAAEP,QAAQ,CAACO,SAAS;QAC7BC,WAAW,EAAER,QAAQ,CAACQ,WAAW;QACjCC,GAAG,EAAEN,IAAI,CAACM,GAAG;QACb3F,MAAM,EAAEqF,IAAI,CAACrF,MAAM;QACnB4F,IAAI,EAAEP,IAAI,CAACO,IAAI;QACfjF,KAAK,EAAE0E,IAAI,CAAC1E;MACd,CAAC;IACH;IACA,IAAIkF,OAAO;IACX,IAAIX,QAAQ,IAAIzK,KAAK,KAAK,KAAK,EAAE;MAC/B,MAAMiH,QAAQ,GAAGoD,UAAU,CAACK,OAAO,CAACzD,QAAQ;MAC5C,IAAIA,QAAQ,CAACoE,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMC,GAAG,GAAGrE,QAAQ,CAACgD,YAAY,CAACsB,GAAG,CAACvL,KAAK,CAAC,CAAC;QAC7C,IAAI+I,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAI,CAACqC,GAAG,EAAE;YACRpC,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,0CAA0CnJ,KAAK,IAAI,EAAEiK,YAAY,CAACuB,IAAI,GAAG,gDAAgDC,KAAK,CAACC,IAAI,CAACzB,YAAY,CAACuB,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;UAC5Q;QACF;QACAP,OAAO,GAAGE,GAAG,GAAGA,GAAG,CAACT,qBAAqB,CAAC,CAAC,GAAG,IAAI;QAClD,IAAI9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAI,CAAC5C,oBAAoB,IAAI+E,OAAO,IAAIA,OAAO,CAACtG,KAAK,KAAK,CAAC,IAAIsG,OAAO,CAAC9F,MAAM,KAAK,CAAC;UACtH;UACAqF,QAAQ,CAACG,WAAW,KAAK,CAAC,EAAE;YAC1BH,QAAQ,GAAG,IAAI;YACfzB,OAAO,CAACC,KAAK,CAAC,CAAC,6DAA6D,EAAE,iCAAiCnJ,KAAK,wCAAwC,EAAE,qFAAqF,CAAC,CAAC2L,IAAI,CAAC,IAAI,CAAC,CAAC;YAChQtF,oBAAoB,GAAG,IAAI;UAC7B;QACF;MACF;IACF;IACA,OAAO;MACLsE,QAAQ;MACRS;IACF,CAAC;EACH,CAAC;EACD,MAAMQ,oBAAoB,GAAG,CAAC,CAAC,EAAE5K,iBAAiB,CAACrB,OAAO,EAAE,MAAM;IAChE,MAAM;MACJgL,QAAQ;MACRS;IACF,CAAC,GAAGZ,WAAW,CAAC,CAAC;IACjB,IAAIqB,UAAU,GAAG,CAAC;IAClB,IAAIC,cAAc;IAClB,IAAIpJ,QAAQ,EAAE;MACZoJ,cAAc,GAAG,KAAK;MACtB,IAAIV,OAAO,IAAIT,QAAQ,EAAE;QACvBkB,UAAU,GAAGT,OAAO,CAACF,GAAG,GAAGP,QAAQ,CAACO,GAAG,GAAGP,QAAQ,CAACK,SAAS;MAC9D;IACF,CAAC,MAAM;MACLc,cAAc,GAAGlF,KAAK,GAAG,OAAO,GAAG,MAAM;MACzC,IAAIwE,OAAO,IAAIT,QAAQ,EAAE;QACvBkB,UAAU,GAAG,CAACjF,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAKwE,OAAO,CAACU,cAAc,CAAC,GAAGnB,QAAQ,CAACmB,cAAc,CAAC,GAAGnB,QAAQ,CAACI,UAAU,CAAC;MAC5G;IACF;IACA,MAAMgB,iBAAiB,GAAG;MACxB,CAACD,cAAc,GAAGD,UAAU;MAC5B;MACA,CAACvD,IAAI,GAAG8C,OAAO,GAAGA,OAAO,CAAC9C,IAAI,CAAC,GAAG;IACpC,CAAC;IACD,IAAI,OAAOiB,cAAc,CAACuC,cAAc,CAAC,KAAK,QAAQ,IAAI,OAAOvC,cAAc,CAACjB,IAAI,CAAC,KAAK,QAAQ,EAAE;MAClGkB,iBAAiB,CAACuC,iBAAiB,CAAC;IACtC,CAAC,MAAM;MACL,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC3C,cAAc,CAACuC,cAAc,CAAC,GAAGC,iBAAiB,CAACD,cAAc,CAAC,CAAC;MAC3F,MAAMK,KAAK,GAAGF,IAAI,CAACC,GAAG,CAAC3C,cAAc,CAACjB,IAAI,CAAC,GAAGyD,iBAAiB,CAACzD,IAAI,CAAC,CAAC;MACtE,IAAI0D,MAAM,IAAI,CAAC,IAAIG,KAAK,IAAI,CAAC,EAAE;QAC7B3C,iBAAiB,CAACuC,iBAAiB,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EACF,MAAMK,MAAM,GAAGA,CAACC,WAAW,EAAE;IAC3BC,SAAS,GAAG;EACd,CAAC,GAAG,CAAC,CAAC,KAAK;IACT,IAAIA,SAAS,EAAE;MACb,CAAC,CAAC,EAAEzL,QAAQ,CAAClB,OAAO,EAAEuI,WAAW,EAAEiC,OAAO,CAACO,OAAO,EAAE2B,WAAW,EAAE;QAC/DE,QAAQ,EAAExI,KAAK,CAAC0B,WAAW,CAAC8G,QAAQ,CAACC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLrC,OAAO,CAACO,OAAO,CAACxC,WAAW,CAAC,GAAGmE,WAAW;IAC5C;EACF,CAAC;EACD,MAAMI,cAAc,GAAGC,KAAK,IAAI;IAC9B,IAAIL,WAAW,GAAGlC,OAAO,CAACO,OAAO,CAACxC,WAAW,CAAC;IAC9C,IAAIxF,QAAQ,EAAE;MACZ2J,WAAW,IAAIK,KAAK;IACtB,CAAC,MAAM;MACLL,WAAW,IAAIK,KAAK,IAAI9F,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACzC;IACAwF,MAAM,CAACC,WAAW,CAAC;EACrB,CAAC;EACD,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,aAAa,GAAGzC,OAAO,CAACO,OAAO,CAACrC,UAAU,CAAC;IACjD,IAAIwE,SAAS,GAAG,CAAC;IACjB,MAAM5F,QAAQ,GAAGwE,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACK,OAAO,CAACzD,QAAQ,CAAC;IACxD,KAAK,IAAI6F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7F,QAAQ,CAACoE,MAAM,EAAEyB,CAAC,IAAI,CAAC,EAAE;MAC3C,MAAMxB,GAAG,GAAGrE,QAAQ,CAAC6F,CAAC,CAAC;MACvB,IAAID,SAAS,GAAGvB,GAAG,CAACjD,UAAU,CAAC,GAAGuE,aAAa,EAAE;QAC/C;QACA;QACA,IAAIE,CAAC,KAAK,CAAC,EAAE;UACXD,SAAS,GAAGD,aAAa;QAC3B;QACA;MACF;MACAC,SAAS,IAAIvB,GAAG,CAACjD,UAAU,CAAC;IAC9B;IACA,OAAOwE,SAAS;EAClB,CAAC;EACD,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnCN,cAAc,CAAC,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;EACtC,CAAC;EACD,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjCP,cAAc,CAACE,aAAa,CAAC,CAAC,CAAC;EACjC,CAAC;EACD,MAAM,CAACM,aAAa,EAAE;IACpB3F,QAAQ,EAAE4F,iBAAiB;IAC3B,GAAGC;EACL,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE/L,QAAQ,CAACzB,OAAO,EAAE,WAAW,EAAE;IACtCwH,SAAS,EAAE,CAAC,CAAC,EAAE/G,KAAK,CAACT,OAAO,EAAEsD,OAAO,CAACJ,WAAW,EAAEI,OAAO,CAACL,aAAa,CAAC;IACzE4F,WAAW,EAAErC,iBAAiB;IAC9BiH,0BAA0B,EAAE,IAAI;IAChC9C,sBAAsB;IACtB7H;EACF,CAAC,CAAC;;EAEF;EACA;EACA,MAAM4K,yBAAyB,GAAGpN,KAAK,CAACqN,WAAW,CAACvI,cAAc,IAAI;IACpEmI,iBAAiB,GAAGnI,cAAc,CAAC;IACnCiF,gBAAgB,CAAC;MACfhG,QAAQ,EAAE,IAAI;MACde;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACmI,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACK,iBAAiB,EAAEC,qBAAqB,CAAC,GAAG,CAAC,CAAC,EAAEpM,QAAQ,CAACzB,OAAO,EAAE,eAAe,EAAE;IACxFwH,SAAS,EAAE,CAAC,CAAC,EAAE/G,KAAK,CAACT,OAAO,EAAEsD,OAAO,CAACK,aAAa,EAAEsE,oBAAoB,CAACT,SAAS,CAAC;IACpFqB,WAAW,EAAEzH,gBAAgB,CAACpB,OAAO;IACrC2K,sBAAsB;IACtB7H,UAAU;IACVgL,eAAe,EAAE;MACflG,WAAW;MACXrE,KAAK,EAAE;QACLuF,qBAAqB,EAAEvF,KAAK,CAACyF,qBAAqB,IAAIzF,KAAK,CAACuF,qBAAqB;QACjFI,mBAAmB,EAAE3F,KAAK,CAAC4F,mBAAmB,IAAI5F,KAAK,CAAC2F;MAC1D,CAAC;MACDnB,SAAS,EAAE;QACTiB,qBAAqB,EAAEJ,0BAA0B;QACjDO,mBAAmB,EAAEF;MACvB;IACF;EACF,CAAC,CAAC;EACF,MAAM8E,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;IAC9BA,mBAAmB,CAACC,qBAAqB,GAAG3F,UAAU,GAAG,aAAa,CAAC,CAAC,EAAE5G,WAAW,CAACwM,GAAG,EAAEZ,aAAa,EAAE;MACxG,GAAGE,kBAAkB;MACrB7F,QAAQ,EAAE+F;IACZ,CAAC,CAAC,GAAG,IAAI;IACT,MAAMS,mBAAmB,GAAGrE,kBAAkB,IAAIE,gBAAgB;IAClE,MAAMoE,iBAAiB,GAAG9F,UAAU,KAAK3E,aAAa,KAAK,MAAM,IAAIwK,mBAAmB,IAAIxK,aAAa,KAAK,IAAI,CAAC;IACnHqK,mBAAmB,CAACK,iBAAiB,GAAGD,iBAAiB,GAAG,aAAa,CAAC,CAAC,EAAE1M,WAAW,CAACwM,GAAG,EAAEN,iBAAiB,EAAE;MAC/GU,SAAS,EAAErH,KAAK,GAAG,OAAO,GAAG,MAAM;MACnCsH,OAAO,EAAEnB,sBAAsB;MAC/B3K,QAAQ,EAAE,CAACqH,kBAAkB;MAC7B,GAAG+D;IACL,CAAC,CAAC,GAAG,IAAI;IACTG,mBAAmB,CAACQ,eAAe,GAAGJ,iBAAiB,GAAG,aAAa,CAAC,CAAC,EAAE1M,WAAW,CAACwM,GAAG,EAAEN,iBAAiB,EAAE;MAC7GU,SAAS,EAAErH,KAAK,GAAG,MAAM,GAAG,OAAO;MACnCsH,OAAO,EAAElB,oBAAoB;MAC7B5K,QAAQ,EAAE,CAACuH,gBAAgB;MAC3B,GAAG6D;IACL,CAAC,CAAC,GAAG,IAAI;IACT,OAAOG,mBAAmB;EAC5B,CAAC;EACD,MAAMS,sBAAsB,GAAG,CAAC,CAAC,EAAEpN,iBAAiB,CAACrB,OAAO,EAAE2M,SAAS,IAAI;IACzE,MAAM;MACJ3B,QAAQ;MACRS;IACF,CAAC,GAAGZ,WAAW,CAAC,CAAC;IACjB,IAAI,CAACY,OAAO,IAAI,CAACT,QAAQ,EAAE;MACzB;IACF;IACA,IAAIS,OAAO,CAACjD,KAAK,CAAC,GAAGwC,QAAQ,CAACxC,KAAK,CAAC,EAAE;MACpC;MACA,MAAMkG,eAAe,GAAG1D,QAAQ,CAACzC,WAAW,CAAC,IAAIkD,OAAO,CAACjD,KAAK,CAAC,GAAGwC,QAAQ,CAACxC,KAAK,CAAC,CAAC;MAClFiE,MAAM,CAACiC,eAAe,EAAE;QACtB/B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIlB,OAAO,CAAChD,GAAG,CAAC,GAAGuC,QAAQ,CAACvC,GAAG,CAAC,EAAE;MACvC;MACA,MAAMiG,eAAe,GAAG1D,QAAQ,CAACzC,WAAW,CAAC,IAAIkD,OAAO,CAAChD,GAAG,CAAC,GAAGuC,QAAQ,CAACvC,GAAG,CAAC,CAAC;MAC9EgE,MAAM,CAACiC,eAAe,EAAE;QACtB/B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAMgC,uBAAuB,GAAG,CAAC,CAAC,EAAEtN,iBAAiB,CAACrB,OAAO,EAAE,MAAM;IACnE,IAAIsI,UAAU,IAAI3E,aAAa,KAAK,KAAK,EAAE;MACzCwG,uBAAuB,CAAC,CAACD,oBAAoB,CAAC;IAChD;EACF,CAAC,CAAC;EACF5J,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAE5N,SAAS,CAACjB,OAAO,EAAE,MAAM;MAChD;MACA;MACA;MACA;MACA;MACA;MACA,IAAIwK,OAAO,CAACO,OAAO,EAAE;QACnBkB,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACF,IAAI6C,cAAc;;IAElB;AACJ;AACA;IACI,MAAMC,cAAc,GAAGC,OAAO,IAAI;MAChCA,OAAO,CAACC,OAAO,CAACC,MAAM,IAAI;QACxBA,MAAM,CAACC,YAAY,CAACF,OAAO,CAACpN,IAAI,IAAI;UAClCiN,cAAc,EAAEM,SAAS,CAACvN,IAAI,CAAC;QACjC,CAAC,CAAC;QACFqN,MAAM,CAACG,UAAU,CAACJ,OAAO,CAACpN,IAAI,IAAI;UAChCiN,cAAc,EAAEQ,OAAO,CAACzN,IAAI,CAAC;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC;MACFgN,YAAY,CAAC,CAAC;MACdF,uBAAuB,CAAC,CAAC;IAC3B,CAAC;IACD,MAAMY,GAAG,GAAG,CAAC,CAAC,EAAE/N,YAAY,CAACxB,OAAO,EAAEwK,OAAO,CAACO,OAAO,CAAC;IACtDwE,GAAG,CAACC,gBAAgB,CAAC,QAAQ,EAAEX,YAAY,CAAC;IAC5C,IAAIY,gBAAgB;IACpB,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;MACzCZ,cAAc,GAAG,IAAIY,cAAc,CAACb,YAAY,CAAC;MACjD/C,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACK,OAAO,CAACzD,QAAQ,CAAC,CAAC2H,OAAO,CAACU,KAAK,IAAI;QACvDb,cAAc,CAACQ,OAAO,CAACK,KAAK,CAAC;MAC/B,CAAC,CAAC;IACJ;IACA,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAE;MAC3CH,gBAAgB,GAAG,IAAIG,gBAAgB,CAACb,cAAc,CAAC;MACvDU,gBAAgB,CAACH,OAAO,CAAC5E,UAAU,CAACK,OAAO,EAAE;QAC3C8E,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACA,OAAO,MAAM;MACXhB,YAAY,CAACiB,KAAK,CAAC,CAAC;MACpBP,GAAG,CAACQ,mBAAmB,CAAC,QAAQ,EAAElB,YAAY,CAAC;MAC/CY,gBAAgB,EAAEO,UAAU,CAAC,CAAC;MAC9BlB,cAAc,EAAEkB,UAAU,CAAC,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAAC/D,oBAAoB,EAAE0C,uBAAuB,CAAC,CAAC;;EAEnD;AACF;AACA;AACA;EACErO,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB,MAAMqB,eAAe,GAAGnE,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACK,OAAO,CAACzD,QAAQ,CAAC;IAC/D,MAAMoE,MAAM,GAAGuE,eAAe,CAACvE,MAAM;IACrC,IAAI,OAAOwE,oBAAoB,KAAK,WAAW,IAAIxE,MAAM,GAAG,CAAC,IAAIpD,UAAU,IAAI3E,aAAa,KAAK,KAAK,EAAE;MACtG,MAAMwM,QAAQ,GAAGF,eAAe,CAAC,CAAC,CAAC;MACnC,MAAMG,OAAO,GAAGH,eAAe,CAACvE,MAAM,GAAG,CAAC,CAAC;MAC3C,MAAM2E,eAAe,GAAG;QACtB7M,IAAI,EAAEgH,OAAO,CAACO,OAAO;QACrBuF,SAAS,EAAE;MACb,CAAC;MACD,MAAMC,uBAAuB,GAAGC,OAAO,IAAI;QACzCzG,qBAAqB,CAAC,CAACyG,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC;MACnD,CAAC;MACD,MAAMC,aAAa,GAAG,IAAIR,oBAAoB,CAACK,uBAAuB,EAAEF,eAAe,CAAC;MACxFK,aAAa,CAACpB,OAAO,CAACa,QAAQ,CAAC;MAC/B,MAAMQ,qBAAqB,GAAGH,OAAO,IAAI;QACvCvG,mBAAmB,CAAC,CAACuG,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC;MACjD,CAAC;MACD,MAAMG,YAAY,GAAG,IAAIV,oBAAoB,CAACS,qBAAqB,EAAEN,eAAe,CAAC;MACrFO,YAAY,CAACtB,OAAO,CAACc,OAAO,CAAC;MAC7B,OAAO,MAAM;QACXM,aAAa,CAACV,UAAU,CAAC,CAAC;QAC1BY,YAAY,CAACZ,UAAU,CAAC,CAAC;MAC3B,CAAC;IACH;IACA,OAAOa,SAAS;EAClB,CAAC,EAAE,CAACvI,UAAU,EAAE3E,aAAa,EAAEuG,oBAAoB,EAAE3C,YAAY,EAAEmE,MAAM,CAAC,CAAC;EAC3EpL,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpBlF,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNpJ,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB3C,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF3L,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB;IACAH,sBAAsB,CAAChI,qBAAqB,KAAKmD,cAAc,CAAC;EAClE,CAAC,EAAE,CAAC6E,sBAAsB,EAAE7E,cAAc,CAAC,CAAC;EAC5CtJ,KAAK,CAACwQ,mBAAmB,CAACzJ,MAAM,EAAE,OAAO;IACvC0J,eAAe,EAAE9E,oBAAoB;IACrC+E,mBAAmB,EAAErC;EACvB,CAAC,CAAC,EAAE,CAAC1C,oBAAoB,EAAE0C,uBAAuB,CAAC,CAAC;EACpD,MAAM,CAACsC,aAAa,EAAEC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAEzP,QAAQ,CAACzB,OAAO,EAAE,WAAW,EAAE;IAC7EwH,SAAS,EAAE,CAAC,CAAC,EAAE/G,KAAK,CAACT,OAAO,EAAEsD,OAAO,CAACI,SAAS,EAAEsE,iBAAiB,CAACR,SAAS,CAAC;IAC7EqB,WAAW,EAAEnD,aAAa;IAC1BiF,sBAAsB;IACtB7H,UAAU;IACVgL,eAAe,EAAE;MACfpJ,KAAK,EAAEkF;IACT;EACF,CAAC,CAAC;EACF,MAAMlG,SAAS,GAAG,aAAa,CAAC,CAAC,EAAEhC,WAAW,CAACwM,GAAG,EAAE+C,aAAa,EAAE;IACjE,GAAGC;EACL,CAAC,CAAC;EACF,IAAIC,UAAU,GAAG,CAAC;EAClB,MAAM7J,QAAQ,GAAGhH,KAAK,CAAC8Q,QAAQ,CAACC,GAAG,CAAC9J,YAAY,EAAEoI,KAAK,IAAI;IACzD,IAAI,EAAE,aAAarP,KAAK,CAACgR,cAAc,CAAC3B,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,IAAIvG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAAC,CAAC,EAAE/I,QAAQ,CAACgR,UAAU,EAAE5B,KAAK,CAAC,EAAE;QACnCpG,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,sCAAsC,CAAC,CAACwC,IAAI,CAAC,IAAI,CAAC,CAAC;MACrI;IACF;IACA,MAAMwF,UAAU,GAAG7B,KAAK,CAACzL,KAAK,CAAC7D,KAAK,KAAKwQ,SAAS,GAAGM,UAAU,GAAGxB,KAAK,CAACzL,KAAK,CAAC7D,KAAK;IACnFiK,YAAY,CAACmH,GAAG,CAACD,UAAU,EAAEL,UAAU,CAAC;IACxC,MAAMO,QAAQ,GAAGF,UAAU,KAAKnR,KAAK;IACrC8Q,UAAU,IAAI,CAAC;IACf,OAAO,aAAa7Q,KAAK,CAACqR,YAAY,CAAChC,KAAK,EAAE;MAC5CiC,SAAS,EAAEzJ,OAAO,KAAK,WAAW;MAClCzE,SAAS,EAAEgO,QAAQ,IAAI,CAACjI,OAAO,IAAI/F,SAAS;MAC5CgO,QAAQ;MACR5J,qBAAqB;MACrBH,QAAQ;MACRO,SAAS;MACT7H,KAAK,EAAEmR,UAAU;MACjB,IAAIL,UAAU,KAAK,CAAC,IAAI9Q,KAAK,KAAK,KAAK,IAAI,CAACsP,KAAK,CAACzL,KAAK,CAAC2N,QAAQ,GAAG;QACjEA,QAAQ,EAAE;MACZ,CAAC,GAAG,CAAC,CAAC;IACR,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGC,KAAK,IAAI;IAC7B;IACA,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,OAAO,EAAE;MACpE;IACF;IACA,MAAMvQ,IAAI,GAAG8I,UAAU,CAACK,OAAO;IAC/B,MAAM3I,YAAY,GAAG,CAAC,CAAC,EAAEb,cAAc,CAACvB,OAAO,EAAE4B,IAAI,CAAC,CAACwQ,aAAa;IACpE;IACA;IACA;IACA,MAAMC,IAAI,GAAGjQ,YAAY,CAACM,YAAY,CAAC,MAAM,CAAC;IAC9C,IAAI2P,IAAI,KAAK,KAAK,EAAE;MAClB;IACF;IACA,IAAIC,eAAe,GAAG1K,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,SAAS;IAC5E,IAAI2K,WAAW,GAAG3K,WAAW,KAAK,YAAY,GAAG,YAAY,GAAG,WAAW;IAC3E,IAAIA,WAAW,KAAK,YAAY,IAAIX,KAAK,EAAE;MACzC;MACAqL,eAAe,GAAG,YAAY;MAC9BC,WAAW,GAAG,WAAW;IAC3B;IACA,QAAQR,KAAK,CAACS,GAAG;MACf,KAAKF,eAAe;QAClBP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBtQ,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAEJ,YAAY,CAAC;QAC3C;MACF,KAAKuQ,WAAW;QACdR,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBtQ,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAET,QAAQ,CAAC;QACvC;MACF,KAAK,MAAM;QACToQ,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBtQ,SAAS,CAACP,IAAI,EAAE,IAAI,EAAED,QAAQ,CAAC;QAC/B;MACF,KAAK,KAAK;QACRoQ,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBtQ,SAAS,CAACP,IAAI,EAAE,IAAI,EAAEI,YAAY,CAAC;QACnC;MACF;QACE;IACJ;EACF,CAAC;EACD,MAAMgM,mBAAmB,GAAGD,sBAAsB,CAAC,CAAC;EACpD,MAAM,CAAC2E,QAAQ,EAAEC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAElR,QAAQ,CAACzB,OAAO,EAAE,MAAM,EAAE;IAC9D8G,GAAG;IACHU,SAAS,EAAE,CAAC,CAAC,EAAE/G,KAAK,CAACT,OAAO,EAAEsD,OAAO,CAACE,IAAI,EAAEgE,SAAS,CAAC;IACtDqB,WAAW,EAAEhF,QAAQ;IACrB8G,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGtC,KAAK;MACRZ;IACF,CAAC;IACD3E;EACF,CAAC,CAAC;EACF,MAAM,CAAC8P,YAAY,EAAEC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAEpR,QAAQ,CAACzB,OAAO,EAAE,UAAU,EAAE;IAC1E8G,GAAG,EAAE0D,OAAO;IACZhD,SAAS,EAAElE,OAAO,CAACG,QAAQ;IAC3BoF,WAAW,EAAE/D,YAAY;IACzB6F,sBAAsB;IACtB7H,UAAU;IACVgL,eAAe,EAAE;MACfpJ,KAAK,EAAE;QACLL,QAAQ,EAAE+F,aAAa,CAAC/F,QAAQ;QAChC,CAACtB,QAAQ,GAAG,SAASkE,KAAK,GAAG,MAAM,GAAG,OAAO,EAAE,GAAG,cAAc,GAAGmB,gBAAgB,GAAGyI,SAAS,GAAG,CAACzG,aAAa,CAAChF;MACnH;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAAC0N,QAAQ,EAAEC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAEtR,QAAQ,CAACzB,OAAO,EAAE,MAAM,EAAE;IAC9D8G,GAAG,EAAE4D,UAAU;IACflD,SAAS,EAAE,CAAC,CAAC,EAAE/G,KAAK,CAACT,OAAO,EAAEsD,OAAO,CAAC1B,IAAI,EAAE0B,OAAO,CAACiC,aAAa,CAAC;IAClEsD,WAAW,EAAEvD,IAAI;IACjBqF,sBAAsB;IACtB7H,UAAU;IACVkQ,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXC,SAAS,EAAEnB,KAAK,IAAI;QAClBD,aAAa,CAACC,KAAK,CAAC;QACpBkB,QAAQ,CAACC,SAAS,GAAGnB,KAAK,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAa,CAAC,CAAC,EAAErQ,WAAW,CAACyR,IAAI,EAAET,QAAQ,EAAE;IAClD,GAAGC,aAAa;IAChBrL,QAAQ,EAAE,CAAC0G,mBAAmB,CAACK,iBAAiB,EAAEL,mBAAmB,CAACC,qBAAqB,EAAE,aAAa,CAAC,CAAC,EAAEvM,WAAW,CAACyR,IAAI,EAAEP,YAAY,EAAE;MAC5I,GAAGC,iBAAiB;MACpBvL,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE5F,WAAW,CAACwM,GAAG,EAAE4E,QAAQ,EAAE;QACrD,YAAY,EAAE3L,SAAS;QACvB,iBAAiB,EAAEC,cAAc;QACjC,kBAAkB,EAAEQ,WAAW,KAAK,UAAU,GAAG,UAAU,GAAG,IAAI;QAClEyK,IAAI,EAAE,SAAS;QACf,GAAGU,aAAa;QAChBzL,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAEmC,OAAO,IAAI/F,SAAS;IAC1B,CAAC,CAAC,EAAEsK,mBAAmB,CAACQ,eAAe;EACzC,CAAC,CAAC;AACJ,CAAC,CAAC;AACFpF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3C,IAAI,CAACyM,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/L,MAAM,EAAE3G,QAAQ,CAACV,OAAO;EACxB;AACF;AACA;AACA;AACA;EACE0H,wBAAwB,EAAElH,UAAU,CAACR,OAAO,CAACqT,IAAI;EACjD;AACF;AACA;EACE,YAAY,EAAE7S,UAAU,CAACR,OAAO,CAACsT,MAAM;EACvC;AACF;AACA;EACE,iBAAiB,EAAE9S,UAAU,CAACR,OAAO,CAACsT,MAAM;EAC5C;AACF;AACA;AACA;AACA;EACElQ,QAAQ,EAAE5C,UAAU,CAACR,OAAO,CAACqT,IAAI;EACjC;AACF;AACA;EACE/L,QAAQ,EAAE9G,UAAU,CAACR,OAAO,CAACuT,IAAI;EACjC;AACF;AACA;EACEjQ,OAAO,EAAE9C,UAAU,CAACR,OAAO,CAACwT,MAAM;EAClC;AACF;AACA;EACEhM,SAAS,EAAEhH,UAAU,CAACR,OAAO,CAACsT,MAAM;EACpC;AACF;AACA;AACA;EACE7L,SAAS,EAAEjH,UAAU,CAACR,OAAO,CAAC6I,WAAW;EACzC;AACF;AACA;AACA;EACE7C,cAAc,EAAExF,UAAU,CAACR,OAAO,CAAC,sCAAsCyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC0T,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,EAAElT,UAAU,CAACR,OAAO,CAACsT,MAAM,CAAC,CAAC;EACnK;AACF;AACA;AACA;AACA;AACA;EACE3L,QAAQ,EAAEnH,UAAU,CAACR,OAAO,CAAC2T,IAAI;EACjC;AACF;AACA;AACA;EACE/L,WAAW,EAAEpH,UAAU,CAACR,OAAO,CAAC0T,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACjE;AACF;AACA;AACA;AACA;EACE7L,qBAAqB,EAAErH,UAAU,CAACR,OAAO,CAAC6I,WAAW;EACrD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElF,aAAa,EAAEnD,UAAU,CAACR,OAAO,CAAC,sCAAsC0T,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EACpG;AACF;AACA;AACA;EACE5L,qBAAqB,EAAEtH,UAAU,CAACR,OAAO,CAACqT,IAAI;EAC9C;AACF;AACA;AACA;EACEtL,SAAS,EAAEvH,UAAU,CAACR,OAAO,CAAC4T,KAAK,CAAC;IAClCzK,mBAAmB,EAAE3I,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;IACvG9P,SAAS,EAAElD,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;IAC7F5R,IAAI,EAAEpB,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;IACxFhQ,IAAI,EAAEhD,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;IACxFK,SAAS,EAAErT,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;IAC7F7P,aAAa,EAAEnD,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;IACjG/P,QAAQ,EAAEjD,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;IAC5FxK,qBAAqB,EAAExI,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC;EAC1G,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEjQ,KAAK,EAAE/C,UAAU,CAACR,OAAO,CAAC4T,KAAK,CAAC;IAC9BzK,mBAAmB,EAAE3I,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACnDK,mBAAmB,EAAE1I,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACnDnF,SAAS,EAAElD,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACzCjH,IAAI,EAAEpB,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACpCrF,IAAI,EAAEhD,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACpCgL,SAAS,EAAErT,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACzClF,aAAa,EAAEnD,UAAU,CAACR,OAAO,CAAC6I,WAAW;IAC7CpF,QAAQ,EAAEjD,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACxCG,qBAAqB,EAAExI,UAAU,CAACR,OAAO,CAAC6I,WAAW;IACrDC,qBAAqB,EAAEtI,UAAU,CAACR,OAAO,CAAC6I;EAC5C,CAAC,CAAC;EACF;AACF;AACA;EACEiL,EAAE,EAAEtT,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC+T,OAAO,CAACvT,UAAU,CAACR,OAAO,CAACyT,SAAS,CAAC,CAACjT,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,EAAEhT,UAAU,CAACR,OAAO,CAACqT,IAAI,CAAC,CAAC,CAAC,EAAE7S,UAAU,CAACR,OAAO,CAAC2T,IAAI,EAAEnT,UAAU,CAACR,OAAO,CAACwT,MAAM,CAAC,CAAC;EAC/N;AACF;AACA;AACA;AACA;EACExL,iBAAiB,EAAExH,UAAU,CAACR,OAAO,CAACwT,MAAM;EAC5C;AACF;AACA;AACA;AACA;EACEvL,oBAAoB,EAAEzH,UAAU,CAACR,OAAO,CAACwT,MAAM;EAC/C;AACF;AACA;AACA;EACEtL,SAAS,EAAE1H,UAAU,CAACR,OAAO,CAAC0T,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;EACxE;AACF;AACA;AACA;EACErT,KAAK,EAAEG,UAAU,CAACR,OAAO,CAACgU,GAAG;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7L,OAAO,EAAE3H,UAAU,CAACR,OAAO,CAAC0T,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;EAC1E;AACF;AACA;AACA;AACA;EACEtL,gBAAgB,EAAE5H,UAAU,CAACR,OAAO,CAACqT;AACvC,CAAC,GAAG,KAAK,CAAC;AACV,IAAIY,QAAQ,GAAG7T,OAAO,CAACJ,OAAO,GAAG2G,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}