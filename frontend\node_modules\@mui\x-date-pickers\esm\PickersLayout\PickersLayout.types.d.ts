import * as React from 'react';
import { SxProps, Theme } from '@mui/material/styles';
import { SlotComponentProps } from '@mui/utils/types';
import { PickersActionBar, PickersActionBarProps } from "../PickersActionBar/index.js";
import { BaseToolbarProps, ExportedBaseToolbarProps } from "../internals/models/props/toolbar.js";
import { ExportedBaseTabsProps } from "../internals/models/props/tabs.js";
import { PickersLayoutClasses } from "./pickersLayoutClasses.js";
import { PickersShortcutsProps } from "../PickersShortcuts/index.js";
import { ExportedPickersShortcutProps, PickersShortcuts } from "../PickersShortcuts/PickersShortcuts.js";
import { PickerOwnerState } from "../models/index.js";
import { PickerValidValue } from "../internals/models/index.js";
export interface ExportedPickersLayoutSlots<TValue extends PickerValidValue> {
  /**
   * Custom component for the action bar, it is placed below the Picker views.
   * @default PickersActionBar
   */
  actionBar?: React.ElementType<PickersActionBarProps>;
  /**
   * Custom component for the shortcuts.
   * @default PickersShortcuts
   */
  shortcuts?: React.JSXElementConstructor<PickersShortcutsProps<TValue>>;
  /**
   * Custom component for wrapping the layout.
   * It wraps the toolbar, views, action bar, and shortcuts.
   */
  layout?: React.JSXElementConstructor<PickersLayoutProps<TValue> & React.RefAttributes<HTMLDivElement>>;
}
export interface PickerLayoutOwnerState extends PickerOwnerState {
  /**
   * The direction of the layout.
   * Is equal to "ltr" when the layout is in left-to-right direction.
   * Is equal to "rtl" when the layout is in right-to-left direction.
   */
  layoutDirection: 'ltr' | 'rtl';
}
export interface ExportedPickersLayoutSlotProps<TValue extends PickerValidValue> {
  /**
   * Props passed down to the action bar component.
   */
  actionBar?: SlotComponentProps<typeof PickersActionBar, {}, PickerLayoutOwnerState>;
  /**
   * Props passed down to the shortcuts component.
   */
  shortcuts?: SlotComponentProps<typeof PickersShortcuts, {}, PickerLayoutOwnerState>;
  /**
   * Props passed down to the layoutRoot component.
   */
  layout?: Partial<PickersLayoutProps<TValue>>;
}
export interface PickersLayoutSlots<TValue extends PickerValidValue> extends ExportedPickersLayoutSlots<TValue> {
  /**
   * Tabs enabling toggling between views.
   */
  tabs?: React.ElementType<{}>;
  /**
   * Custom component for the toolbar.
   * It is placed above the Picker views.
   */
  toolbar?: React.JSXElementConstructor<BaseToolbarProps>;
}
export interface PickersLayoutSlotProps<TValue extends PickerValidValue> extends ExportedPickersLayoutSlotProps<TValue> {
  /**
   * Props passed down to the tabs component.
   */
  tabs?: ExportedBaseTabsProps;
  /**
   * Props passed down to the toolbar component.
   */
  toolbar?: ExportedBaseToolbarProps;
}
export interface PickersLayoutProps<TValue extends PickerValidValue> {
  className?: string;
  children?: React.ReactNode;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps<Theme>;
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<PickersLayoutClasses>;
  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: PickersLayoutSlots<TValue>;
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: PickersLayoutSlotProps<TValue>;
}
export interface SubComponents<TValue extends PickerValidValue> {
  toolbar: React.ReactElement<ExportedBaseToolbarProps> | null;
  content: React.ReactNode;
  tabs: React.ReactElement<ExportedBaseTabsProps> | null;
  actionBar: React.ReactElement<PickersActionBarProps>;
  shortcuts: React.ReactElement<ExportedPickersShortcutProps<TValue>> | null;
}