import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
} from '@mui/material';
import { apiService } from '../services/apiService';
import TopCompaniesChart from '../components/TopCompaniesChart';
import TransactionTrendsChart from '../components/TransactionTrendsChart';
import StatCard from '../components/StatCard';
import {
  TrendingUp,
  TrendingDown,
  Business,
  Person,
} from '@mui/icons-material';

const Analytics: React.FC = () => {
  const [summary, setSummary] = useState<any>(null);
  const [topCompanies, setTopCompanies] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [summaryResponse, companiesResponse] = await Promise.all([
          apiService.getAnalyticsSummary(),
          apiService.getTopCompanies({ limit: 20 }),
        ]);

        setSummary(summaryResponse.data);
        setTopCompanies(companiesResponse.data);
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, []);

  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(value)) {
      return '₹0';
    }

    const absValue = Math.abs(value);
    if (absValue >= 10000000) {
      return `₹${(value / 10000000).toFixed(1)}Cr`;
    } else if (absValue >= 100000) {
      return `₹${(value / 100000).toFixed(1)}L`;
    } else {
      return `₹${value.toLocaleString()}`;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  const netValue = summary ? summary.net_value : 0;

  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          Analytics
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Detailed analysis of insider trading patterns and trends
        </Typography>
      </Box>

      {/* Summary Stats */}
      {summary && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Total Buy Value"
              value={formatCurrency(summary.total_buy_value)}
              icon={<TrendingUp />}
              color="success"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Total Sell Value"
              value={formatCurrency(summary.total_sell_value)}
              icon={<TrendingDown />}
              color="error"
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Net Value"
              value={formatCurrency(Math.abs(netValue))}
              subtitle={netValue >= 0 ? 'Net Buying' : 'Net Selling'}
              icon={netValue >= 0 ? <TrendingUp /> : <TrendingDown />}
              color={netValue >= 0 ? 'success' : 'error'}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <StatCard
              title="Active Companies"
              value={summary.unique_companies.toLocaleString()}
              icon={<Business />}
              color="info"
            />
          </Grid>
        </Grid>
      )}

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Transaction Trends */}
        <Grid size={{ xs: 12, lg: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Transaction Trends (Last 30 Days)
              </Typography>
              <TransactionTrendsChart />
            </CardContent>
          </Card>
        </Grid>

        {/* Top Companies */}
        <Grid size={{ xs: 12, lg: 4 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Companies by Value
              </Typography>
              <Box sx={{ height: 400, overflow: 'auto' }}>
                {topCompanies.slice(0, 10).map((company, index) => (
                  <Box
                    key={company.symbol}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 1,
                      borderBottom: index < 9 ? '1px solid' : 'none',
                      borderBottomColor: 'divider',
                    }}
                  >
                    <Box>
                      <Typography variant="body2" fontWeight={600}>
                        {company.symbol}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {company.transaction_count} transactions
                      </Typography>
                    </Box>
                    <Typography variant="body2" fontWeight={600} color="primary.main">
                      {formatCurrency(company.total_value)}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Companies Chart */}
        <Grid size={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top 10 Companies by Transaction Value
              </Typography>
              <TopCompaniesChart data={topCompanies} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Analytics;
