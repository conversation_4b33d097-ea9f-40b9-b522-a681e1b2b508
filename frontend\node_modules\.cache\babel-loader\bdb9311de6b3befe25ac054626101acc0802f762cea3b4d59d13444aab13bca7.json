{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useTimePickerDefaultizedProps = useTimePickerDefaultizedProps;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styles = require(\"@mui/material/styles\");\nvar _useUtils = require(\"../internals/hooks/useUtils\");\nvar _TimePickerToolbar = require(\"./TimePickerToolbar\");\nvar _views = require(\"../internals/utils/views\");\nvar _useTimeManager = require(\"../managers/useTimeManager\");\nfunction useTimePickerDefaultizedProps(props, name) {\n  const utils = (0, _useUtils.useUtils)();\n  const themeProps = (0, _styles.useThemeProps)({\n    props,\n    name\n  });\n  const validationProps = (0, _useTimeManager.useApplyDefaultValuesToTimeValidationProps)(themeProps);\n  const ampm = themeProps.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return (0, _extends2.default)({}, themeProps.localeText, {\n      timePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  return (0, _extends2.default)({}, themeProps, validationProps, {\n    ampm,\n    localeText\n  }, (0, _views.applyDefaultViewProps)({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['hours', 'minutes'],\n    defaultOpenTo: 'hours'\n  }), {\n    slots: (0, _extends2.default)({\n      toolbar: _TimePickerToolbar.TimePickerToolbar\n    }, themeProps.slots),\n    slotProps: (0, _extends2.default)({}, themeProps.slotProps, {\n      toolbar: (0, _extends2.default)({\n        ampm,\n        ampmInClock: themeProps.ampmInClock\n      }, themeProps.slotProps?.toolbar)\n    })\n  });\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "useTimePickerDefaultizedProps", "_extends2", "React", "_styles", "_useUtils", "_TimePickerToolbar", "_views", "_useTimeManager", "props", "name", "utils", "useUtils", "themeProps", "useThemeProps", "validationProps", "useApplyDefaultValuesToTimeValidationProps", "ampm", "is12HourCycleInCurrentLocale", "localeText", "useMemo", "toolbarTitle", "timePickerToolbarTitle", "applyDefaultViewProps", "views", "openTo", "defaultViews", "defaultOpenTo", "slots", "toolbar", "TimePickerToolbar", "slotProps", "ampmInClock"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/TimePicker/shared.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useTimePickerDefaultizedProps = useTimePickerDefaultizedProps;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styles = require(\"@mui/material/styles\");\nvar _useUtils = require(\"../internals/hooks/useUtils\");\nvar _TimePickerToolbar = require(\"./TimePickerToolbar\");\nvar _views = require(\"../internals/utils/views\");\nvar _useTimeManager = require(\"../managers/useTimeManager\");\nfunction useTimePickerDefaultizedProps(props, name) {\n  const utils = (0, _useUtils.useUtils)();\n  const themeProps = (0, _styles.useThemeProps)({\n    props,\n    name\n  });\n  const validationProps = (0, _useTimeManager.useApplyDefaultValuesToTimeValidationProps)(themeProps);\n  const ampm = themeProps.ampm ?? utils.is12HourCycleInCurrentLocale();\n  const localeText = React.useMemo(() => {\n    if (themeProps.localeText?.toolbarTitle == null) {\n      return themeProps.localeText;\n    }\n    return (0, _extends2.default)({}, themeProps.localeText, {\n      timePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  return (0, _extends2.default)({}, themeProps, validationProps, {\n    ampm,\n    localeText\n  }, (0, _views.applyDefaultViewProps)({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['hours', 'minutes'],\n    defaultOpenTo: 'hours'\n  }), {\n    slots: (0, _extends2.default)({\n      toolbar: _TimePickerToolbar.TimePickerToolbar\n    }, themeProps.slots),\n    slotProps: (0, _extends2.default)({}, themeProps.slotProps, {\n      toolbar: (0, _extends2.default)({\n        ampm,\n        ampmInClock: themeProps.ampmInClock\n      }, themeProps.slotProps?.toolbar)\n    })\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,6BAA6B,GAAGA,6BAA6B;AACrE,IAAIC,SAAS,GAAGN,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIS,KAAK,GAAGV,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIU,OAAO,GAAGV,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIW,SAAS,GAAGX,OAAO,CAAC,6BAA6B,CAAC;AACtD,IAAIY,kBAAkB,GAAGZ,OAAO,CAAC,qBAAqB,CAAC;AACvD,IAAIa,MAAM,GAAGb,OAAO,CAAC,0BAA0B,CAAC;AAChD,IAAIc,eAAe,GAAGd,OAAO,CAAC,4BAA4B,CAAC;AAC3D,SAASO,6BAA6BA,CAACQ,KAAK,EAAEC,IAAI,EAAE;EAClD,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEN,SAAS,CAACO,QAAQ,EAAE,CAAC;EACvC,MAAMC,UAAU,GAAG,CAAC,CAAC,EAAET,OAAO,CAACU,aAAa,EAAE;IAC5CL,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAMK,eAAe,GAAG,CAAC,CAAC,EAAEP,eAAe,CAACQ,0CAA0C,EAAEH,UAAU,CAAC;EACnG,MAAMI,IAAI,GAAGJ,UAAU,CAACI,IAAI,IAAIN,KAAK,CAACO,4BAA4B,CAAC,CAAC;EACpE,MAAMC,UAAU,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAM;IACrC,IAAIP,UAAU,CAACM,UAAU,EAAEE,YAAY,IAAI,IAAI,EAAE;MAC/C,OAAOR,UAAU,CAACM,UAAU;IAC9B;IACA,OAAO,CAAC,CAAC,EAAEjB,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEkB,UAAU,CAACM,UAAU,EAAE;MACvDG,sBAAsB,EAAET,UAAU,CAACM,UAAU,CAACE;IAChD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,UAAU,CAACM,UAAU,CAAC,CAAC;EAC3B,OAAO,CAAC,CAAC,EAAEjB,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEkB,UAAU,EAAEE,eAAe,EAAE;IAC7DE,IAAI;IACJE;EACF,CAAC,EAAE,CAAC,CAAC,EAAEZ,MAAM,CAACgB,qBAAqB,EAAE;IACnCC,KAAK,EAAEX,UAAU,CAACW,KAAK;IACvBC,MAAM,EAAEZ,UAAU,CAACY,MAAM;IACzBC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;IAClCC,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE;IACFC,KAAK,EAAE,CAAC,CAAC,EAAE1B,SAAS,CAACP,OAAO,EAAE;MAC5BkC,OAAO,EAAEvB,kBAAkB,CAACwB;IAC9B,CAAC,EAAEjB,UAAU,CAACe,KAAK,CAAC;IACpBG,SAAS,EAAE,CAAC,CAAC,EAAE7B,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEkB,UAAU,CAACkB,SAAS,EAAE;MAC1DF,OAAO,EAAE,CAAC,CAAC,EAAE3B,SAAS,CAACP,OAAO,EAAE;QAC9BsB,IAAI;QACJe,WAAW,EAAEnB,UAAU,CAACmB;MAC1B,CAAC,EAAEnB,UAAU,CAACkB,SAAS,EAAEF,OAAO;IAClC,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}