{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getDialogActionsUtilityClass = getDialogActionsUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getDialogActionsUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiDialogActions', slot);\n}\nconst dialogActionsClasses = (0, _generateUtilityClasses.default)('MuiDialogActions', ['root', 'spacing']);\nvar _default = exports.default = dialogActionsClasses;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "getDialogActionsUtilityClass", "_generateUtilityClasses", "_generateUtilityClass", "slot", "dialogActionsClasses", "_default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/DialogActions/dialogActionsClasses.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.getDialogActionsUtilityClass = getDialogActionsUtilityClass;\nvar _generateUtilityClasses = _interopRequireDefault(require(\"@mui/utils/generateUtilityClasses\"));\nvar _generateUtilityClass = _interopRequireDefault(require(\"@mui/utils/generateUtilityClass\"));\nfunction getDialogActionsUtilityClass(slot) {\n  return (0, _generateUtilityClass.default)('MuiDialogActions', slot);\n}\nconst dialogActionsClasses = (0, _generateUtilityClasses.default)('MuiDialogActions', ['root', 'spacing']);\nvar _default = exports.default = dialogActionsClasses;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAG,KAAK,CAAC;AACxBG,OAAO,CAACE,4BAA4B,GAAGA,4BAA4B;AACnE,IAAIC,uBAAuB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAClG,IAAIQ,qBAAqB,GAAGT,sBAAsB,CAACC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC9F,SAASM,4BAA4BA,CAACG,IAAI,EAAE;EAC1C,OAAO,CAAC,CAAC,EAAED,qBAAqB,CAACP,OAAO,EAAE,kBAAkB,EAAEQ,IAAI,CAAC;AACrE;AACA,MAAMC,oBAAoB,GAAG,CAAC,CAAC,EAAEH,uBAAuB,CAACN,OAAO,EAAE,kBAAkB,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC1G,IAAIU,QAAQ,GAAGP,OAAO,CAACH,OAAO,GAAGS,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}