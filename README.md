# NSE Insider Trading Data Scraping & Visualization System

A comprehensive, production-ready system for scraping, storing, and visualizing NSE (National Stock Exchange) insider trading data with real-time analytics and interactive dashboards.

## 🚀 Features

### 📊 Data Collection & Storage
- **Automated Scraping**: Robust NSE insider trading data scraper with 15-minute intervals
- **PostgreSQL Database**: Optimized schema with proper indexing and relationships
- **Data Validation**: Comprehensive data cleaning and deduplication
- **Error Handling**: Retry mechanisms with exponential backoff
- **Incremental Updates**: Only fetch new/changed data to optimize performance

### 🔧 Backend API
- **FastAPI Framework**: High-performance REST API with automatic documentation
- **Public Access**: No authentication required for easy access
- **Advanced Filtering**: Filter by company, person, date range, transaction type, etc.
- **Analytics Endpoints**: Summary statistics, top companies, trends analysis
- **Caching**: In-memory caching for improved response times
- **Rate Limiting**: Built-in protection against abuse

### 🎨 Frontend Dashboard
- **React + TypeScript**: Modern, responsive web application
- **Material-UI**: Professional design with consistent theming
- **Interactive Charts**: Plotly.js and Recharts for data visualization
- **Data Tables**: AG Grid-like functionality with sorting and filtering
- **Real-time Updates**: Live data refresh every 5 minutes
- **Mobile Responsive**: Works seamlessly on all device sizes

### 📈 Analytics & Monitoring
- **Performance Monitoring**: System, database, and API metrics
- **Health Checks**: Automated monitoring with alerting
- **Data Quality**: Validation and integrity checks
- **Trend Analysis**: Historical data analysis and pattern recognition

### 🐳 Deployment
- **Docker Support**: Complete containerization with docker-compose
- **Nginx Reverse Proxy**: Production-ready web server configuration
- **Scalable Architecture**: Microservices design for easy scaling
- **Environment Configuration**: Flexible configuration management

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │   FastAPI Backend│    │  PostgreSQL DB  │
│   (Port 3000)   │◄──►│   (Port 8000)   │◄──►│   (External)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │  NSE Scraper    │              │
         │              │  (Scheduled)    │──────────────┘
         │              └─────────────────┘
         │
┌─────────────────┐    ┌─────────────────┐
│  Nginx Proxy    │    │  Performance    │
│  (Port 80)      │    │  Monitor        │
└─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Node.js 18+
- Docker & Docker Compose (optional)

### 1. Clone Repository
```bash
git clone <repository-url>
cd nsescrapper
```

### 2. Backend Setup
```bash
# Install Python dependencies
pip install -r requirements.txt

# Initialize database
python database/connection.py

# Start API server
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. Frontend Setup
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

### 4. Start Data Scraper
```bash
# Run scraper once
python scraper/enhanced_scraper.py

# Start scheduled scraper
python scraper/scheduler.py
```

### 5. Access Application
- **Frontend Dashboard**: http://localhost:3000
- **API Documentation**: http://localhost:8000/docs
- **API Health Check**: http://localhost:8000/health

## 🐳 Docker Deployment

### Quick Start with Docker Compose
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Services Included
- **API Server**: FastAPI backend on port 8000
- **Frontend**: React application served by Nginx on port 80
- **Scraper**: Automated data collection service
- **Monitor**: Performance monitoring service
- **Redis**: Caching layer
- **Nginx**: Reverse proxy and static file server

## 📊 Database Schema

### Core Tables
- **companies**: Company information and metadata
- **insider_transactions**: Main transaction records
- **person_categories**: Insider person categories (Promoters, Directors, etc.)
- **transaction_types**: Transaction types (Buy, Sell, etc.)
- **scraper_logs**: Scraper execution history
- **api_usage_log**: API usage tracking

### Key Features
- **Optimized Indexing**: Fast queries on date ranges and company lookups
- **Data Integrity**: Foreign key constraints and validation
- **Audit Trail**: Complete transaction history with timestamps
- **Performance**: Efficient storage and retrieval for large datasets

## 🔌 API Endpoints

### Core Data Endpoints
- `GET /transactions` - List transactions with filtering
- `GET /companies/{symbol}/transactions` - Company-specific transactions
- `GET /analytics/summary` - Summary statistics
- `GET /analytics/top-companies` - Top companies by activity
- `GET /search` - Search companies and persons
- `GET /system/status` - System health and status

### Example API Usage
```bash
# Get recent transactions
curl "http://localhost:8000/transactions?limit=10&sort_order=desc"

# Get company transactions
curl "http://localhost:8000/companies/RELIANCE/transactions"

# Get analytics summary
curl "http://localhost:8000/analytics/summary"

# Search companies
curl "http://localhost:8000/search?q=reliance&type=company"
```

## 📱 Frontend Features

### Dashboard
- **Key Metrics**: Total transactions, buy/sell values, net activity
- **System Status**: Real-time scraper and database status
- **Recent Transactions**: Latest insider trading activities
- **Interactive Charts**: Transaction trends and top companies

### Transactions Page
- **Advanced Filtering**: By symbol, person, category, date range, value
- **Data Grid**: Sortable, paginated transaction table
- **Export Options**: CSV, Excel export functionality
- **Real-time Search**: Instant filtering and search

### Analytics Page
- **Trend Analysis**: Historical transaction patterns
- **Company Rankings**: Top performers by various metrics
- **Visual Charts**: Interactive graphs and visualizations
- **Statistical Summaries**: Comprehensive data insights

### Companies Page
- **Company Directory**: Browse all companies with insider activity
- **Activity Metrics**: Transaction counts, values, and trends
- **Search & Filter**: Find companies by symbol or name
- **Detailed Views**: Company-specific transaction history

## ⚙️ Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:pass@host:port/dbname

# API Settings
API_HOST=0.0.0.0
API_PORT=8000

# Scraper Settings
SCRAPER_INTERVAL_MINUTES=15
SCRAPER_MARKET_HOURS_ONLY=true

# Frontend
REACT_APP_API_URL=http://localhost:8000
```

### Scraper Configuration
```json
{
  "scraping": {
    "interval_minutes": 2,
    "market_hours_only": true,
    "market_start_time": "09:15",
    "market_end_time": "15:30",
    "weekdays_only": true
  },
  "health_check": {
    "enabled": true,
    "interval_minutes": 60,
    "max_consecutive_failures": 3
  }
}
```

## 🔧 Development

### Project Structure
```
nsescrapper/
├── api/                    # FastAPI backend
│   ├── main.py            # Main API application
│   ├── models.py          # Pydantic models
│   └── ...
├── database/              # Database components
│   ├── connection.py      # Database connection & DAL
│   └── schema.sql         # Database schema
├── scraper/               # Data scraping components
│   ├── enhanced_scraper.py # Main scraper
│   └── scheduler.py       # Automated scheduling
├── frontend/              # React frontend
│   ├── src/
│   │   ├── components/    # Reusable components
│   │   ├── pages/         # Page components
│   │   └── services/      # API services
│   └── ...
├── monitoring/            # Performance monitoring
├── docker-compose.yml     # Docker deployment
└── README.md
```

### Adding New Features
1. **Backend**: Add new endpoints in `api/main.py`
2. **Frontend**: Create components in `frontend/src/components/`
3. **Database**: Update schema in `database/schema.sql`
4. **Scraper**: Modify logic in `scraper/enhanced_scraper.py`

## 📊 Performance

### Optimizations Implemented
- **Database Indexing**: Optimized queries for time-series data
- **API Caching**: In-memory caching for frequently accessed data
- **Frontend Optimization**: Lazy loading, virtualization, code splitting
- **Connection Pooling**: Efficient database connection management
- **Compression**: Gzip compression for API responses

### Monitoring Metrics
- **System**: CPU, memory, disk usage
- **Database**: Connection count, query performance, cache hit ratio
- **API**: Response times, error rates, request volume
- **Scraper**: Success rates, data quality, execution times

## 🛡️ Security

### Implemented Measures
- **Rate Limiting**: Protection against API abuse
- **Input Validation**: Comprehensive data validation
- **SQL Injection Prevention**: Parameterized queries
- **CORS Configuration**: Proper cross-origin resource sharing
- **Security Headers**: Standard security headers in responses

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **NSE India** for providing the data source
- **FastAPI** for the excellent API framework
- **React** and **Material-UI** for the frontend framework
- **PostgreSQL** for robust data storage
- **Docker** for containerization support

## 📞 Support

For support, please open an issue in the GitHub repository or contact the development team.

---

**Built with ❤️ for the Indian financial community**
