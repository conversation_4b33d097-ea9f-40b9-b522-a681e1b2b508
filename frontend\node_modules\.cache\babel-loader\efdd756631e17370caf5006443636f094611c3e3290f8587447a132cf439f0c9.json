{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = void 0;\nexports.arrayIncludes = arrayIncludes;\nexports.getFocusedListItemIndex = exports.getActiveElement = exports.executeInTheNextEventLoopTick = void 0;\nexports.mergeSx = mergeSx;\nexports.onSpaceOrEnter = void 0;\n/* Use it instead of .includes method for IE support */\nfunction arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n  return array.indexOf(itemOrItems) !== -1;\n}\nconst onSpaceOrEnter = (innerFn, externalEvent) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event);\n\n    // prevent any side effects\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  if (externalEvent) {\n    externalEvent(event);\n  }\n};\nexports.onSpaceOrEnter = onSpaceOrEnter;\nconst executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\n\n// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexports.executeInTheNextEventLoopTick = executeInTheNextEventLoopTick;\nconst getActiveElement = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\n\n/**\n * Gets the index of the focused list item in a given ul list element.\n *\n * @param {HTMLUListElement} listElement - The list element to search within.\n * @returns {number} The index of the focused list item, or -1 if none is focused.\n */\nexports.getActiveElement = getActiveElement;\nconst getFocusedListItemIndex = listElement => {\n  const children = Array.from(listElement.children);\n  return children.indexOf(getActiveElement(document));\n};\nexports.getFocusedListItemIndex = getFocusedListItemIndex;\nconst DEFAULT_DESKTOP_MODE_MEDIA_QUERY = exports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = '@media (pointer: fine)';\nfunction mergeSx(...sxProps) {\n  return sxProps.reduce((acc, sxProp) => {\n    if (Array.isArray(sxProp)) {\n      acc.push(...sxProp);\n    } else if (sxProp != null) {\n      acc.push(sxProp);\n    }\n    return acc;\n  }, []);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "DEFAULT_DESKTOP_MODE_MEDIA_QUERY", "arrayIncludes", "getFocusedListItemIndex", "getActiveElement", "executeInTheNextEventLoopTick", "mergeSx", "onSpaceOrEnter", "array", "itemOrItems", "Array", "isArray", "every", "item", "indexOf", "innerFn", "externalEvent", "event", "key", "preventDefault", "stopPropagation", "fn", "setTimeout", "root", "document", "activeEl", "activeElement", "shadowRoot", "listElement", "children", "from", "sxProps", "reduce", "acc", "sxProp", "push"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/utils/utils.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = void 0;\nexports.arrayIncludes = arrayIncludes;\nexports.getFocusedListItemIndex = exports.getActiveElement = exports.executeInTheNextEventLoopTick = void 0;\nexports.mergeSx = mergeSx;\nexports.onSpaceOrEnter = void 0;\n/* Use it instead of .includes method for IE support */\nfunction arrayIncludes(array, itemOrItems) {\n  if (Array.isArray(itemOrItems)) {\n    return itemOrItems.every(item => array.indexOf(item) !== -1);\n  }\n  return array.indexOf(itemOrItems) !== -1;\n}\nconst onSpaceOrEnter = (innerFn, externalEvent) => event => {\n  if (event.key === 'Enter' || event.key === ' ') {\n    innerFn(event);\n\n    // prevent any side effects\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  if (externalEvent) {\n    externalEvent(event);\n  }\n};\nexports.onSpaceOrEnter = onSpaceOrEnter;\nconst executeInTheNextEventLoopTick = fn => {\n  setTimeout(fn, 0);\n};\n\n// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexports.executeInTheNextEventLoopTick = executeInTheNextEventLoopTick;\nconst getActiveElement = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\n\n/**\n * Gets the index of the focused list item in a given ul list element.\n *\n * @param {HTMLUListElement} listElement - The list element to search within.\n * @returns {number} The index of the focused list item, or -1 if none is focused.\n */\nexports.getActiveElement = getActiveElement;\nconst getFocusedListItemIndex = listElement => {\n  const children = Array.from(listElement.children);\n  return children.indexOf(getActiveElement(document));\n};\nexports.getFocusedListItemIndex = getFocusedListItemIndex;\nconst DEFAULT_DESKTOP_MODE_MEDIA_QUERY = exports.DEFAULT_DESKTOP_MODE_MEDIA_QUERY = '@media (pointer: fine)';\nfunction mergeSx(...sxProps) {\n  return sxProps.reduce((acc, sxProp) => {\n    if (Array.isArray(sxProp)) {\n      acc.push(...sxProp);\n    } else if (sxProp != null) {\n      acc.push(sxProp);\n    }\n    return acc;\n  }, []);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,gCAAgC,GAAG,KAAK,CAAC;AACjDF,OAAO,CAACG,aAAa,GAAGA,aAAa;AACrCH,OAAO,CAACI,uBAAuB,GAAGJ,OAAO,CAACK,gBAAgB,GAAGL,OAAO,CAACM,6BAA6B,GAAG,KAAK,CAAC;AAC3GN,OAAO,CAACO,OAAO,GAAGA,OAAO;AACzBP,OAAO,CAACQ,cAAc,GAAG,KAAK,CAAC;AAC/B;AACA,SAASL,aAAaA,CAACM,KAAK,EAAEC,WAAW,EAAE;EACzC,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAOA,WAAW,CAACG,KAAK,CAACC,IAAI,IAAIL,KAAK,CAACM,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOL,KAAK,CAACM,OAAO,CAACL,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1C;AACA,MAAMF,cAAc,GAAGA,CAACQ,OAAO,EAAEC,aAAa,KAAKC,KAAK,IAAI;EAC1D,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAID,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;IAC9CH,OAAO,CAACE,KAAK,CAAC;;IAEd;IACAA,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;EACzB;EACA,IAAIJ,aAAa,EAAE;IACjBA,aAAa,CAACC,KAAK,CAAC;EACtB;AACF,CAAC;AACDlB,OAAO,CAACQ,cAAc,GAAGA,cAAc;AACvC,MAAMF,6BAA6B,GAAGgB,EAAE,IAAI;EAC1CC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC;AACnB,CAAC;;AAED;AACAtB,OAAO,CAACM,6BAA6B,GAAGA,6BAA6B;AACrE,MAAMD,gBAAgB,GAAGA,CAACmB,IAAI,GAAGC,QAAQ,KAAK;EAC5C,MAAMC,QAAQ,GAAGF,IAAI,CAACG,aAAa;EACnC,IAAI,CAACD,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EACA,IAAIA,QAAQ,CAACE,UAAU,EAAE;IACvB,OAAOvB,gBAAgB,CAACqB,QAAQ,CAACE,UAAU,CAAC;EAC9C;EACA,OAAOF,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA1B,OAAO,CAACK,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,uBAAuB,GAAGyB,WAAW,IAAI;EAC7C,MAAMC,QAAQ,GAAGnB,KAAK,CAACoB,IAAI,CAACF,WAAW,CAACC,QAAQ,CAAC;EACjD,OAAOA,QAAQ,CAACf,OAAO,CAACV,gBAAgB,CAACoB,QAAQ,CAAC,CAAC;AACrD,CAAC;AACDzB,OAAO,CAACI,uBAAuB,GAAGA,uBAAuB;AACzD,MAAMF,gCAAgC,GAAGF,OAAO,CAACE,gCAAgC,GAAG,wBAAwB;AAC5G,SAASK,OAAOA,CAAC,GAAGyB,OAAO,EAAE;EAC3B,OAAOA,OAAO,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;IACrC,IAAIxB,KAAK,CAACC,OAAO,CAACuB,MAAM,CAAC,EAAE;MACzBD,GAAG,CAACE,IAAI,CAAC,GAAGD,MAAM,CAAC;IACrB,CAAC,MAAM,IAAIA,MAAM,IAAI,IAAI,EAAE;MACzBD,GAAG,CAACE,IAAI,CAACD,MAAM,CAAC;IAClB;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}