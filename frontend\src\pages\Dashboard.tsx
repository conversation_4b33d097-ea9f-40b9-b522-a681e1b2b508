import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  <PERSON><PERSON>,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Business,
  Person,
  AccountBalance,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { apiService } from '../services/apiService';
import StatCard from '../components/StatCard';
import RecentTransactions from '../components/RecentTransactions';
import TopCompaniesChart from '../components/TopCompaniesChart';
import TransactionTrendsChart from '../components/TransactionTrendsChart';
import LastFetchedStatus from '../components/LastFetchedStatus';

interface DashboardData {
  summary: {
    total_transactions: number;
    total_buy_value: number;
    total_sell_value: number;
    net_value: number;
    unique_companies: number;
    unique_persons: number;
    date_range: {
      earliest: string;
      latest: string;
    };
  };
  recent_transactions: any[];
  top_companies: any[];
  system_status: {
    api_status: string;
    database_status: {
      total_transactions: number;
      total_companies: number;
      date_range?: {
        earliest: string;
        latest: string;
      };
      last_updated?: string;
    };
    scraper_status: {
      last_execution?: string;
      status: string;
      records_fetched: number;
      records_inserted: number;
      records_skipped: number;
      error_message?: string;
    };
    timestamp: string;
  };
}

const Dashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch all dashboard data in parallel
        const [summaryResponse, recentTransactionsResponse, topCompaniesResponse, systemStatusResponse] = await Promise.all([
          apiService.getAnalyticsSummary(),
          apiService.getTransactions({ limit: 10, sort_by: 'transaction_date', sort_order: 'desc' }),
          apiService.getTopCompanies({ limit: 10 }),
          apiService.getSystemStatus(),
        ]);

        setData({
          summary: summaryResponse.data,
          recent_transactions: recentTransactionsResponse.data.transactions,
          top_companies: topCompaniesResponse.data,
          system_status: systemStatusResponse.data,
        });
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (!data) {
    return (
      <Box p={3}>
        <Alert severity="info">No data available</Alert>
      </Box>
    );
  }

  const { summary, recent_transactions, top_companies, system_status } = data;

  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined || isNaN(value)) {
      return '₹0';
    }

    const absValue = Math.abs(value);
    if (absValue >= 10000000) {
      return `₹${(value / 10000000).toFixed(1)}Cr`;
    } else if (absValue >= 100000) {
      return `₹${(value / 100000).toFixed(1)}L`;
    } else {
      return `₹${value.toLocaleString()}`;
    }
  };

  const netValue = summary.net_value;

  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Overview of NSE insider trading activities
        </Typography>
      </Box>

      {/* System Status */}
      <Box mb={3}>
        <LastFetchedStatus />
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
          <StatCard
            title="Total Transactions"
            value={summary.total_transactions.toLocaleString()}
            icon={<AccountBalance />}
            color="primary"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
          <StatCard
            title="Buy Value"
            value={formatCurrency(summary.total_buy_value)}
            icon={<TrendingUp />}
            color="success"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
          <StatCard
            title="Sell Value"
            value={formatCurrency(summary.total_sell_value)}
            icon={<TrendingDown />}
            color="error"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
          <StatCard
            title="Net Value"
            value={formatCurrency(Math.abs(netValue))}
            subtitle={netValue >= 0 ? 'Net Buying' : 'Net Selling'}
            icon={netValue >= 0 ? <TrendingUp /> : <TrendingDown />}
            color={netValue >= 0 ? 'success' : 'error'}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>
          <StatCard
            title="Companies"
            value={summary.unique_companies.toLocaleString()}
            icon={<Business />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        {/* Top Companies Chart */}
        <Grid size={{ xs: 12, lg: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Companies by Transaction Value
              </Typography>
              <TopCompaniesChart data={top_companies} />
            </CardContent>
          </Card>
        </Grid>

        {/* Transaction Trends */}
        <Grid size={{ xs: 12, lg: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Transaction Trends (Last 30 Days)
              </Typography>
              <TransactionTrendsChart />
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Transactions */}
        <Grid size={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Transactions
              </Typography>
              <RecentTransactions transactions={recent_transactions} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
