{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, CircularProgress, Alert } from '@mui/material';\nimport { TrendingUp, TrendingDown, Business, AccountBalance } from '@mui/icons-material';\nimport { apiService } from '../services/apiService';\nimport StatCard from '../components/StatCard';\nimport RecentTransactions from '../components/RecentTransactions';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport LastFetchedStatus from '../components/LastFetchedStatus';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Fetch all dashboard data in parallel\n        const [summaryResponse, recentTransactionsResponse, topCompaniesResponse, systemStatusResponse] = await Promise.all([apiService.getAnalyticsSummary(), apiService.getTransactions({\n          limit: 10,\n          sort_by: 'transaction_date',\n          sort_order: 'desc'\n        }), apiService.getTopCompanies({\n          limit: 10\n        }), apiService.getSystemStatus()]);\n        setData({\n          summary: summaryResponse.data,\n          recent_transactions: recentTransactionsResponse.data.transactions,\n          top_companies: topCompaniesResponse.data,\n          system_status: systemStatusResponse.data\n        });\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err);\n        setError('Failed to load dashboard data. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDashboardData();\n\n    // Set up auto-refresh every 5 minutes\n    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this);\n  }\n  if (!data) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"No data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  const {\n    summary,\n    recent_transactions,\n    top_companies,\n    system_status\n  } = data;\n  const formatCurrency = value => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n  const netValue = summary.net_value;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Overview of NSE insider trading activities\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(LastFetchedStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Transactions\",\n          value: summary.total_transactions.toLocaleString(),\n          icon: /*#__PURE__*/_jsxDEV(AccountBalance, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 19\n          }, this),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Buy Value\",\n          value: formatCurrency(summary.total_buy_value),\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 19\n          }, this),\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Sell Value\",\n          value: formatCurrency(summary.total_sell_value),\n          icon: /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 19\n          }, this),\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Net Value\",\n          value: formatCurrency(Math.abs(netValue)),\n          subtitle: netValue >= 0 ? 'Net Buying' : 'Net Selling',\n          icon: netValue >= 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 35\n          }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 52\n          }, this),\n          color: netValue >= 0 ? 'success' : 'error'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          sm: 6,\n          md: 2.4\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Companies\",\n          value: summary.unique_companies.toLocaleString(),\n          icon: /*#__PURE__*/_jsxDEV(Business, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 19\n          }, this),\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          lg: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Top Companies by Transaction Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TopCompaniesChart, {\n              data: top_companies\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: {\n          xs: 12,\n          lg: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Transaction Trends (Last 30 Days)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TransactionTrendsChart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        size: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Recent Transactions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecentTransactions, {\n              transactions: recent_transactions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"RiL7vLwmC7ZWXKL/bXt2EIBjBYk=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "CircularProgress", "<PERSON><PERSON>", "TrendingUp", "TrendingDown", "Business", "AccountBalance", "apiService", "StatCard", "RecentTransactions", "TopCompaniesChart", "TransactionTrendsChart", "LastFetchedStatus", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "fetchDashboardData", "summaryResponse", "recentTransactionsResponse", "topCompaniesResponse", "systemStatusResponse", "Promise", "all", "getAnalyticsSummary", "getTransactions", "limit", "sort_by", "sort_order", "getTopCompanies", "getSystemStatus", "summary", "recent_transactions", "transactions", "top_companies", "system_status", "err", "console", "interval", "setInterval", "clearInterval", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "severity", "formatCurrency", "value", "undefined", "isNaN", "absValue", "Math", "abs", "toFixed", "toLocaleString", "netValue", "net_value", "mb", "variant", "gutterBottom", "color", "container", "spacing", "sx", "xs", "sm", "md", "title", "total_transactions", "icon", "total_buy_value", "total_sell_value", "subtitle", "unique_companies", "lg", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  CircularProgress,\n  <PERSON><PERSON>,\n  Chip,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Business,\n  Person,\n  AccountBalance,\n} from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport { apiService } from '../services/apiService';\nimport StatCard from '../components/StatCard';\nimport RecentTransactions from '../components/RecentTransactions';\nimport TopCompaniesChart from '../components/TopCompaniesChart';\nimport TransactionTrendsChart from '../components/TransactionTrendsChart';\nimport LastFetchedStatus from '../components/LastFetchedStatus';\n\ninterface DashboardData {\n  summary: {\n    total_transactions: number;\n    total_buy_value: number;\n    total_sell_value: number;\n    net_value: number;\n    unique_companies: number;\n    unique_persons: number;\n    date_range: {\n      earliest: string;\n      latest: string;\n    };\n  };\n  recent_transactions: any[];\n  top_companies: any[];\n  system_status: {\n    api_status: string;\n    database_status: {\n      total_transactions: number;\n      total_companies: number;\n      date_range?: {\n        earliest: string;\n        latest: string;\n      };\n      last_updated?: string;\n    };\n    scraper_status: {\n      last_execution?: string;\n      status: string;\n      records_fetched: number;\n      records_inserted: number;\n      records_skipped: number;\n      error_message?: string;\n    };\n    timestamp: string;\n  };\n}\n\nconst Dashboard: React.FC = () => {\n  const [data, setData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Fetch all dashboard data in parallel\n        const [summaryResponse, recentTransactionsResponse, topCompaniesResponse, systemStatusResponse] = await Promise.all([\n          apiService.getAnalyticsSummary(),\n          apiService.getTransactions({ limit: 10, sort_by: 'transaction_date', sort_order: 'desc' }),\n          apiService.getTopCompanies({ limit: 10 }),\n          apiService.getSystemStatus(),\n        ]);\n\n        setData({\n          summary: summaryResponse.data,\n          recent_transactions: recentTransactionsResponse.data.transactions,\n          top_companies: topCompaniesResponse.data,\n          system_status: systemStatusResponse.data,\n        });\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err);\n        setError('Failed to load dashboard data. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n\n    // Set up auto-refresh every 5 minutes\n    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  if (!data) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"info\">No data available</Alert>\n      </Box>\n    );\n  }\n\n  const { summary, recent_transactions, top_companies, system_status } = data;\n\n  const formatCurrency = (value: number | null | undefined) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return '₹0';\n    }\n\n    const absValue = Math.abs(value);\n    if (absValue >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (absValue >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  const netValue = summary.net_value;\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Dashboard\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Overview of NSE insider trading activities\n        </Typography>\n      </Box>\n\n      {/* System Status */}\n      <Box mb={3}>\n        <LastFetchedStatus />\n      </Box>\n\n      {/* Key Metrics */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Total Transactions\"\n            value={summary.total_transactions.toLocaleString()}\n            icon={<AccountBalance />}\n            color=\"primary\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Buy Value\"\n            value={formatCurrency(summary.total_buy_value)}\n            icon={<TrendingUp />}\n            color=\"success\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Sell Value\"\n            value={formatCurrency(summary.total_sell_value)}\n            icon={<TrendingDown />}\n            color=\"error\"\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Net Value\"\n            value={formatCurrency(Math.abs(netValue))}\n            subtitle={netValue >= 0 ? 'Net Buying' : 'Net Selling'}\n            icon={netValue >= 0 ? <TrendingUp /> : <TrendingDown />}\n            color={netValue >= 0 ? 'success' : 'error'}\n          />\n        </Grid>\n        <Grid size={{ xs: 12, sm: 6, md: 2.4 }}>\n          <StatCard\n            title=\"Companies\"\n            value={summary.unique_companies.toLocaleString()}\n            icon={<Business />}\n            color=\"info\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Charts and Tables */}\n      <Grid container spacing={3}>\n        {/* Top Companies Chart */}\n        <Grid size={{ xs: 12, lg: 6 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Top Companies by Transaction Value\n              </Typography>\n              <TopCompaniesChart data={top_companies} />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Transaction Trends */}\n        <Grid size={{ xs: 12, lg: 6 }}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Transaction Trends (Last 30 Days)\n              </Typography>\n              <TransactionTrendsChart />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Recent Transactions */}\n        <Grid size={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Recent Transactions\n              </Typography>\n              <RecentTransactions transactions={recent_transactions} />\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,QAEA,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,QAAQ,EAERC,cAAc,QACT,qBAAqB;AAE5B,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAwChE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAuB,IAAI,CAAC;EAC5D,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAM4B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,MAAM,CAACE,eAAe,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,oBAAoB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClHtB,UAAU,CAACuB,mBAAmB,CAAC,CAAC,EAChCvB,UAAU,CAACwB,eAAe,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE,kBAAkB;UAAEC,UAAU,EAAE;QAAO,CAAC,CAAC,EAC1F3B,UAAU,CAAC4B,eAAe,CAAC;UAAEH,KAAK,EAAE;QAAG,CAAC,CAAC,EACzCzB,UAAU,CAAC6B,eAAe,CAAC,CAAC,CAC7B,CAAC;QAEFlB,OAAO,CAAC;UACNmB,OAAO,EAAEb,eAAe,CAACP,IAAI;UAC7BqB,mBAAmB,EAAEb,0BAA0B,CAACR,IAAI,CAACsB,YAAY;UACjEC,aAAa,EAAEd,oBAAoB,CAACT,IAAI;UACxCwB,aAAa,EAAEd,oBAAoB,CAACV;QACtC,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOyB,GAAG,EAAE;QACZC,OAAO,CAACtB,KAAK,CAAC,gCAAgC,EAAEqB,GAAG,CAAC;QACpDpB,QAAQ,CAAC,kDAAkD,CAAC;MAC9D,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,kBAAkB,CAAC,CAAC;;IAEpB;IACA,MAAMqB,QAAQ,GAAGC,WAAW,CAACtB,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAC/D,OAAO,MAAMuB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIzB,OAAO,EAAE;IACX,oBACEL,OAAA,CAAClB,GAAG;MAACmD,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/ErC,OAAA,CAACb,gBAAgB;QAACmD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAInC,KAAK,EAAE;IACT,oBACEP,OAAA,CAAClB,GAAG;MAAC6D,CAAC,EAAE,CAAE;MAAAN,QAAA,eACRrC,OAAA,CAACZ,KAAK;QAACwD,QAAQ,EAAC,OAAO;QAAAP,QAAA,EAAE9B;MAAK;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,IAAI,CAACvC,IAAI,EAAE;IACT,oBACEH,OAAA,CAAClB,GAAG;MAAC6D,CAAC,EAAE,CAAE;MAAAN,QAAA,eACRrC,OAAA,CAACZ,KAAK;QAACwD,QAAQ,EAAC,MAAM;QAAAP,QAAA,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;EAEA,MAAM;IAAEnB,OAAO;IAAEC,mBAAmB;IAAEE,aAAa;IAAEC;EAAc,CAAC,GAAGxB,IAAI;EAE3E,MAAM0C,cAAc,GAAIC,KAAgC,IAAK;IAC3D,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIC,KAAK,CAACF,KAAK,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC;IAChC,IAAIG,QAAQ,IAAI,QAAQ,EAAE;MACxB,OAAO,IAAI,CAACH,KAAK,GAAG,QAAQ,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI;IAC9C,CAAC,MAAM,IAAIH,QAAQ,IAAI,MAAM,EAAE;MAC7B,OAAO,IAAI,CAACH,KAAK,GAAG,MAAM,EAAEM,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3C,CAAC,MAAM;MACL,OAAO,IAAIN,KAAK,CAACO,cAAc,CAAC,CAAC,EAAE;IACrC;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG/B,OAAO,CAACgC,SAAS;EAElC,oBACEvD,OAAA,CAAClB,GAAG;IAAAuD,QAAA,gBAEFrC,OAAA,CAAClB,GAAG;MAAC0E,EAAE,EAAE,CAAE;MAAAnB,QAAA,gBACTrC,OAAA,CAACd,UAAU;QAACuE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1C,OAAA,CAACd,UAAU;QAACuE,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,gBAAgB;QAAAtB,QAAA,EAAC;MAEnD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN1C,OAAA,CAAClB,GAAG;MAAC0E,EAAE,EAAE,CAAE;MAAAnB,QAAA,eACTrC,OAAA,CAACF,iBAAiB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGN1C,OAAA,CAACjB,IAAI;MAAC6E,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBACxCrC,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEyB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAA5B,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPwE,KAAK,EAAC,oBAAoB;UAC1BpB,KAAK,EAAEvB,OAAO,CAAC4C,kBAAkB,CAACd,cAAc,CAAC,CAAE;UACnDe,IAAI,eAAEpE,OAAA,CAACR,cAAc;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBiB,KAAK,EAAC;QAAS;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEyB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAA5B,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPwE,KAAK,EAAC,WAAW;UACjBpB,KAAK,EAAED,cAAc,CAACtB,OAAO,CAAC8C,eAAe,CAAE;UAC/CD,IAAI,eAAEpE,OAAA,CAACX,UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBiB,KAAK,EAAC;QAAS;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEyB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAA5B,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPwE,KAAK,EAAC,YAAY;UAClBpB,KAAK,EAAED,cAAc,CAACtB,OAAO,CAAC+C,gBAAgB,CAAE;UAChDF,IAAI,eAAEpE,OAAA,CAACV,YAAY;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBiB,KAAK,EAAC;QAAO;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEyB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAA5B,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPwE,KAAK,EAAC,WAAW;UACjBpB,KAAK,EAAED,cAAc,CAACK,IAAI,CAACC,GAAG,CAACG,QAAQ,CAAC,CAAE;UAC1CiB,QAAQ,EAAEjB,QAAQ,IAAI,CAAC,GAAG,YAAY,GAAG,aAAc;UACvDc,IAAI,EAAEd,QAAQ,IAAI,CAAC,gBAAGtD,OAAA,CAACX,UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG1C,OAAA,CAACV,YAAY;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxDiB,KAAK,EAAEL,QAAQ,IAAI,CAAC,GAAG,SAAS,GAAG;QAAQ;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEyB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAA5B,QAAA,eACrCrC,OAAA,CAACN,QAAQ;UACPwE,KAAK,EAAC,WAAW;UACjBpB,KAAK,EAAEvB,OAAO,CAACiD,gBAAgB,CAACnB,cAAc,CAAC,CAAE;UACjDe,IAAI,eAAEpE,OAAA,CAACT,QAAQ;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBiB,KAAK,EAAC;QAAM;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1C,OAAA,CAACjB,IAAI;MAAC6E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAxB,QAAA,gBAEzBrC,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEyB,EAAE,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAApC,QAAA,eAC5BrC,OAAA,CAAChB,IAAI;UAAAqD,QAAA,eACHrC,OAAA,CAACf,WAAW;YAAAoD,QAAA,gBACVrC,OAAA,CAACd,UAAU;cAACuE,OAAO,EAAC,IAAI;cAACC,YAAY;cAAArB,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACJ,iBAAiB;cAACO,IAAI,EAAEuB;YAAc;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE;UAAEyB,EAAE,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAApC,QAAA,eAC5BrC,OAAA,CAAChB,IAAI;UAAAqD,QAAA,eACHrC,OAAA,CAACf,WAAW;YAAAoD,QAAA,gBACVrC,OAAA,CAACd,UAAU;cAACuE,OAAO,EAAC,IAAI;cAACC,YAAY;cAAArB,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACH,sBAAsB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP1C,OAAA,CAACjB,IAAI;QAACuD,IAAI,EAAE,EAAG;QAAAD,QAAA,eACbrC,OAAA,CAAChB,IAAI;UAAAqD,QAAA,eACHrC,OAAA,CAACf,WAAW;YAAAoD,QAAA,gBACVrC,OAAA,CAACd,UAAU;cAACuE,OAAO,EAAC,IAAI;cAACC,YAAY;cAAArB,QAAA,EAAC;YAEtC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1C,OAAA,CAACL,kBAAkB;cAAC8B,YAAY,EAAED;YAAoB;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxC,EAAA,CAzLID,SAAmB;AAAAyE,EAAA,GAAnBzE,SAAmB;AA2LzB,eAAeA,SAAS;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}