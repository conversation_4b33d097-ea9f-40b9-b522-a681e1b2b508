{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldV7TextField = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useEnhancedEffect = _interopRequireDefault(require(\"@mui/utils/useEnhancedEffect\"));\nvar _useField = require(\"./useField.utils\");\nvar _utils = require(\"../../utils/utils\");\nvar _hooks = require(\"../../../hooks\");\nvar _useFieldCharacterEditing = require(\"./useFieldCharacterEditing\");\nvar _useFieldState = require(\"./useFieldState\");\nvar _useFieldInternalPropsWithDefaults = require(\"./useFieldInternalPropsWithDefaults\");\nvar _syncSelectionToDOM = require(\"./syncSelectionToDOM\");\nvar _useFieldRootProps = require(\"./useFieldRootProps\");\nvar _useFieldHiddenInputProps = require(\"./useFieldHiddenInputProps\");\nvar _useFieldSectionContainerProps = require(\"./useFieldSectionContainerProps\");\nvar _useFieldSectionContentProps = require(\"./useFieldSectionContentProps\");\nconst useFieldV7TextField = parameters => {\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = (0, _hooks.useSplitFieldProps)(props, valueType);\n  const internalPropsWithDefaults = (0, _useFieldInternalPropsWithDefaults.useFieldInternalPropsWithDefaults)({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    sectionListRef: sectionListRefProp,\n    onBlur,\n    onClick,\n    onFocus,\n    onInput,\n    onPaste,\n    onKeyDown,\n    onClear,\n    clearable\n  } = forwardedProps;\n  const {\n    disabled = false,\n    readOnly = false,\n    autoFocus = false,\n    focused: focusedProp,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const sectionListRef = React.useRef(null);\n  const handleSectionListRef = (0, _useForkRef.default)(sectionListRefProp, sectionListRef);\n  const domGetters = React.useMemo(() => ({\n    isReady: () => sectionListRef.current != null,\n    getRoot: () => sectionListRef.current.getRoot(),\n    getSectionContainer: sectionIndex => sectionListRef.current.getSectionContainer(sectionIndex),\n    getSectionContent: sectionIndex => sectionListRef.current.getSectionContent(sectionIndex),\n    getSectionIndexFromDOMElement: element => sectionListRef.current.getSectionIndexFromDOMElement(element)\n  }), [sectionListRef]);\n  const stateResponse = (0, _useFieldState.useFieldState)({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    areAllSectionsEmpty,\n    error,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    setSelectedSections\n  } = stateResponse;\n  const applyCharacterEditing = (0, _useFieldCharacterEditing.useFieldCharacterEditing)({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const [focused, setFocused] = React.useState(false);\n  function focusField(newSelectedSections = 0) {\n    if (disabled || !sectionListRef.current ||\n    // if the field is already focused, we don't need to focus it again\n    getActiveSectionIndex(sectionListRef) != null) {\n      return;\n    }\n    const newParsedSelectedSections = (0, _useField.parseSelectedSections)(newSelectedSections, state.sections);\n    setFocused(true);\n    sectionListRef.current.getSectionContent(newParsedSelectedSections).focus();\n  }\n  const rootProps = (0, _useFieldRootProps.useFieldRootProps)({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse,\n    applyCharacterEditing,\n    focused,\n    setFocused,\n    domGetters\n  });\n  const hiddenInputProps = (0, _useFieldHiddenInputProps.useFieldHiddenInputProps)({\n    manager,\n    stateResponse\n  });\n  const createSectionContainerProps = (0, _useFieldSectionContainerProps.useFieldSectionContainerProps)({\n    stateResponse,\n    internalPropsWithDefaults\n  });\n  const createSectionContentProps = (0, _useFieldSectionContentProps.useFieldSectionContentProps)({\n    manager,\n    stateResponse,\n    applyCharacterEditing,\n    internalPropsWithDefaults,\n    domGetters,\n    focused\n  });\n  const handleRootKeyDown = (0, _useEventCallback.default)(event => {\n    onKeyDown?.(event);\n    rootProps.onKeyDown(event);\n  });\n  const handleRootBlur = (0, _useEventCallback.default)(event => {\n    onBlur?.(event);\n    rootProps.onBlur(event);\n  });\n  const handleRootFocus = (0, _useEventCallback.default)(event => {\n    onFocus?.(event);\n    rootProps.onFocus(event);\n  });\n  const handleRootClick = (0, _useEventCallback.default)(event => {\n    // The click event on the clear or open button would propagate to the input, trigger this handler and result in an inadvertent section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a propagated call, which should be skipped.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event);\n    rootProps.onClick(event);\n  });\n  const handleRootPaste = (0, _useEventCallback.default)(event => {\n    onPaste?.(event);\n    rootProps.onPaste(event);\n  });\n  const handleRootInput = (0, _useEventCallback.default)(event => {\n    onInput?.(event);\n    rootProps.onInput(event);\n  });\n  const handleClear = (0, _useEventCallback.default)((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!isFieldFocused(sectionListRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const elements = React.useMemo(() => {\n    return state.sections.map((section, sectionIndex) => {\n      const content = createSectionContentProps(section, sectionIndex);\n      return {\n        container: createSectionContainerProps(sectionIndex),\n        content: createSectionContentProps(section, sectionIndex),\n        before: {\n          children: section.startSeparator\n        },\n        after: {\n          children: section.endSeparator,\n          'data-range-position': section.isEndFormatSeparator ? content['data-range-position'] : undefined\n        }\n      };\n    });\n  }, [state.sections, createSectionContainerProps, createSectionContentProps]);\n  React.useEffect(() => {\n    if (sectionListRef.current == null) {\n      throw new Error(['MUI X: The `sectionListRef` prop has not been initialized by `PickersSectionList`', 'You probably tried to pass a component to the `textField` slot that contains an `<input />` element instead of a `PickersSectionList`.', '', 'If you want to keep using an `<input />` HTML element for the editing, please add the `enableAccessibleFieldDOMStructure={false}` prop to your Picker or Field component:', '', '<DatePicker enableAccessibleFieldDOMStructure={false} slots={{ textField: MyCustomTextField }} />', '', 'Learn more about the field accessible DOM structure on the MUI documentation: https://mui.com/x/react-date-pickers/fields/#fields-to-edit-a-single-element'].join('\\n'));\n    }\n    if (autoFocus && !disabled && sectionListRef.current) {\n      sectionListRef.current.getSectionContent(sectionOrder.startIndex).focus();\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  (0, _useEnhancedEffect.default)(() => {\n    if (!focused || !sectionListRef.current) {\n      return;\n    }\n    if (parsedSelectedSections === 'all') {\n      sectionListRef.current.getRoot().focus();\n    } else if (typeof parsedSelectedSections === 'number') {\n      const domElement = sectionListRef.current.getSectionContent(parsedSelectedSections);\n      if (domElement) {\n        domElement.focus();\n      }\n    }\n  }, [parsedSelectedSections, focused]);\n  (0, _useEnhancedEffect.default)(() => {\n    (0, _syncSelectionToDOM.syncSelectionToDOM)({\n      focused,\n      domGetters,\n      stateResponse\n    });\n  });\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => getActiveSectionIndex(sectionListRef),\n    setSelectedSections: newSelectedSections => {\n      if (disabled || !sectionListRef.current) {\n        return;\n      }\n      const newParsedSelectedSections = (0, _useField.parseSelectedSections)(newSelectedSections, state.sections);\n      const newActiveSectionIndex = newParsedSelectedSections === 'all' ? 0 : newParsedSelectedSections;\n      setFocused(newActiveSectionIndex !== null);\n      setSelectedSections(newSelectedSections);\n    },\n    focusField,\n    isFieldFocused: () => isFieldFocused(sectionListRef)\n  }));\n  return (0, _extends2.default)({}, forwardedProps, rootProps, {\n    onBlur: handleRootBlur,\n    onClick: handleRootClick,\n    onFocus: handleRootFocus,\n    onInput: handleRootInput,\n    onPaste: handleRootPaste,\n    onKeyDown: handleRootKeyDown,\n    onClear: handleClear\n  }, hiddenInputProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    focused: focusedProp ?? focused,\n    sectionListRef: handleSectionListRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: true,\n    elements,\n    areAllSectionsEmpty,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nexports.useFieldV7TextField = useFieldV7TextField;\nfunction getActiveSectionIndex(sectionListRef) {\n  const activeElement = (0, _utils.getActiveElement)(document);\n  if (!activeElement || !sectionListRef.current || !sectionListRef.current.getRoot().contains(activeElement)) {\n    return null;\n  }\n  return sectionListRef.current.getSectionIndexFromDOMElement(activeElement);\n}\nfunction isFieldFocused(sectionListRef) {\n  const activeElement = (0, _utils.getActiveElement)(document);\n  return !!sectionListRef.current && sectionListRef.current.getRoot().contains(activeElement);\n}", "map": {"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "Object", "defineProperty", "exports", "value", "useFieldV7TextField", "_extends2", "React", "_useForkRef", "_useEventCallback", "_useEnhancedEffect", "_useField", "_utils", "_hooks", "_useFieldCharacterEditing", "_useFieldState", "_useFieldInternalPropsWithDefaults", "_syncSelectionToDOM", "_useFieldRootProps", "_useFieldHiddenInputProps", "_useFieldSectionContainerProps", "_useFieldSectionContentProps", "parameters", "props", "manager", "skipContextFieldRefAssignment", "valueType", "internal_useOpenPickerButtonAriaLabel", "useOpenPickerButtonAriaLabel", "internalProps", "forwardedProps", "useSplitFieldProps", "internalPropsWithDefaults", "useFieldInternalPropsWithDefaults", "sectionListRef", "sectionListRefProp", "onBlur", "onClick", "onFocus", "onInput", "onPaste", "onKeyDown", "onClear", "clearable", "disabled", "readOnly", "autoFocus", "focused", "focusedProp", "unstableFieldRef", "useRef", "handleSectionListRef", "domGetters", "useMemo", "isReady", "current", "getRoot", "getSectionContainer", "sectionIndex", "getSectionContent", "getSectionIndexFromDOMElement", "element", "stateResponse", "useFieldState", "areAllSectionsEmpty", "error", "parsedSelectedSections", "sectionOrder", "state", "clearValue", "setSelectedSections", "applyCharacterEditing", "useFieldCharacterEditing", "openPickerAriaLabel", "setFocused", "useState", "focusField", "newSelectedSections", "getActiveSectionIndex", "newParsedSelectedSections", "parseSelectedSections", "sections", "focus", "rootProps", "useFieldRootProps", "hiddenInputProps", "useFieldHiddenInputProps", "createSectionContainerProps", "useFieldSectionContainerProps", "createSectionContentProps", "useFieldSectionContentProps", "handleRootKeyDown", "event", "handleRootBlur", "handleRootFocus", "handleRootClick", "isDefaultPrevented", "handleRootPaste", "handleRootInput", "handleClear", "args", "preventDefault", "isFieldFocused", "startIndex", "elements", "map", "section", "content", "container", "before", "children", "startSeparator", "after", "endSeparator", "isEndFormatSeparator", "undefined", "useEffect", "Error", "join", "dom<PERSON>lement", "syncSelectionToDOM", "useImperativeHandle", "getSections", "newActiveSectionIndex", "Boolean", "enableAccessibleFieldDOMStructure", "activeElement", "getActiveElement", "document", "contains"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldV7TextField.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useFieldV7TextField = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useForkRef = _interopRequireDefault(require(\"@mui/utils/useForkRef\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useEnhancedEffect = _interopRequireDefault(require(\"@mui/utils/useEnhancedEffect\"));\nvar _useField = require(\"./useField.utils\");\nvar _utils = require(\"../../utils/utils\");\nvar _hooks = require(\"../../../hooks\");\nvar _useFieldCharacterEditing = require(\"./useFieldCharacterEditing\");\nvar _useFieldState = require(\"./useFieldState\");\nvar _useFieldInternalPropsWithDefaults = require(\"./useFieldInternalPropsWithDefaults\");\nvar _syncSelectionToDOM = require(\"./syncSelectionToDOM\");\nvar _useFieldRootProps = require(\"./useFieldRootProps\");\nvar _useFieldHiddenInputProps = require(\"./useFieldHiddenInputProps\");\nvar _useFieldSectionContainerProps = require(\"./useFieldSectionContainerProps\");\nvar _useFieldSectionContentProps = require(\"./useFieldSectionContentProps\");\nconst useFieldV7TextField = parameters => {\n  const {\n    props,\n    manager,\n    skipContextFieldRefAssignment,\n    manager: {\n      valueType,\n      internal_useOpenPickerButtonAriaLabel: useOpenPickerButtonAriaLabel\n    }\n  } = parameters;\n  const {\n    internalProps,\n    forwardedProps\n  } = (0, _hooks.useSplitFieldProps)(props, valueType);\n  const internalPropsWithDefaults = (0, _useFieldInternalPropsWithDefaults.useFieldInternalPropsWithDefaults)({\n    manager,\n    internalProps,\n    skipContextFieldRefAssignment\n  });\n  const {\n    sectionListRef: sectionListRefProp,\n    onBlur,\n    onClick,\n    onFocus,\n    onInput,\n    onPaste,\n    onKeyDown,\n    onClear,\n    clearable\n  } = forwardedProps;\n  const {\n    disabled = false,\n    readOnly = false,\n    autoFocus = false,\n    focused: focusedProp,\n    unstableFieldRef\n  } = internalPropsWithDefaults;\n  const sectionListRef = React.useRef(null);\n  const handleSectionListRef = (0, _useForkRef.default)(sectionListRefProp, sectionListRef);\n  const domGetters = React.useMemo(() => ({\n    isReady: () => sectionListRef.current != null,\n    getRoot: () => sectionListRef.current.getRoot(),\n    getSectionContainer: sectionIndex => sectionListRef.current.getSectionContainer(sectionIndex),\n    getSectionContent: sectionIndex => sectionListRef.current.getSectionContent(sectionIndex),\n    getSectionIndexFromDOMElement: element => sectionListRef.current.getSectionIndexFromDOMElement(element)\n  }), [sectionListRef]);\n  const stateResponse = (0, _useFieldState.useFieldState)({\n    manager,\n    internalPropsWithDefaults,\n    forwardedProps\n  });\n  const {\n    // States and derived states\n    areAllSectionsEmpty,\n    error,\n    parsedSelectedSections,\n    sectionOrder,\n    state,\n    value,\n    // Methods to update the states\n    clearValue,\n    setSelectedSections\n  } = stateResponse;\n  const applyCharacterEditing = (0, _useFieldCharacterEditing.useFieldCharacterEditing)({\n    stateResponse\n  });\n  const openPickerAriaLabel = useOpenPickerButtonAriaLabel(value);\n  const [focused, setFocused] = React.useState(false);\n  function focusField(newSelectedSections = 0) {\n    if (disabled || !sectionListRef.current ||\n    // if the field is already focused, we don't need to focus it again\n    getActiveSectionIndex(sectionListRef) != null) {\n      return;\n    }\n    const newParsedSelectedSections = (0, _useField.parseSelectedSections)(newSelectedSections, state.sections);\n    setFocused(true);\n    sectionListRef.current.getSectionContent(newParsedSelectedSections).focus();\n  }\n  const rootProps = (0, _useFieldRootProps.useFieldRootProps)({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse,\n    applyCharacterEditing,\n    focused,\n    setFocused,\n    domGetters\n  });\n  const hiddenInputProps = (0, _useFieldHiddenInputProps.useFieldHiddenInputProps)({\n    manager,\n    stateResponse\n  });\n  const createSectionContainerProps = (0, _useFieldSectionContainerProps.useFieldSectionContainerProps)({\n    stateResponse,\n    internalPropsWithDefaults\n  });\n  const createSectionContentProps = (0, _useFieldSectionContentProps.useFieldSectionContentProps)({\n    manager,\n    stateResponse,\n    applyCharacterEditing,\n    internalPropsWithDefaults,\n    domGetters,\n    focused\n  });\n  const handleRootKeyDown = (0, _useEventCallback.default)(event => {\n    onKeyDown?.(event);\n    rootProps.onKeyDown(event);\n  });\n  const handleRootBlur = (0, _useEventCallback.default)(event => {\n    onBlur?.(event);\n    rootProps.onBlur(event);\n  });\n  const handleRootFocus = (0, _useEventCallback.default)(event => {\n    onFocus?.(event);\n    rootProps.onFocus(event);\n  });\n  const handleRootClick = (0, _useEventCallback.default)(event => {\n    // The click event on the clear or open button would propagate to the input, trigger this handler and result in an inadvertent section selection.\n    // We avoid this by checking if the call of `handleInputClick` is actually intended, or a propagated call, which should be skipped.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    onClick?.(event);\n    rootProps.onClick(event);\n  });\n  const handleRootPaste = (0, _useEventCallback.default)(event => {\n    onPaste?.(event);\n    rootProps.onPaste(event);\n  });\n  const handleRootInput = (0, _useEventCallback.default)(event => {\n    onInput?.(event);\n    rootProps.onInput(event);\n  });\n  const handleClear = (0, _useEventCallback.default)((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!isFieldFocused(sectionListRef)) {\n      // setSelectedSections is called internally\n      focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const elements = React.useMemo(() => {\n    return state.sections.map((section, sectionIndex) => {\n      const content = createSectionContentProps(section, sectionIndex);\n      return {\n        container: createSectionContainerProps(sectionIndex),\n        content: createSectionContentProps(section, sectionIndex),\n        before: {\n          children: section.startSeparator\n        },\n        after: {\n          children: section.endSeparator,\n          'data-range-position': section.isEndFormatSeparator ? content['data-range-position'] : undefined\n        }\n      };\n    });\n  }, [state.sections, createSectionContainerProps, createSectionContentProps]);\n  React.useEffect(() => {\n    if (sectionListRef.current == null) {\n      throw new Error(['MUI X: The `sectionListRef` prop has not been initialized by `PickersSectionList`', 'You probably tried to pass a component to the `textField` slot that contains an `<input />` element instead of a `PickersSectionList`.', '', 'If you want to keep using an `<input />` HTML element for the editing, please add the `enableAccessibleFieldDOMStructure={false}` prop to your Picker or Field component:', '', '<DatePicker enableAccessibleFieldDOMStructure={false} slots={{ textField: MyCustomTextField }} />', '', 'Learn more about the field accessible DOM structure on the MUI documentation: https://mui.com/x/react-date-pickers/fields/#fields-to-edit-a-single-element'].join('\\n'));\n    }\n    if (autoFocus && !disabled && sectionListRef.current) {\n      sectionListRef.current.getSectionContent(sectionOrder.startIndex).focus();\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  (0, _useEnhancedEffect.default)(() => {\n    if (!focused || !sectionListRef.current) {\n      return;\n    }\n    if (parsedSelectedSections === 'all') {\n      sectionListRef.current.getRoot().focus();\n    } else if (typeof parsedSelectedSections === 'number') {\n      const domElement = sectionListRef.current.getSectionContent(parsedSelectedSections);\n      if (domElement) {\n        domElement.focus();\n      }\n    }\n  }, [parsedSelectedSections, focused]);\n  (0, _useEnhancedEffect.default)(() => {\n    (0, _syncSelectionToDOM.syncSelectionToDOM)({\n      focused,\n      domGetters,\n      stateResponse\n    });\n  });\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: () => getActiveSectionIndex(sectionListRef),\n    setSelectedSections: newSelectedSections => {\n      if (disabled || !sectionListRef.current) {\n        return;\n      }\n      const newParsedSelectedSections = (0, _useField.parseSelectedSections)(newSelectedSections, state.sections);\n      const newActiveSectionIndex = newParsedSelectedSections === 'all' ? 0 : newParsedSelectedSections;\n      setFocused(newActiveSectionIndex !== null);\n      setSelectedSections(newSelectedSections);\n    },\n    focusField,\n    isFieldFocused: () => isFieldFocused(sectionListRef)\n  }));\n  return (0, _extends2.default)({}, forwardedProps, rootProps, {\n    onBlur: handleRootBlur,\n    onClick: handleRootClick,\n    onFocus: handleRootFocus,\n    onInput: handleRootInput,\n    onPaste: handleRootPaste,\n    onKeyDown: handleRootKeyDown,\n    onClear: handleClear\n  }, hiddenInputProps, {\n    error,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled),\n    focused: focusedProp ?? focused,\n    sectionListRef: handleSectionListRef,\n    // Additional\n    enableAccessibleFieldDOMStructure: true,\n    elements,\n    areAllSectionsEmpty,\n    disabled,\n    readOnly,\n    autoFocus,\n    openPickerAriaLabel\n  });\n};\nexports.useFieldV7TextField = useFieldV7TextField;\nfunction getActiveSectionIndex(sectionListRef) {\n  const activeElement = (0, _utils.getActiveElement)(document);\n  if (!activeElement || !sectionListRef.current || !sectionListRef.current.getRoot().contains(activeElement)) {\n    return null;\n  }\n  return sectionListRef.current.getSectionIndexFromDOMElement(activeElement);\n}\nfunction isFieldFocused(sectionListRef) {\n  const activeElement = (0, _utils.getActiveElement)(document);\n  return !!sectionListRef.current && sectionListRef.current.getRoot().contains(activeElement);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,mBAAmB,GAAG,KAAK,CAAC;AACpC,IAAIC,SAAS,GAAGN,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIS,KAAK,GAAGV,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIU,WAAW,GAAGR,sBAAsB,CAACF,OAAO,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAIW,iBAAiB,GAAGT,sBAAsB,CAACF,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIY,kBAAkB,GAAGV,sBAAsB,CAACF,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACxF,IAAIa,SAAS,GAAGb,OAAO,CAAC,kBAAkB,CAAC;AAC3C,IAAIc,MAAM,GAAGd,OAAO,CAAC,mBAAmB,CAAC;AACzC,IAAIe,MAAM,GAAGf,OAAO,CAAC,gBAAgB,CAAC;AACtC,IAAIgB,yBAAyB,GAAGhB,OAAO,CAAC,4BAA4B,CAAC;AACrE,IAAIiB,cAAc,GAAGjB,OAAO,CAAC,iBAAiB,CAAC;AAC/C,IAAIkB,kCAAkC,GAAGlB,OAAO,CAAC,qCAAqC,CAAC;AACvF,IAAImB,mBAAmB,GAAGnB,OAAO,CAAC,sBAAsB,CAAC;AACzD,IAAIoB,kBAAkB,GAAGpB,OAAO,CAAC,qBAAqB,CAAC;AACvD,IAAIqB,yBAAyB,GAAGrB,OAAO,CAAC,4BAA4B,CAAC;AACrE,IAAIsB,8BAA8B,GAAGtB,OAAO,CAAC,iCAAiC,CAAC;AAC/E,IAAIuB,4BAA4B,GAAGvB,OAAO,CAAC,+BAA+B,CAAC;AAC3E,MAAMO,mBAAmB,GAAGiB,UAAU,IAAI;EACxC,MAAM;IACJC,KAAK;IACLC,OAAO;IACPC,6BAA6B;IAC7BD,OAAO,EAAE;MACPE,SAAS;MACTC,qCAAqC,EAAEC;IACzC;EACF,CAAC,GAAGN,UAAU;EACd,MAAM;IACJO,aAAa;IACbC;EACF,CAAC,GAAG,CAAC,CAAC,EAAEjB,MAAM,CAACkB,kBAAkB,EAAER,KAAK,EAAEG,SAAS,CAAC;EACpD,MAAMM,yBAAyB,GAAG,CAAC,CAAC,EAAEhB,kCAAkC,CAACiB,iCAAiC,EAAE;IAC1GT,OAAO;IACPK,aAAa;IACbJ;EACF,CAAC,CAAC;EACF,MAAM;IACJS,cAAc,EAAEC,kBAAkB;IAClCC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGb,cAAc;EAClB,MAAM;IACJc,QAAQ,GAAG,KAAK;IAChBC,QAAQ,GAAG,KAAK;IAChBC,SAAS,GAAG,KAAK;IACjBC,OAAO,EAAEC,WAAW;IACpBC;EACF,CAAC,GAAGjB,yBAAyB;EAC7B,MAAME,cAAc,GAAG3B,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,oBAAoB,GAAG,CAAC,CAAC,EAAE3C,WAAW,CAACT,OAAO,EAAEoC,kBAAkB,EAAED,cAAc,CAAC;EACzF,MAAMkB,UAAU,GAAG7C,KAAK,CAAC8C,OAAO,CAAC,OAAO;IACtCC,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAACqB,OAAO,IAAI,IAAI;IAC7CC,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAACqB,OAAO,CAACC,OAAO,CAAC,CAAC;IAC/CC,mBAAmB,EAAEC,YAAY,IAAIxB,cAAc,CAACqB,OAAO,CAACE,mBAAmB,CAACC,YAAY,CAAC;IAC7FC,iBAAiB,EAAED,YAAY,IAAIxB,cAAc,CAACqB,OAAO,CAACI,iBAAiB,CAACD,YAAY,CAAC;IACzFE,6BAA6B,EAAEC,OAAO,IAAI3B,cAAc,CAACqB,OAAO,CAACK,6BAA6B,CAACC,OAAO;EACxG,CAAC,CAAC,EAAE,CAAC3B,cAAc,CAAC,CAAC;EACrB,MAAM4B,aAAa,GAAG,CAAC,CAAC,EAAE/C,cAAc,CAACgD,aAAa,EAAE;IACtDvC,OAAO;IACPQ,yBAAyB;IACzBF;EACF,CAAC,CAAC;EACF,MAAM;IACJ;IACAkC,mBAAmB;IACnBC,KAAK;IACLC,sBAAsB;IACtBC,YAAY;IACZC,KAAK;IACLhE,KAAK;IACL;IACAiE,UAAU;IACVC;EACF,CAAC,GAAGR,aAAa;EACjB,MAAMS,qBAAqB,GAAG,CAAC,CAAC,EAAEzD,yBAAyB,CAAC0D,wBAAwB,EAAE;IACpFV;EACF,CAAC,CAAC;EACF,MAAMW,mBAAmB,GAAG7C,4BAA4B,CAACxB,KAAK,CAAC;EAC/D,MAAM,CAAC2C,OAAO,EAAE2B,UAAU,CAAC,GAAGnE,KAAK,CAACoE,QAAQ,CAAC,KAAK,CAAC;EACnD,SAASC,UAAUA,CAACC,mBAAmB,GAAG,CAAC,EAAE;IAC3C,IAAIjC,QAAQ,IAAI,CAACV,cAAc,CAACqB,OAAO;IACvC;IACAuB,qBAAqB,CAAC5C,cAAc,CAAC,IAAI,IAAI,EAAE;MAC7C;IACF;IACA,MAAM6C,yBAAyB,GAAG,CAAC,CAAC,EAAEpE,SAAS,CAACqE,qBAAqB,EAAEH,mBAAmB,EAAET,KAAK,CAACa,QAAQ,CAAC;IAC3GP,UAAU,CAAC,IAAI,CAAC;IAChBxC,cAAc,CAACqB,OAAO,CAACI,iBAAiB,CAACoB,yBAAyB,CAAC,CAACG,KAAK,CAAC,CAAC;EAC7E;EACA,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAEjE,kBAAkB,CAACkE,iBAAiB,EAAE;IAC1D5D,OAAO;IACPQ,yBAAyB;IACzB8B,aAAa;IACbS,qBAAqB;IACrBxB,OAAO;IACP2B,UAAU;IACVtB;EACF,CAAC,CAAC;EACF,MAAMiC,gBAAgB,GAAG,CAAC,CAAC,EAAElE,yBAAyB,CAACmE,wBAAwB,EAAE;IAC/E9D,OAAO;IACPsC;EACF,CAAC,CAAC;EACF,MAAMyB,2BAA2B,GAAG,CAAC,CAAC,EAAEnE,8BAA8B,CAACoE,6BAA6B,EAAE;IACpG1B,aAAa;IACb9B;EACF,CAAC,CAAC;EACF,MAAMyD,yBAAyB,GAAG,CAAC,CAAC,EAAEpE,4BAA4B,CAACqE,2BAA2B,EAAE;IAC9FlE,OAAO;IACPsC,aAAa;IACbS,qBAAqB;IACrBvC,yBAAyB;IACzBoB,UAAU;IACVL;EACF,CAAC,CAAC;EACF,MAAM4C,iBAAiB,GAAG,CAAC,CAAC,EAAElF,iBAAiB,CAACV,OAAO,EAAE6F,KAAK,IAAI;IAChEnD,SAAS,GAAGmD,KAAK,CAAC;IAClBT,SAAS,CAAC1C,SAAS,CAACmD,KAAK,CAAC;EAC5B,CAAC,CAAC;EACF,MAAMC,cAAc,GAAG,CAAC,CAAC,EAAEpF,iBAAiB,CAACV,OAAO,EAAE6F,KAAK,IAAI;IAC7DxD,MAAM,GAAGwD,KAAK,CAAC;IACfT,SAAS,CAAC/C,MAAM,CAACwD,KAAK,CAAC;EACzB,CAAC,CAAC;EACF,MAAME,eAAe,GAAG,CAAC,CAAC,EAAErF,iBAAiB,CAACV,OAAO,EAAE6F,KAAK,IAAI;IAC9DtD,OAAO,GAAGsD,KAAK,CAAC;IAChBT,SAAS,CAAC7C,OAAO,CAACsD,KAAK,CAAC;EAC1B,CAAC,CAAC;EACF,MAAMG,eAAe,GAAG,CAAC,CAAC,EAAEtF,iBAAiB,CAACV,OAAO,EAAE6F,KAAK,IAAI;IAC9D;IACA;IACA,IAAIA,KAAK,CAACI,kBAAkB,CAAC,CAAC,EAAE;MAC9B;IACF;IACA3D,OAAO,GAAGuD,KAAK,CAAC;IAChBT,SAAS,CAAC9C,OAAO,CAACuD,KAAK,CAAC;EAC1B,CAAC,CAAC;EACF,MAAMK,eAAe,GAAG,CAAC,CAAC,EAAExF,iBAAiB,CAACV,OAAO,EAAE6F,KAAK,IAAI;IAC9DpD,OAAO,GAAGoD,KAAK,CAAC;IAChBT,SAAS,CAAC3C,OAAO,CAACoD,KAAK,CAAC;EAC1B,CAAC,CAAC;EACF,MAAMM,eAAe,GAAG,CAAC,CAAC,EAAEzF,iBAAiB,CAACV,OAAO,EAAE6F,KAAK,IAAI;IAC9DrD,OAAO,GAAGqD,KAAK,CAAC;IAChBT,SAAS,CAAC5C,OAAO,CAACqD,KAAK,CAAC;EAC1B,CAAC,CAAC;EACF,MAAMO,WAAW,GAAG,CAAC,CAAC,EAAE1F,iBAAiB,CAACV,OAAO,EAAE,CAAC6F,KAAK,EAAE,GAAGQ,IAAI,KAAK;IACrER,KAAK,CAACS,cAAc,CAAC,CAAC;IACtB3D,OAAO,GAAGkD,KAAK,EAAE,GAAGQ,IAAI,CAAC;IACzB/B,UAAU,CAAC,CAAC;IACZ,IAAI,CAACiC,cAAc,CAACpE,cAAc,CAAC,EAAE;MACnC;MACA0C,UAAU,CAAC,CAAC,CAAC;IACf,CAAC,MAAM;MACLN,mBAAmB,CAACH,YAAY,CAACoC,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGjG,KAAK,CAAC8C,OAAO,CAAC,MAAM;IACnC,OAAOe,KAAK,CAACa,QAAQ,CAACwB,GAAG,CAAC,CAACC,OAAO,EAAEhD,YAAY,KAAK;MACnD,MAAMiD,OAAO,GAAGlB,yBAAyB,CAACiB,OAAO,EAAEhD,YAAY,CAAC;MAChE,OAAO;QACLkD,SAAS,EAAErB,2BAA2B,CAAC7B,YAAY,CAAC;QACpDiD,OAAO,EAAElB,yBAAyB,CAACiB,OAAO,EAAEhD,YAAY,CAAC;QACzDmD,MAAM,EAAE;UACNC,QAAQ,EAAEJ,OAAO,CAACK;QACpB,CAAC;QACDC,KAAK,EAAE;UACLF,QAAQ,EAAEJ,OAAO,CAACO,YAAY;UAC9B,qBAAqB,EAAEP,OAAO,CAACQ,oBAAoB,GAAGP,OAAO,CAAC,qBAAqB,CAAC,GAAGQ;QACzF;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/C,KAAK,CAACa,QAAQ,EAAEM,2BAA2B,EAAEE,yBAAyB,CAAC,CAAC;EAC5ElF,KAAK,CAAC6G,SAAS,CAAC,MAAM;IACpB,IAAIlF,cAAc,CAACqB,OAAO,IAAI,IAAI,EAAE;MAClC,MAAM,IAAI8D,KAAK,CAAC,CAAC,mFAAmF,EAAE,wIAAwI,EAAE,EAAE,EAAE,2KAA2K,EAAE,EAAE,EAAE,mGAAmG,EAAE,EAAE,EAAE,4JAA4J,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzrB;IACA,IAAIxE,SAAS,IAAI,CAACF,QAAQ,IAAIV,cAAc,CAACqB,OAAO,EAAE;MACpDrB,cAAc,CAACqB,OAAO,CAACI,iBAAiB,CAACQ,YAAY,CAACoC,UAAU,CAAC,CAACrB,KAAK,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,CAAC,CAAC,EAAExE,kBAAkB,CAACX,OAAO,EAAE,MAAM;IACpC,IAAI,CAACgD,OAAO,IAAI,CAACb,cAAc,CAACqB,OAAO,EAAE;MACvC;IACF;IACA,IAAIW,sBAAsB,KAAK,KAAK,EAAE;MACpChC,cAAc,CAACqB,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC0B,KAAK,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAI,OAAOhB,sBAAsB,KAAK,QAAQ,EAAE;MACrD,MAAMqD,UAAU,GAAGrF,cAAc,CAACqB,OAAO,CAACI,iBAAiB,CAACO,sBAAsB,CAAC;MACnF,IAAIqD,UAAU,EAAE;QACdA,UAAU,CAACrC,KAAK,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAAChB,sBAAsB,EAAEnB,OAAO,CAAC,CAAC;EACrC,CAAC,CAAC,EAAErC,kBAAkB,CAACX,OAAO,EAAE,MAAM;IACpC,CAAC,CAAC,EAAEkB,mBAAmB,CAACuG,kBAAkB,EAAE;MAC1CzE,OAAO;MACPK,UAAU;MACVU;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFvD,KAAK,CAACkH,mBAAmB,CAACxE,gBAAgB,EAAE,OAAO;IACjDyE,WAAW,EAAEA,CAAA,KAAMtD,KAAK,CAACa,QAAQ;IACjCH,qBAAqB,EAAEA,CAAA,KAAMA,qBAAqB,CAAC5C,cAAc,CAAC;IAClEoC,mBAAmB,EAAEO,mBAAmB,IAAI;MAC1C,IAAIjC,QAAQ,IAAI,CAACV,cAAc,CAACqB,OAAO,EAAE;QACvC;MACF;MACA,MAAMwB,yBAAyB,GAAG,CAAC,CAAC,EAAEpE,SAAS,CAACqE,qBAAqB,EAAEH,mBAAmB,EAAET,KAAK,CAACa,QAAQ,CAAC;MAC3G,MAAM0C,qBAAqB,GAAG5C,yBAAyB,KAAK,KAAK,GAAG,CAAC,GAAGA,yBAAyB;MACjGL,UAAU,CAACiD,qBAAqB,KAAK,IAAI,CAAC;MAC1CrD,mBAAmB,CAACO,mBAAmB,CAAC;IAC1C,CAAC;IACDD,UAAU;IACV0B,cAAc,EAAEA,CAAA,KAAMA,cAAc,CAACpE,cAAc;EACrD,CAAC,CAAC,CAAC;EACH,OAAO,CAAC,CAAC,EAAE5B,SAAS,CAACP,OAAO,EAAE,CAAC,CAAC,EAAE+B,cAAc,EAAEqD,SAAS,EAAE;IAC3D/C,MAAM,EAAEyD,cAAc;IACtBxD,OAAO,EAAE0D,eAAe;IACxBzD,OAAO,EAAEwD,eAAe;IACxBvD,OAAO,EAAE2D,eAAe;IACxB1D,OAAO,EAAEyD,eAAe;IACxBxD,SAAS,EAAEkD,iBAAiB;IAC5BjD,OAAO,EAAEyD;EACX,CAAC,EAAEd,gBAAgB,EAAE;IACnBpB,KAAK;IACLtB,SAAS,EAAEiF,OAAO,CAACjF,SAAS,IAAI,CAACqB,mBAAmB,IAAI,CAACnB,QAAQ,IAAI,CAACD,QAAQ,CAAC;IAC/EG,OAAO,EAAEC,WAAW,IAAID,OAAO;IAC/Bb,cAAc,EAAEiB,oBAAoB;IACpC;IACA0E,iCAAiC,EAAE,IAAI;IACvCrB,QAAQ;IACRxC,mBAAmB;IACnBpB,QAAQ;IACRC,QAAQ;IACRC,SAAS;IACT2B;EACF,CAAC,CAAC;AACJ,CAAC;AACDtE,OAAO,CAACE,mBAAmB,GAAGA,mBAAmB;AACjD,SAASyE,qBAAqBA,CAAC5C,cAAc,EAAE;EAC7C,MAAM4F,aAAa,GAAG,CAAC,CAAC,EAAElH,MAAM,CAACmH,gBAAgB,EAAEC,QAAQ,CAAC;EAC5D,IAAI,CAACF,aAAa,IAAI,CAAC5F,cAAc,CAACqB,OAAO,IAAI,CAACrB,cAAc,CAACqB,OAAO,CAACC,OAAO,CAAC,CAAC,CAACyE,QAAQ,CAACH,aAAa,CAAC,EAAE;IAC1G,OAAO,IAAI;EACb;EACA,OAAO5F,cAAc,CAACqB,OAAO,CAACK,6BAA6B,CAACkE,aAAa,CAAC;AAC5E;AACA,SAASxB,cAAcA,CAACpE,cAAc,EAAE;EACtC,MAAM4F,aAAa,GAAG,CAAC,CAAC,EAAElH,MAAM,CAACmH,gBAAgB,EAAEC,QAAQ,CAAC;EAC5D,OAAO,CAAC,CAAC9F,cAAc,CAACqB,OAAO,IAAIrB,cAAc,CAACqB,OAAO,CAACC,OAAO,CAAC,CAAC,CAACyE,QAAQ,CAACH,aAAa,CAAC;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}