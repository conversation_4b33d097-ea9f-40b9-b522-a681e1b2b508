{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getMinutes = exports.getHours = exports.CLOCK_WIDTH = exports.CLOCK_HOUR_WIDTH = void 0;\nconst CLOCK_WIDTH = exports.CLOCK_WIDTH = 220;\nconst CLOCK_HOUR_WIDTH = exports.CLOCK_HOUR_WIDTH = 36;\nconst clockCenter = {\n  x: CLOCK_WIDTH / 2,\n  y: CLOCK_WIDTH / 2\n};\nconst baseClockPoint = {\n  x: clockCenter.x,\n  y: 0\n};\nconst cx = baseClockPoint.x - clockCenter.x;\nconst cy = baseClockPoint.y - clockCenter.y;\nconst rad2deg = rad => rad * (180 / Math.PI);\nconst getAngleValue = (step, offsetX, offsetY) => {\n  const x = offsetX - clockCenter.x;\n  const y = offsetY - clockCenter.y;\n  const atan = Math.atan2(cx, cy) - Math.atan2(x, y);\n  let deg = rad2deg(atan);\n  deg = Math.round(deg / step) * step;\n  deg %= 360;\n  const value = Math.floor(deg / step) || 0;\n  const delta = x ** 2 + y ** 2;\n  const distance = Math.sqrt(delta);\n  return {\n    value,\n    distance\n  };\n};\nconst getMinutes = (offsetX, offsetY, step = 1) => {\n  const angleStep = step * 6;\n  let {\n    value\n  } = getAngleValue(angleStep, offsetX, offsetY);\n  value = value * step % 60;\n  return value;\n};\nexports.getMinutes = getMinutes;\nconst getHours = (offsetX, offsetY, ampm) => {\n  const {\n    value,\n    distance\n  } = getAngleValue(30, offsetX, offsetY);\n  let hour = value || 12;\n  if (!ampm) {\n    if (distance < CLOCK_WIDTH / 2 - CLOCK_HOUR_WIDTH) {\n      hour += 12;\n      hour %= 24;\n    }\n  } else {\n    hour %= 12;\n  }\n  return hour;\n};\nexports.getHours = getHours;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getMinutes", "getHours", "CLOCK_WIDTH", "CLOCK_HOUR_WIDTH", "clockCenter", "x", "y", "baseClockPoint", "cx", "cy", "rad2deg", "rad", "Math", "PI", "getAngleValue", "step", "offsetX", "offsetY", "atan", "atan2", "deg", "round", "floor", "delta", "distance", "sqrt", "angleStep", "ampm", "hour"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/TimeClock/shared.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getMinutes = exports.getHours = exports.CLOCK_WIDTH = exports.CLOCK_HOUR_WIDTH = void 0;\nconst CLOCK_WIDTH = exports.CLOCK_WIDTH = 220;\nconst CLOCK_HOUR_WIDTH = exports.CLOCK_HOUR_WIDTH = 36;\nconst clockCenter = {\n  x: CLOCK_WIDTH / 2,\n  y: CLOCK_WIDTH / 2\n};\nconst baseClockPoint = {\n  x: clockCenter.x,\n  y: 0\n};\nconst cx = baseClockPoint.x - clockCenter.x;\nconst cy = baseClockPoint.y - clockCenter.y;\nconst rad2deg = rad => rad * (180 / Math.PI);\nconst getAngleValue = (step, offsetX, offsetY) => {\n  const x = offsetX - clockCenter.x;\n  const y = offsetY - clockCenter.y;\n  const atan = Math.atan2(cx, cy) - Math.atan2(x, y);\n  let deg = rad2deg(atan);\n  deg = Math.round(deg / step) * step;\n  deg %= 360;\n  const value = Math.floor(deg / step) || 0;\n  const delta = x ** 2 + y ** 2;\n  const distance = Math.sqrt(delta);\n  return {\n    value,\n    distance\n  };\n};\nconst getMinutes = (offsetX, offsetY, step = 1) => {\n  const angleStep = step * 6;\n  let {\n    value\n  } = getAngleValue(angleStep, offsetX, offsetY);\n  value = value * step % 60;\n  return value;\n};\nexports.getMinutes = getMinutes;\nconst getHours = (offsetX, offsetY, ampm) => {\n  const {\n    value,\n    distance\n  } = getAngleValue(30, offsetX, offsetY);\n  let hour = value || 12;\n  if (!ampm) {\n    if (distance < CLOCK_WIDTH / 2 - CLOCK_HOUR_WIDTH) {\n      hour += 12;\n      hour %= 24;\n    }\n  } else {\n    hour %= 12;\n  }\n  return hour;\n};\nexports.getHours = getHours;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGF,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACI,WAAW,GAAGJ,OAAO,CAACK,gBAAgB,GAAG,KAAK,CAAC;AAC/F,MAAMD,WAAW,GAAGJ,OAAO,CAACI,WAAW,GAAG,GAAG;AAC7C,MAAMC,gBAAgB,GAAGL,OAAO,CAACK,gBAAgB,GAAG,EAAE;AACtD,MAAMC,WAAW,GAAG;EAClBC,CAAC,EAAEH,WAAW,GAAG,CAAC;EAClBI,CAAC,EAAEJ,WAAW,GAAG;AACnB,CAAC;AACD,MAAMK,cAAc,GAAG;EACrBF,CAAC,EAAED,WAAW,CAACC,CAAC;EAChBC,CAAC,EAAE;AACL,CAAC;AACD,MAAME,EAAE,GAAGD,cAAc,CAACF,CAAC,GAAGD,WAAW,CAACC,CAAC;AAC3C,MAAMI,EAAE,GAAGF,cAAc,CAACD,CAAC,GAAGF,WAAW,CAACE,CAAC;AAC3C,MAAMI,OAAO,GAAGC,GAAG,IAAIA,GAAG,IAAI,GAAG,GAAGC,IAAI,CAACC,EAAE,CAAC;AAC5C,MAAMC,aAAa,GAAGA,CAACC,IAAI,EAAEC,OAAO,EAAEC,OAAO,KAAK;EAChD,MAAMZ,CAAC,GAAGW,OAAO,GAAGZ,WAAW,CAACC,CAAC;EACjC,MAAMC,CAAC,GAAGW,OAAO,GAAGb,WAAW,CAACE,CAAC;EACjC,MAAMY,IAAI,GAAGN,IAAI,CAACO,KAAK,CAACX,EAAE,EAAEC,EAAE,CAAC,GAAGG,IAAI,CAACO,KAAK,CAACd,CAAC,EAAEC,CAAC,CAAC;EAClD,IAAIc,GAAG,GAAGV,OAAO,CAACQ,IAAI,CAAC;EACvBE,GAAG,GAAGR,IAAI,CAACS,KAAK,CAACD,GAAG,GAAGL,IAAI,CAAC,GAAGA,IAAI;EACnCK,GAAG,IAAI,GAAG;EACV,MAAMrB,KAAK,GAAGa,IAAI,CAACU,KAAK,CAACF,GAAG,GAAGL,IAAI,CAAC,IAAI,CAAC;EACzC,MAAMQ,KAAK,GAAGlB,CAAC,IAAI,CAAC,GAAGC,CAAC,IAAI,CAAC;EAC7B,MAAMkB,QAAQ,GAAGZ,IAAI,CAACa,IAAI,CAACF,KAAK,CAAC;EACjC,OAAO;IACLxB,KAAK;IACLyB;EACF,CAAC;AACH,CAAC;AACD,MAAMxB,UAAU,GAAGA,CAACgB,OAAO,EAAEC,OAAO,EAAEF,IAAI,GAAG,CAAC,KAAK;EACjD,MAAMW,SAAS,GAAGX,IAAI,GAAG,CAAC;EAC1B,IAAI;IACFhB;EACF,CAAC,GAAGe,aAAa,CAACY,SAAS,EAAEV,OAAO,EAAEC,OAAO,CAAC;EAC9ClB,KAAK,GAAGA,KAAK,GAAGgB,IAAI,GAAG,EAAE;EACzB,OAAOhB,KAAK;AACd,CAAC;AACDD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/B,MAAMC,QAAQ,GAAGA,CAACe,OAAO,EAAEC,OAAO,EAAEU,IAAI,KAAK;EAC3C,MAAM;IACJ5B,KAAK;IACLyB;EACF,CAAC,GAAGV,aAAa,CAAC,EAAE,EAAEE,OAAO,EAAEC,OAAO,CAAC;EACvC,IAAIW,IAAI,GAAG7B,KAAK,IAAI,EAAE;EACtB,IAAI,CAAC4B,IAAI,EAAE;IACT,IAAIH,QAAQ,GAAGtB,WAAW,GAAG,CAAC,GAAGC,gBAAgB,EAAE;MACjDyB,IAAI,IAAI,EAAE;MACVA,IAAI,IAAI,EAAE;IACZ;EACF,CAAC,MAAM;IACLA,IAAI,IAAI,EAAE;EACZ;EACA,OAAOA,IAAI;AACb,CAAC;AACD9B,OAAO,CAACG,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}