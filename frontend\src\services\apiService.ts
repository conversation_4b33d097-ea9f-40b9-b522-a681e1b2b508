import axios, { AxiosResponse } from 'axios';

// API base URL - will use proxy in development
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      console.error(`API Error ${status}:`, data);
    } else if (error.request) {
      // Request was made but no response received
      console.error('No response received:', error.request);
    } else {
      // Something else happened
      console.error('Request setup error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// API service interface
export interface TransactionFilters {
  symbol?: string;
  person_name?: string;
  person_category?: string;
  transaction_type?: string;
  from_date?: string;
  to_date?: string;
  min_value?: number;
  max_value?: number;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface Transaction {
  id: string;
  symbol: string;
  company_name: string;
  person_name: string;
  person_category?: string;
  transaction_date: string;
  intimation_date?: string;
  transaction_type?: string;
  transaction_mode?: string;
  security_type?: string;
  exchange_name?: string;
  buy_value?: number;
  sell_value?: number;
  buy_quantity?: number;
  sell_quantity?: number;
  security_value?: number;
  securities_acquired?: number;
  shares_before_transaction?: number;
  percentage_before_transaction?: number;
  shares_after_transaction?: number;
  percentage_after_transaction?: number;
  remarks?: string;
  xbrl_link?: string;
  created_at: string;
}

export interface TransactionListResponse {
  transactions: Transaction[];
  total_count: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface AnalyticsSummary {
  total_transactions: number;
  total_buy_value: number;
  total_sell_value: number;
  unique_companies: number;
  unique_persons: number;
  date_range: {
    earliest: string;
    latest: string;
  };
}

export interface CompanyActivity {
  symbol: string;
  company_name: string;
  transaction_count: number;
  total_value: number;
  total_buy_value: number;
  total_sell_value: number;
  unique_insiders: number;
  latest_transaction_date: string;
}

export interface SearchResult {
  type: string;
  id?: string;
  symbol?: string;
  name: string;
  category?: string;
  transaction_count?: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total_count: number;
  query: string;
  search_type: string;
}

export interface SystemStatus {
  api_status: string;
  database_status: {
    total_transactions: number;
    total_companies: number;
    date_range?: {
      earliest: string;
      latest: string;
    };
    last_updated?: string;
  };
  scraper_status: {
    last_execution?: string;
    status: string;
    records_fetched: number;
    records_inserted: number;
    records_skipped: number;
    error_message?: string;
  };
  timestamp: string;
}

export interface ManualScrapeResponse {
  status: 'success' | 'error';
  message: string;
  records_fetched?: number;
  records_inserted?: number;
  records_skipped?: number;
  execution_time?: number;
  timestamp: string;
}

// API service class
class ApiService {
  // Health check
  async healthCheck(): Promise<AxiosResponse<any>> {
    return api.get('/health');
  }

  // Transactions
  async getTransactions(filters: TransactionFilters = {}): Promise<AxiosResponse<TransactionListResponse>> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    return api.get(`/transactions?${params.toString()}`);
  }

  // Analytics
  async getAnalyticsSummary(fromDate?: string, toDate?: string): Promise<AxiosResponse<AnalyticsSummary>> {
    const params = new URLSearchParams();
    if (fromDate) params.append('from_date', fromDate);
    if (toDate) params.append('to_date', toDate);
    
    return api.get(`/analytics/summary?${params.toString()}`);
  }

  async getTopCompanies(options: {
    limit?: number;
    sort_by?: string;
    from_date?: string;
    to_date?: string;
  } = {}): Promise<AxiosResponse<CompanyActivity[]>> {
    const params = new URLSearchParams();
    
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    return api.get(`/analytics/top-companies?${params.toString()}`);
  }

  // Companies
  async getCompanyTransactions(
    symbol: string,
    options: {
      from_date?: string;
      to_date?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<AxiosResponse<TransactionListResponse>> {
    const params = new URLSearchParams();
    
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    return api.get(`/companies/${symbol}/transactions?${params.toString()}`);
  }

  // Search
  async search(
    query: string,
    type: 'all' | 'company' | 'person' = 'all',
    limit: number = 10
  ): Promise<AxiosResponse<SearchResponse>> {
    const params = new URLSearchParams({
      q: query,
      type,
      limit: limit.toString(),
    });

    return api.get(`/search?${params.toString()}`);
  }

  // System status
  async getSystemStatus(): Promise<AxiosResponse<SystemStatus>> {
    return api.get('/system/status');
  }

  // Manual scraping
  async triggerManualScrape(daysBack: number = 7): Promise<AxiosResponse<ManualScrapeResponse>> {
    return api.post(`/system/scrape?days_back=${daysBack}`);
  }

  // Utility method to format API errors
  formatError(error: any): string {
    if (error.response?.data?.detail) {
      return error.response.data.detail;
    } else if (error.response?.data?.message) {
      return error.response.data.message;
    } else if (error.message) {
      return error.message;
    } else {
      return 'An unexpected error occurred';
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
