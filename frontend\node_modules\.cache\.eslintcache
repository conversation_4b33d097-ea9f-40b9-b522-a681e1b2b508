[{"D:\\chirag\\nsescrapper\\frontend\\src\\index.tsx": "1", "D:\\chirag\\nsescrapper\\frontend\\src\\reportWebVitals.ts": "2", "D:\\chirag\\nsescrapper\\frontend\\src\\App.tsx": "3", "D:\\chirag\\nsescrapper\\frontend\\src\\components\\Layout.tsx": "4", "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Dashboard.tsx": "5", "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Analytics.tsx": "6", "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Transactions.tsx": "7", "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Companies.tsx": "8", "D:\\chirag\\nsescrapper\\frontend\\src\\components\\RecentTransactions.tsx": "9", "D:\\chirag\\nsescrapper\\frontend\\src\\components\\TopCompaniesChart.tsx": "10", "D:\\chirag\\nsescrapper\\frontend\\src\\services\\apiService.ts": "11", "D:\\chirag\\nsescrapper\\frontend\\src\\components\\StatCard.tsx": "12", "D:\\chirag\\nsescrapper\\frontend\\src\\components\\TransactionTrendsChart.tsx": "13", "D:\\chirag\\nsescrapper\\frontend\\src\\components\\LastFetchedStatus.tsx": "14"}, {"size": 554, "mtime": 1750171884532, "results": "15", "hashOfConfig": "16"}, {"size": 425, "mtime": 1750171881266, "results": "17", "hashOfConfig": "16"}, {"size": 1981, "mtime": 1750177762067, "results": "18", "hashOfConfig": "16"}, {"size": 5432, "mtime": 1750179804560, "results": "19", "hashOfConfig": "16"}, {"size": 6856, "mtime": 1750179855831, "results": "20", "hashOfConfig": "16"}, {"size": 6020, "mtime": 1750175200196, "results": "21", "hashOfConfig": "16"}, {"size": 11268, "mtime": 1750179155136, "results": "22", "hashOfConfig": "16"}, {"size": 8246, "mtime": 1750175281481, "results": "23", "hashOfConfig": "16"}, {"size": 6563, "mtime": 1750172553022, "results": "24", "hashOfConfig": "16"}, {"size": 4632, "mtime": 1750172580585, "results": "25", "hashOfConfig": "16"}, {"size": 6594, "mtime": 1750172475806, "results": "26", "hashOfConfig": "16"}, {"size": 3759, "mtime": 1750172527938, "results": "27", "hashOfConfig": "16"}, {"size": 7090, "mtime": 1750172611338, "results": "28", "hashOfConfig": "16"}, {"size": 5930, "mtime": 1750180056722, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1fqi<PERSON>jk", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\chirag\\nsescrapper\\frontend\\src\\index.tsx", [], [], "D:\\chirag\\nsescrapper\\frontend\\src\\reportWebVitals.ts", [], [], "D:\\chirag\\nsescrapper\\frontend\\src\\App.tsx", [], [], "D:\\chirag\\nsescrapper\\frontend\\src\\components\\Layout.tsx", [], [], "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Dashboard.tsx", ["72", "73", "74", "75"], [], "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Analytics.tsx", ["76"], [], "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Transactions.tsx", ["77", "78"], [], "D:\\chirag\\nsescrapper\\frontend\\src\\pages\\Companies.tsx", ["79"], [], "D:\\chirag\\nsescrapper\\frontend\\src\\components\\RecentTransactions.tsx", ["80"], [], "D:\\chirag\\nsescrapper\\frontend\\src\\components\\TopCompaniesChart.tsx", ["81"], [], "D:\\chirag\\nsescrapper\\frontend\\src\\services\\apiService.ts", [], [], "D:\\chirag\\nsescrapper\\frontend\\src\\components\\StatCard.tsx", [], [], "D:\\chirag\\nsescrapper\\frontend\\src\\components\\TransactionTrendsChart.tsx", ["82"], [], "D:\\chirag\\nsescrapper\\frontend\\src\\components\\LastFetchedStatus.tsx", ["83"], [], {"ruleId": "84", "severity": 1, "message": "85", "line": 10, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 10, "endColumn": 7}, {"ruleId": "84", "severity": 1, "message": "88", "line": 16, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 16, "endColumn": 9}, {"ruleId": "84", "severity": 1, "message": "89", "line": 19, "column": 10, "nodeType": "86", "messageId": "87", "endLine": 19, "endColumn": 16}, {"ruleId": "84", "severity": 1, "message": "90", "line": 128, "column": 56, "nodeType": "86", "messageId": "87", "endLine": 128, "endColumn": 69}, {"ruleId": "84", "severity": 1, "message": "88", "line": 19, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 19, "endColumn": 9}, {"ruleId": "84", "severity": 1, "message": "91", "line": 12, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 12, "endColumn": 19}, {"ruleId": "84", "severity": 1, "message": "92", "line": 16, "column": 25, "nodeType": "86", "messageId": "87", "endLine": 16, "endColumn": 33}, {"ruleId": "84", "severity": 1, "message": "93", "line": 9, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 9, "endColumn": 9}, {"ruleId": "84", "severity": 1, "message": "94", "line": 13, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 13, "endColumn": 7}, {"ruleId": "84", "severity": 1, "message": "95", "line": 32, "column": 9, "nodeType": "86", "messageId": "87", "endLine": 32, "endColumn": 27}, {"ruleId": "84", "severity": 1, "message": "96", "line": 13, "column": 27, "nodeType": "86", "messageId": "87", "endLine": 13, "endColumn": 37}, {"ruleId": "84", "severity": 1, "message": "97", "line": 145, "column": 9, "nodeType": "86", "messageId": "87", "endLine": 145, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'Chip' is defined but never used.", "Identifier", "unusedVar", "'Person' is defined but never used.", "'format' is defined but never used.", "'system_status' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'Download' is defined but never used.", "'Button' is defined but never used.", "'Link' is defined but never used.", "'formatTooltipValue' is assigned a value but never used.", "'startOfDay' is defined but never used.", "'statusColor' is assigned a value but never used."]