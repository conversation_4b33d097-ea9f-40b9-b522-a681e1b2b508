{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.slowAnimationDevices = void 0;\nexports.useReduceAnimations = useReduceAnimations;\nvar _useMediaQuery = _interopRequireDefault(require(\"@mui/material/useMediaQuery\"));\nconst PREFERS_REDUCED_MOTION = '@media (prefers-reduced-motion: reduce)';\n\n// detect if user agent has Android version < 10 or iOS version < 13\nconst mobileVersionMatches = typeof navigator !== 'undefined' && navigator.userAgent.match(/android\\s(\\d+)|OS\\s(\\d+)/i);\nconst androidVersion = mobileVersionMatches && mobileVersionMatches[1] ? parseInt(mobileVersionMatches[1], 10) : null;\nconst iOSVersion = mobileVersionMatches && mobileVersionMatches[2] ? parseInt(mobileVersionMatches[2], 10) : null;\nconst slowAnimationDevices = exports.slowAnimationDevices = androidVersion && androidVersion < 10 || iOSVersion && iOSVersion < 13 || false;\nfunction useReduceAnimations(customReduceAnimations) {\n  const prefersReduced = (0, _useMediaQuery.default)(PREFERS_REDUCED_MOTION, {\n    defaultMatches: false\n  });\n  if (customReduceAnimations != null) {\n    return customReduceAnimations;\n  }\n  return prefersReduced || slowAnimationDevices;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "slowAnimationDevices", "useReduceAnimations", "_useMediaQuery", "PREFERS_REDUCED_MOTION", "mobileVersionMatches", "navigator", "userAgent", "match", "androidVersion", "parseInt", "iOSVersion", "customReduceAnimations", "prefersReduced", "defaultMatches"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useReduceAnimations.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.slowAnimationDevices = void 0;\nexports.useReduceAnimations = useReduceAnimations;\nvar _useMediaQuery = _interopRequireDefault(require(\"@mui/material/useMediaQuery\"));\nconst PREFERS_REDUCED_MOTION = '@media (prefers-reduced-motion: reduce)';\n\n// detect if user agent has Android version < 10 or iOS version < 13\nconst mobileVersionMatches = typeof navigator !== 'undefined' && navigator.userAgent.match(/android\\s(\\d+)|OS\\s(\\d+)/i);\nconst androidVersion = mobileVersionMatches && mobileVersionMatches[1] ? parseInt(mobileVersionMatches[1], 10) : null;\nconst iOSVersion = mobileVersionMatches && mobileVersionMatches[2] ? parseInt(mobileVersionMatches[2], 10) : null;\nconst slowAnimationDevices = exports.slowAnimationDevices = androidVersion && androidVersion < 10 || iOSVersion && iOSVersion < 13 || false;\nfunction useReduceAnimations(customReduceAnimations) {\n  const prefersReduced = (0, _useMediaQuery.default)(PREFERS_REDUCED_MOTION, {\n    defaultMatches: false\n  });\n  if (customReduceAnimations != null) {\n    return customReduceAnimations;\n  }\n  return prefersReduced || slowAnimationDevices;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,oBAAoB,GAAG,KAAK,CAAC;AACrCF,OAAO,CAACG,mBAAmB,GAAGA,mBAAmB;AACjD,IAAIC,cAAc,GAAGT,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACnF,MAAMS,sBAAsB,GAAG,yCAAyC;;AAExE;AACA,MAAMC,oBAAoB,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,2BAA2B,CAAC;AACvH,MAAMC,cAAc,GAAGJ,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,GAAGK,QAAQ,CAACL,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;AACrH,MAAMM,UAAU,GAAGN,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,GAAGK,QAAQ,CAACL,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;AACjH,MAAMJ,oBAAoB,GAAGF,OAAO,CAACE,oBAAoB,GAAGQ,cAAc,IAAIA,cAAc,GAAG,EAAE,IAAIE,UAAU,IAAIA,UAAU,GAAG,EAAE,IAAI,KAAK;AAC3I,SAAST,mBAAmBA,CAACU,sBAAsB,EAAE;EACnD,MAAMC,cAAc,GAAG,CAAC,CAAC,EAAEV,cAAc,CAACP,OAAO,EAAEQ,sBAAsB,EAAE;IACzEU,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,IAAIF,sBAAsB,IAAI,IAAI,EAAE;IAClC,OAAOA,sBAAsB;EAC/B;EACA,OAAOC,cAAc,IAAIZ,oBAAoB;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}