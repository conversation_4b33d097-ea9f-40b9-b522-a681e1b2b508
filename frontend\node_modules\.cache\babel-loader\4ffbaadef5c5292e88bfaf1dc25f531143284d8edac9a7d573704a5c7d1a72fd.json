{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useControlledValue = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _useUtils = require(\"./useUtils\");\n/**\n * Hooks controlling the value while making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nconst useControlledValue = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const utils = (0, _useUtils.useUtils)();\n  const [valueWithInputTimezone, setValue] = (0, _useControlled.default)({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, valueWithInputTimezone), [utils, valueManager, valueWithInputTimezone]);\n  const setInputTimezone = (0, _useEventCallback.default)(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = React.useMemo(() => {\n    if (timezoneProp) {\n      return timezoneProp;\n    }\n    if (inputTimezone) {\n      return inputTimezone;\n    }\n    if (referenceDate) {\n      return utils.getTimezone(referenceDate);\n    }\n    return 'default';\n  }, [timezoneProp, inputTimezone, referenceDate, utils]);\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, valueWithInputTimezone), [valueManager, utils, timezoneToRender, valueWithInputTimezone]);\n  const handleValueChange = (0, _useEventCallback.default)((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    setValue(newValueWithInputTimezone);\n    onChangeProp?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\nexports.useControlledValue = useControlledValue;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "useControlledValue", "React", "_useEventCallback", "_useControlled", "_useUtils", "name", "timezone", "timezoneProp", "valueProp", "defaultValue", "referenceDate", "onChange", "onChangeProp", "valueManager", "utils", "useUtils", "valueWithInputTimezone", "setValue", "state", "controlled", "emptyValue", "inputTimezone", "useMemo", "getTimezone", "setInputTimezone", "newValue", "setTimezone", "timezoneToRender", "valueWithTimezoneToRender", "handleValueChange", "otherParams", "newValueWithInputTimezone"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useControlledValue.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.useControlledValue = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _useEventCallback = _interopRequireDefault(require(\"@mui/utils/useEventCallback\"));\nvar _useControlled = _interopRequireDefault(require(\"@mui/utils/useControlled\"));\nvar _useUtils = require(\"./useUtils\");\n/**\n * Hooks controlling the value while making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nconst useControlledValue = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const utils = (0, _useUtils.useUtils)();\n  const [valueWithInputTimezone, setValue] = (0, _useControlled.default)({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, valueWithInputTimezone), [utils, valueManager, valueWithInputTimezone]);\n  const setInputTimezone = (0, _useEventCallback.default)(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = React.useMemo(() => {\n    if (timezoneProp) {\n      return timezoneProp;\n    }\n    if (inputTimezone) {\n      return inputTimezone;\n    }\n    if (referenceDate) {\n      return utils.getTimezone(referenceDate);\n    }\n    return 'default';\n  }, [timezoneProp, inputTimezone, referenceDate, utils]);\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, valueWithInputTimezone), [valueManager, utils, timezoneToRender, valueWithInputTimezone]);\n  const handleValueChange = (0, _useEventCallback.default)((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    setValue(newValueWithInputTimezone);\n    onChangeProp?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\nexports.useControlledValue = useControlledValue;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACnC,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,iBAAiB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtF,IAAIU,cAAc,GAAGX,sBAAsB,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChF,IAAIW,SAAS,GAAGX,OAAO,CAAC,YAAY,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,MAAMO,kBAAkB,GAAGA,CAAC;EAC1BK,IAAI;EACJC,QAAQ,EAAEC,YAAY;EACtBR,KAAK,EAAES,SAAS;EAChBC,YAAY;EACZC,aAAa;EACbC,QAAQ,EAAEC,YAAY;EACtBC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEV,SAAS,CAACW,QAAQ,EAAE,CAAC;EACvC,MAAM,CAACC,sBAAsB,EAAEC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAEd,cAAc,CAACT,OAAO,EAAE;IACrEW,IAAI;IACJa,KAAK,EAAE,OAAO;IACdC,UAAU,EAAEX,SAAS;IACrBd,OAAO,EAAEe,YAAY,IAAII,YAAY,CAACO;EACxC,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGpB,KAAK,CAACqB,OAAO,CAAC,MAAMT,YAAY,CAACU,WAAW,CAACT,KAAK,EAAEE,sBAAsB,CAAC,EAAE,CAACF,KAAK,EAAED,YAAY,EAAEG,sBAAsB,CAAC,CAAC;EACjJ,MAAMQ,gBAAgB,GAAG,CAAC,CAAC,EAAEtB,iBAAiB,CAACR,OAAO,EAAE+B,QAAQ,IAAI;IAClE,IAAIJ,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOI,QAAQ;IACjB;IACA,OAAOZ,YAAY,CAACa,WAAW,CAACZ,KAAK,EAAEO,aAAa,EAAEI,QAAQ,CAAC;EACjE,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG1B,KAAK,CAACqB,OAAO,CAAC,MAAM;IAC3C,IAAIf,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;IACA,IAAIc,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;IACA,IAAIX,aAAa,EAAE;MACjB,OAAOI,KAAK,CAACS,WAAW,CAACb,aAAa,CAAC;IACzC;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAACH,YAAY,EAAEc,aAAa,EAAEX,aAAa,EAAEI,KAAK,CAAC,CAAC;EACvD,MAAMc,yBAAyB,GAAG3B,KAAK,CAACqB,OAAO,CAAC,MAAMT,YAAY,CAACa,WAAW,CAACZ,KAAK,EAAEa,gBAAgB,EAAEX,sBAAsB,CAAC,EAAE,CAACH,YAAY,EAAEC,KAAK,EAAEa,gBAAgB,EAAEX,sBAAsB,CAAC,CAAC;EACjM,MAAMa,iBAAiB,GAAG,CAAC,CAAC,EAAE3B,iBAAiB,CAACR,OAAO,EAAE,CAAC+B,QAAQ,EAAE,GAAGK,WAAW,KAAK;IACrF,MAAMC,yBAAyB,GAAGP,gBAAgB,CAACC,QAAQ,CAAC;IAC5DR,QAAQ,CAACc,yBAAyB,CAAC;IACnCnB,YAAY,GAAGmB,yBAAyB,EAAE,GAAGD,WAAW,CAAC;EAC3D,CAAC,CAAC;EACF,OAAO;IACL/B,KAAK,EAAE6B,yBAAyB;IAChCC,iBAAiB;IACjBvB,QAAQ,EAAEqB;EACZ,CAAC;AACH,CAAC;AACD7B,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}